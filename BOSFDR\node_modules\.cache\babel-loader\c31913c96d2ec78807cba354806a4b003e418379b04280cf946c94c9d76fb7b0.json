{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'ClientSidebar',\n  props: {\n    collapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    pendingRequests: {\n      type: Number,\n      default: 0\n    },\n    completedRequests: {\n      type: Number,\n      default: 0\n    },\n    totalServices: {\n      type: Number,\n      default: 8\n    }\n  },\n  emits: ['menu-change', 'logout', 'toggle-sidebar'],\n  computed: {\n    // Detect if the current device is mobile\n    isMobile() {\n      return window.innerWidth <= 768;\n    }\n  },\n  mounted() {\n    // Listen for window resize to update mobile detection\n    this.handleResize = () => {\n      this.$forceUpdate(); // Force re-render to update isMobile computed property\n    };\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeUnmount() {\n    // Clean up event listener\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n  methods: {\n    // Handle image error\n    handleImageError(event) {\n      console.log('Logo image failed to load, using fallback');\n      // You could set a fallback image here if needed\n      event.target.style.display = 'none';\n    },\n    // Handle menu item click\n    handleMenuClick(menu) {\n      // Prevent default link behavior\n      event.preventDefault();\n\n      // Handle special navigation cases\n      if (menu === 'services') {\n        // Navigate to new document request page\n        this.$router.push({\n          name: 'NewDocumentRequest'\n        });\n      } else if (menu === 'requests') {\n        // Navigate to my requests page\n        this.$router.push({\n          name: 'MyRequests'\n        });\n      } else {\n        // Emit menu change event to parent for other menus\n        this.$emit('menu-change', menu);\n      }\n    },\n    // Handle logout\n    handleLogout() {\n      // Prevent default link behavior\n      event.preventDefault();\n\n      // Emit logout event to parent\n      this.$emit('logout');\n    },\n    // Handle mobile close button\n    handleMobileClose() {\n      // Emit toggle sidebar event to close the sidebar on mobile\n      this.$emit('toggle-sidebar');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "collapsed", "type", "Boolean", "default", "activeMenu", "String", "pendingRequests", "Number", "completedRequests", "totalServices", "emits", "computed", "isMobile", "window", "innerWidth", "mounted", "handleResize", "$forceUpdate", "addEventListener", "beforeUnmount", "removeEventListener", "methods", "handleImageError", "event", "console", "log", "target", "style", "display", "handleMenuClick", "menu", "preventDefault", "$router", "push", "$emit", "handleLogout", "handleMobileClose"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/components/client/js/clientSidebar.js"], "sourcesContent": ["export default {\n  name: 'Client<PERSON><PERSON><PERSON>',\n  props: {\n    collapsed: {\n      type: <PERSON><PERSON>an,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    },\n    pendingRequests: {\n      type: Number,\n      default: 0\n    },\n    completedRequests: {\n      type: Number,\n      default: 0\n    },\n    totalServices: {\n      type: Number,\n      default: 8\n    }\n  },\n\n  emits: ['menu-change', 'logout', 'toggle-sidebar'],\n\n  computed: {\n    // Detect if the current device is mobile\n    isMobile() {\n      return window.innerWidth <= 768;\n    }\n  },\n\n  mounted() {\n    // Listen for window resize to update mobile detection\n    this.handleResize = () => {\n      this.$forceUpdate(); // Force re-render to update isMobile computed property\n    };\n    window.addEventListener('resize', this.handleResize);\n  },\n\n  beforeUnmount() {\n    // Clean up event listener\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n\n  methods: {\n    // Handle image error\n    handleImageError(event) {\n      console.log('Logo image failed to load, using fallback');\n      // You could set a fallback image here if needed\n      event.target.style.display = 'none';\n    },\n\n    // Handle menu item click\n    handleMenuClick(menu) {\n      // Prevent default link behavior\n      event.preventDefault();\n\n      // Handle special navigation cases\n      if (menu === 'services') {\n        // Navigate to new document request page\n        this.$router.push({ name: 'NewDocumentRequest' });\n      } else if (menu === 'requests') {\n        // Navigate to my requests page\n        this.$router.push({ name: 'MyRequests' });\n      } else {\n        // Emit menu change event to parent for other menus\n        this.$emit('menu-change', menu);\n      }\n    },\n\n    // Handle logout\n    handleLogout() {\n      // Prevent default link behavior\n      event.preventDefault();\n\n      // Emit logout event to parent\n      this.$emit('logout');\n    },\n\n    // Handle mobile close button\n    handleMobileClose() {\n      // Emit toggle sidebar event to close the sidebar on mobile\n      this.$emit('toggle-sidebar');\n    }\n  }\n};\n"], "mappings": ";AAAA,eAAe;EACbA,IAAI,EAAE,eAAe;EACrBC,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,UAAU,EAAE;MACVH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDG,eAAe,EAAE;MACfL,IAAI,EAAEM,MAAM;MACZJ,OAAO,EAAE;IACX,CAAC;IACDK,iBAAiB,EAAE;MACjBP,IAAI,EAAEM,MAAM;MACZJ,OAAO,EAAE;IACX,CAAC;IACDM,aAAa,EAAE;MACbR,IAAI,EAAEM,MAAM;MACZJ,OAAO,EAAE;IACX;EACF,CAAC;EAEDO,KAAK,EAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,gBAAgB,CAAC;EAElDC,QAAQ,EAAE;IACR;IACAC,QAAQA,CAAA,EAAG;MACT,OAAOC,MAAM,CAACC,UAAU,IAAI,GAAG;IACjC;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAACC,YAAY,GAAG,MAAM;MACxB,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACDJ,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACF,YAAY,CAAC;EACtD,CAAC;EAEDG,aAAaA,CAAA,EAAG;IACd;IACA,IAAI,IAAI,CAACH,YAAY,EAAE;MACrBH,MAAM,CAACO,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACJ,YAAY,CAAC;IACzD;EACF,CAAC;EAEDK,OAAO,EAAE;IACP;IACAC,gBAAgBA,CAACC,KAAK,EAAE;MACtBC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;MACAF,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;IACrC,CAAC;IAED;IACAC,eAAeA,CAACC,IAAI,EAAE;MACpB;MACAP,KAAK,CAACQ,cAAc,CAAC,CAAC;;MAEtB;MACA,IAAID,IAAI,KAAK,UAAU,EAAE;QACvB;QACA,IAAI,CAACE,OAAO,CAACC,IAAI,CAAC;UAAEnC,IAAI,EAAE;QAAqB,CAAC,CAAC;MACnD,CAAC,MAAM,IAAIgC,IAAI,KAAK,UAAU,EAAE;QAC9B;QACA,IAAI,CAACE,OAAO,CAACC,IAAI,CAAC;UAAEnC,IAAI,EAAE;QAAa,CAAC,CAAC;MAC3C,CAAC,MAAM;QACL;QACA,IAAI,CAACoC,KAAK,CAAC,aAAa,EAAEJ,IAAI,CAAC;MACjC;IACF,CAAC;IAED;IACAK,YAAYA,CAAA,EAAG;MACb;MACAZ,KAAK,CAACQ,cAAc,CAAC,CAAC;;MAEtB;MACA,IAAI,CAACG,KAAK,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED;IACAE,iBAAiBA,CAAA,EAAG;MAClB;MACA,IAAI,CAACF,KAAK,CAAC,gBAAgB,CAAC;IAC9B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}