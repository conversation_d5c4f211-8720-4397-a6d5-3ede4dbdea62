{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"client-dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"layout-container\"\n};\nconst _hoisted_3 = {\n  class: \"content-wrapper\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"dashboard-content\"\n};\nconst _hoisted_5 = {\n  class: \"welcome-section\"\n};\nconst _hoisted_6 = {\n  class: \"welcome-card\"\n};\nconst _hoisted_7 = {\n  class: \"welcome-content\"\n};\nconst _hoisted_8 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_9 = {\n  class: \"account-status\"\n};\nconst _hoisted_10 = {\n  class: \"stats-section\"\n};\nconst _hoisted_11 = {\n  class: \"stats-grid\"\n};\nconst _hoisted_12 = {\n  class: \"stat-card\"\n};\nconst _hoisted_13 = {\n  class: \"stat-content\"\n};\nconst _hoisted_14 = {\n  class: \"stat-number\"\n};\nconst _hoisted_15 = {\n  class: \"stat-card\"\n};\nconst _hoisted_16 = {\n  class: \"stat-content\"\n};\nconst _hoisted_17 = {\n  class: \"stat-number\"\n};\nconst _hoisted_18 = {\n  class: \"stat-card\"\n};\nconst _hoisted_19 = {\n  class: \"stat-content\"\n};\nconst _hoisted_20 = {\n  class: \"stat-number\"\n};\nconst _hoisted_21 = {\n  class: \"stat-card\"\n};\nconst _hoisted_22 = {\n  class: \"stat-content\"\n};\nconst _hoisted_23 = {\n  class: \"stat-number\"\n};\nconst _hoisted_24 = {\n  class: \"quick-actions-section\"\n};\nconst _hoisted_25 = {\n  class: \"actions-grid\"\n};\nconst _hoisted_26 = {\n  class: \"activity-section\"\n};\nconst _hoisted_27 = {\n  class: \"activity-content\"\n};\nconst _hoisted_28 = {\n  class: \"activity-main\"\n};\nconst _hoisted_29 = {\n  key: 0,\n  class: \"empty-state\"\n};\nconst _hoisted_30 = {\n  key: 1,\n  class: \"activity-list\"\n};\nconst _hoisted_31 = {\n  class: \"activity-icon\"\n};\nconst _hoisted_32 = {\n  class: \"activity-details\"\n};\nconst _hoisted_33 = {\n  class: \"activity-title\"\n};\nconst _hoisted_34 = {\n  class: \"activity-description\"\n};\nconst _hoisted_35 = {\n  class: \"activity-time\"\n};\nconst _hoisted_36 = {\n  class: \"account-info\"\n};\nconst _hoisted_37 = {\n  class: \"info-list\"\n};\nconst _hoisted_38 = {\n  class: \"info-item\"\n};\nconst _hoisted_39 = {\n  class: \"info-value\"\n};\nconst _hoisted_40 = {\n  class: \"info-item\"\n};\nconst _hoisted_41 = {\n  class: \"info-value-with-status\"\n};\nconst _hoisted_42 = {\n  key: 0,\n  class: \"fas fa-check-circle verified\",\n  title: \"Verified\"\n};\nconst _hoisted_43 = {\n  key: 1,\n  class: \"fas fa-exclamation-circle unverified\",\n  title: \"Not verified\"\n};\nconst _hoisted_44 = {\n  class: \"info-item\"\n};\nconst _hoisted_45 = {\n  class: \"info-value-with-status\"\n};\nconst _hoisted_46 = {\n  key: 0,\n  class: \"fas fa-check-circle verified\",\n  title: \"Verified\"\n};\nconst _hoisted_47 = {\n  key: 1,\n  class: \"fas fa-exclamation-circle unverified\",\n  title: \"Not verified\"\n};\nconst _hoisted_48 = {\n  class: \"info-item\"\n};\nconst _hoisted_49 = {\n  class: \"info-value\"\n};\nconst _hoisted_50 = {\n  class: \"help-content\"\n};\nconst _hoisted_51 = {\n  class: \"page-content\"\n};\nconst _hoisted_52 = {\n  class: \"page-header\"\n};\nconst _hoisted_53 = {\n  class: \"page-title\"\n};\nconst _hoisted_54 = {\n  class: \"page-description\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientHeader = _resolveComponent(\"ClientHeader\");\n  const _component_ClientSidebar = _resolveComponent(\"ClientSidebar\");\n  const _component_HelpSupport = _resolveComponent(\"HelpSupport\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Header Component \"), _createVNode(_component_ClientHeader, {\n    \"user-name\": _ctx.getFullName(),\n    \"notification-count\": _ctx.notificationCount,\n    \"show-notifications\": _ctx.showNotifications,\n    \"show-user-dropdown\": _ctx.showUserDropdown,\n    \"sidebar-collapsed\": _ctx.sidebarCollapsed,\n    \"active-menu\": _ctx.activeMenu,\n    onSidebarToggle: _ctx.toggleSidebar,\n    onNotificationToggle: _ctx.toggleNotifications,\n    onUserDropdownToggle: _ctx.toggleUserDropdown,\n    onMenuAction: _ctx.handleMenuAction,\n    onLogout: _ctx.logout,\n    onViewAllNotifications: _ctx.handleViewAllNotifications\n  }, null, 8 /* PROPS */, [\"user-name\", \"notification-count\", \"show-notifications\", \"show-user-dropdown\", \"sidebar-collapsed\", \"active-menu\", \"onSidebarToggle\", \"onNotificationToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\", \"onViewAllNotifications\"]), _createCommentVNode(\" Mobile Overlay \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"mobile-overlay\", {\n      active: !_ctx.sidebarCollapsed && _ctx.isMobile\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => _ctx.closeMobileSidebar && _ctx.closeMobileSidebar(...args))\n  }, null, 2 /* CLASS */), _createCommentVNode(\" Layout Container \"), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" Sidebar \"), _createVNode(_component_ClientSidebar, {\n    collapsed: _ctx.sidebarCollapsed,\n    \"active-menu\": _ctx.activeMenu,\n    \"pending-requests\": _ctx.pendingRequests,\n    \"completed-requests\": _ctx.completedRequests,\n    \"total-services\": _ctx.totalServices,\n    onMenuChange: _ctx.setActiveMenu,\n    onToggleSidebar: _ctx.toggleSidebar,\n    onLogout: _ctx.logout\n  }, null, 8 /* PROPS */, [\"collapsed\", \"active-menu\", \"pending-requests\", \"completed-requests\", \"total-services\", \"onMenuChange\", \"onToggleSidebar\", \"onLogout\"]), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': _ctx.sidebarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" Dashboard Content \"), _ctx.activeMenu === 'dashboard' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createCommentVNode(\" Welcome Section \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h2\", _hoisted_8, \"Welcome back, \" + _toDisplayString(_ctx.getFullName()) + \"!\", 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"p\", {\n    class: \"welcome-text\"\n  }, \" Access barangay services and manage your requests from your dashboard. \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, [_cache[5] || (_cache[5] = _createElementVNode(\"span\", {\n    class: \"status-label\"\n  }, \"Account Status:\", -1 /* HOISTED */)), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"status-badge\", _ctx.getStatusBadgeClass()])\n  }, _toDisplayString(_ctx.getStatusText()), 3 /* TEXT, CLASS */)])]), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"welcome-avatar\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-circle\"\n  })], -1 /* HOISTED */))])]), _createCommentVNode(\" Stats Cards \"), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"h3\", _hoisted_14, _toDisplayString(_ctx.totalRequests), 1 /* TEXT */), _cache[8] || (_cache[8] = _createElementVNode(\"p\", {\n    class: \"stat-label\"\n  }, \"Total Requests\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_15, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"stat-icon pending\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clock\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"h3\", _hoisted_17, _toDisplayString(_ctx.pendingRequests), 1 /* TEXT */), _cache[10] || (_cache[10] = _createElementVNode(\"p\", {\n    class: \"stat-label\"\n  }, \"Pending\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_18, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n    class: \"stat-icon completed\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-check-circle\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"h3\", _hoisted_20, _toDisplayString(_ctx.completedRequests), 1 /* TEXT */), _cache[12] || (_cache[12] = _createElementVNode(\"p\", {\n    class: \"stat-label\"\n  }, \"Completed\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_21, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-peso-sign\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"h3\", _hoisted_23, \"₱\" + _toDisplayString(_ctx.totalPaid), 1 /* TEXT */), _cache[14] || (_cache[14] = _createElementVNode(\"p\", {\n    class: \"stat-label\"\n  }, \"Total Paid\", -1 /* HOISTED */))])])])]), _createCommentVNode(\" Quick Actions \"), _createElementVNode(\"div\", _hoisted_24, [_cache[20] || (_cache[20] = _createElementVNode(\"h3\", {\n    class: \"section-title\"\n  }, \"Quick Actions\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[1] || (_cache[1] = $event => _ctx.navigateTo('new-request'))\n  }, _cache[16] || (_cache[16] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-42f09c35><i class=\\\"fas fa-plus\\\" data-v-42f09c35></i></div><div class=\\\"action-content\\\" data-v-42f09c35><h4 class=\\\"action-title\\\" data-v-42f09c35>New Request</h4><p class=\\\"action-description\\\" data-v-42f09c35>Apply for documents and certificates</p></div>\", 2)])), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[2] || (_cache[2] = $event => _ctx.navigateTo('track-request'))\n  }, _cache[17] || (_cache[17] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-42f09c35><i class=\\\"fas fa-search\\\" data-v-42f09c35></i></div><div class=\\\"action-content\\\" data-v-42f09c35><h4 class=\\\"action-title\\\" data-v-42f09c35>Track Request</h4><p class=\\\"action-description\\\" data-v-42f09c35>Check your application status</p></div>\", 2)])), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[3] || (_cache[3] = $event => _ctx.navigateTo('payments'))\n  }, _cache[18] || (_cache[18] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-42f09c35><i class=\\\"fas fa-credit-card\\\" data-v-42f09c35></i></div><div class=\\\"action-content\\\" data-v-42f09c35><h4 class=\\\"action-title\\\" data-v-42f09c35>Make Payment</h4><p class=\\\"action-description\\\" data-v-42f09c35>Pay for your requests online</p></div>\", 2)])), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[4] || (_cache[4] = $event => _ctx.navigateTo('help'))\n  }, _cache[19] || (_cache[19] = [_createStaticVNode(\"<div class=\\\"action-icon\\\" data-v-42f09c35><i class=\\\"fas fa-headset\\\" data-v-42f09c35></i></div><div class=\\\"action-content\\\" data-v-42f09c35><h4 class=\\\"action-title\\\" data-v-42f09c35>Get Help</h4><p class=\\\"action-description\\\" data-v-42f09c35>Contact support for assistance</p></div>\", 2)]))])]), _createCommentVNode(\" Recent Activity \"), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_cache[22] || (_cache[22] = _createElementVNode(\"h3\", {\n    class: \"section-title\"\n  }, \"Recent Activity\", -1 /* HOISTED */)), _ctx.recentActivity.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, _cache[21] || (_cache[21] = [_createElementVNode(\"i\", {\n    class: \"fas fa-inbox\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"h4\", null, \"No recent activity\", -1 /* HOISTED */), _createElementVNode(\"p\", null, \"Your recent requests and updates will appear here\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.recentActivity, activity => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: activity.id,\n      class: \"activity-item\"\n    }, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"i\", {\n      class: _normalizeClass(activity.icon)\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"h4\", _hoisted_33, _toDisplayString(activity.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_34, _toDisplayString(activity.description), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_35, _toDisplayString(activity.time), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))]))]), _createElementVNode(\"div\", _hoisted_36, [_cache[27] || (_cache[27] = _createElementVNode(\"h3\", {\n    class: \"section-title\"\n  }, \"Account Information\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_cache[23] || (_cache[23] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"Username\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_39, _toDisplayString(_ctx.clientData?.username), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_40, [_cache[24] || (_cache[24] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"Email\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"span\", null, _toDisplayString(_ctx.clientData?.profile?.email || 'Not provided'), 1 /* TEXT */), _ctx.clientData?.email_verified ? (_openBlock(), _createElementBlock(\"i\", _hoisted_42)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_43))])]), _createElementVNode(\"div\", _hoisted_44, [_cache[25] || (_cache[25] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"Phone\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"span\", null, _toDisplayString(_ctx.clientData?.profile?.phone_number || 'Not provided'), 1 /* TEXT */), _ctx.clientData?.phone_verified ? (_openBlock(), _createElementBlock(\"i\", _hoisted_46)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_47))])]), _createElementVNode(\"div\", _hoisted_48, [_cache[26] || (_cache[26] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"Member Since\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_49, _toDisplayString(_ctx.formatDate(_ctx.clientData?.created_at)), 1 /* TEXT */)])])])])])])) : _ctx.activeMenu === 'help' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Help & Support Content \"), _createElementVNode(\"div\", _hoisted_50, [_createVNode(_component_HelpSupport)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Other Menu Content Placeholders \"), _createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"h2\", _hoisted_53, _toDisplayString(_ctx.getPageTitle()), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_54, _toDisplayString(_ctx.getPageDescription()), 1 /* TEXT */)]), _cache[28] || (_cache[28] = _createElementVNode(\"div\", {\n    class: \"page-body\"\n  }, [_createElementVNode(\"div\", {\n    class: \"coming-soon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-tools\"\n  }), _createElementVNode(\"h3\", null, \"Coming Soon\"), _createElementVNode(\"p\", null, \"This feature is under development and will be available soon.\")])], -1 /* HOISTED */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 2 /* CLASS */)])]);\n}", "map": {"version": 3, "names": ["class", "title", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_ClientHeader", "_ctx", "getFullName", "notificationCount", "showNotifications", "showUserDropdown", "sidebarCollapsed", "activeMenu", "onSidebarToggle", "toggleSidebar", "onNotificationToggle", "toggleNotifications", "onUserDropdownToggle", "toggleUserDropdown", "onMenuAction", "handleMenuAction", "onLogout", "logout", "onViewAllNotifications", "handleViewAllNotifications", "_createElementVNode", "_normalizeClass", "active", "isMobile", "onClick", "_cache", "args", "closeMobileSidebar", "_hoisted_2", "_component_ClientSidebar", "collapsed", "pendingRequests", "completedRequests", "totalServices", "onMenuChange", "setActiveMenu", "onToggleSidebar", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "_hoisted_9", "getStatusBadgeClass", "getStatusText", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "totalRequests", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "totalPaid", "_hoisted_24", "_hoisted_25", "$event", "navigateTo", "_hoisted_26", "_hoisted_27", "_hoisted_28", "recentActivity", "length", "_hoisted_29", "_hoisted_30", "_Fragment", "_renderList", "activity", "key", "id", "_hoisted_31", "icon", "_hoisted_32", "_hoisted_33", "_hoisted_34", "description", "_hoisted_35", "time", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "clientData", "username", "_hoisted_40", "_hoisted_41", "profile", "email", "email_verified", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "phone_number", "phone_verified", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "formatDate", "created_at", "_hoisted_50", "_component_HelpSupport", "_hoisted_51", "_hoisted_52", "_hoisted_53", "getPageTitle", "_hoisted_54", "getPageDescription"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"client-dashboard\">\n    <!-- Header Component -->\n    <ClientHeader\n      :user-name=\"getFullName()\"\n      :notification-count=\"notificationCount\"\n      :show-notifications=\"showNotifications\"\n      :show-user-dropdown=\"showUserDropdown\"\n      :sidebar-collapsed=\"sidebarCollapsed\"\n      :active-menu=\"activeMenu\"\n      @sidebar-toggle=\"toggleSidebar\"\n      @notification-toggle=\"toggleNotifications\"\n      @user-dropdown-toggle=\"toggleUserDropdown\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"logout\"\n      @view-all-notifications=\"handleViewAllNotifications\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <!-- Layout Container -->\n    <div class=\"layout-container\">\n      <!-- Sidebar -->\n      <ClientSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :active-menu=\"activeMenu\"\n        :pending-requests=\"pendingRequests\"\n        :completed-requests=\"completedRequests\"\n        :total-services=\"totalServices\"\n        @menu-change=\"setActiveMenu\"\n        @toggle-sidebar=\"toggleSidebar\"\n        @logout=\"logout\"\n      />\n\n      <!-- Main Content -->\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"content-wrapper\">\n          <!-- Dashboard Content -->\n          <div v-if=\"activeMenu === 'dashboard'\" class=\"dashboard-content\">\n          <!-- Welcome Section -->\n          <div class=\"welcome-section\">\n            <div class=\"welcome-card\">\n              <div class=\"welcome-content\">\n                <h2 class=\"welcome-title\">Welcome back, {{ getFullName() }}!</h2>\n                <p class=\"welcome-text\">\n                  Access barangay services and manage your requests from your dashboard.\n                </p>\n                <div class=\"account-status\">\n                  <span class=\"status-label\">Account Status:</span>\n                  <span class=\"status-badge\" :class=\"getStatusBadgeClass()\">\n                    {{ getStatusText() }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"welcome-avatar\">\n                <i class=\"fas fa-user-circle\"></i>\n              </div>\n            </div>\n          </div>\n\n          <!-- Stats Cards -->\n          <div class=\"stats-section\">\n            <div class=\"stats-grid\">\n              <div class=\"stat-card\">\n                <div class=\"stat-icon\">\n                  <i class=\"fas fa-file-alt\"></i>\n                </div>\n                <div class=\"stat-content\">\n                  <h3 class=\"stat-number\">{{ totalRequests }}</h3>\n                  <p class=\"stat-label\">Total Requests</p>\n                </div>\n              </div>\n\n              <div class=\"stat-card\">\n                <div class=\"stat-icon pending\">\n                  <i class=\"fas fa-clock\"></i>\n                </div>\n                <div class=\"stat-content\">\n                  <h3 class=\"stat-number\">{{ pendingRequests }}</h3>\n                  <p class=\"stat-label\">Pending</p>\n                </div>\n              </div>\n\n              <div class=\"stat-card\">\n                <div class=\"stat-icon completed\">\n                  <i class=\"fas fa-check-circle\"></i>\n                </div>\n                <div class=\"stat-content\">\n                  <h3 class=\"stat-number\">{{ completedRequests }}</h3>\n                  <p class=\"stat-label\">Completed</p>\n                </div>\n              </div>\n\n              <div class=\"stat-card\">\n                <div class=\"stat-icon\">\n                  <i class=\"fas fa-peso-sign\"></i>\n                </div>\n                <div class=\"stat-content\">\n                  <h3 class=\"stat-number\">₱{{ totalPaid }}</h3>\n                  <p class=\"stat-label\">Total Paid</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Actions -->\n          <div class=\"quick-actions-section\">\n            <h3 class=\"section-title\">Quick Actions</h3>\n            <div class=\"actions-grid\">\n              <div class=\"action-card\" @click=\"navigateTo('new-request')\">\n                <div class=\"action-icon\">\n                  <i class=\"fas fa-plus\"></i>\n                </div>\n                <div class=\"action-content\">\n                  <h4 class=\"action-title\">New Request</h4>\n                  <p class=\"action-description\">Apply for documents and certificates</p>\n                </div>\n              </div>\n\n              <div class=\"action-card\" @click=\"navigateTo('track-request')\">\n                <div class=\"action-icon\">\n                  <i class=\"fas fa-search\"></i>\n                </div>\n                <div class=\"action-content\">\n                  <h4 class=\"action-title\">Track Request</h4>\n                  <p class=\"action-description\">Check your application status</p>\n                </div>\n              </div>\n\n              <div class=\"action-card\" @click=\"navigateTo('payments')\">\n                <div class=\"action-icon\">\n                  <i class=\"fas fa-credit-card\"></i>\n                </div>\n                <div class=\"action-content\">\n                  <h4 class=\"action-title\">Make Payment</h4>\n                  <p class=\"action-description\">Pay for your requests online</p>\n                </div>\n              </div>\n\n              <div class=\"action-card\" @click=\"navigateTo('help')\">\n                <div class=\"action-icon\">\n                  <i class=\"fas fa-headset\"></i>\n                </div>\n                <div class=\"action-content\">\n                  <h4 class=\"action-title\">Get Help</h4>\n                  <p class=\"action-description\">Contact support for assistance</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Recent Activity -->\n          <div class=\"activity-section\">\n            <div class=\"activity-content\">\n              <div class=\"activity-main\">\n                <h3 class=\"section-title\">Recent Activity</h3>\n                <div v-if=\"recentActivity.length === 0\" class=\"empty-state\">\n                  <i class=\"fas fa-inbox\"></i>\n                  <h4>No recent activity</h4>\n                  <p>Your recent requests and updates will appear here</p>\n                </div>\n\n                <div v-else class=\"activity-list\">\n                  <div v-for=\"activity in recentActivity\" :key=\"activity.id\" class=\"activity-item\">\n                    <div class=\"activity-icon\">\n                      <i :class=\"activity.icon\"></i>\n                    </div>\n                    <div class=\"activity-details\">\n                      <h4 class=\"activity-title\">{{ activity.title }}</h4>\n                      <p class=\"activity-description\">{{ activity.description }}</p>\n                      <span class=\"activity-time\">{{ activity.time }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"account-info\">\n                <h3 class=\"section-title\">Account Information</h3>\n                <div class=\"info-list\">\n                  <div class=\"info-item\">\n                    <span class=\"info-label\">Username</span>\n                    <span class=\"info-value\">{{ clientData?.username }}</span>\n                  </div>\n\n                  <div class=\"info-item\">\n                    <span class=\"info-label\">Email</span>\n                    <div class=\"info-value-with-status\">\n                      <span>{{ clientData?.profile?.email || 'Not provided' }}</span>\n                      <i v-if=\"clientData?.email_verified\"\n                         class=\"fas fa-check-circle verified\"\n                         title=\"Verified\"></i>\n                      <i v-else\n                         class=\"fas fa-exclamation-circle unverified\"\n                         title=\"Not verified\"></i>\n                    </div>\n                  </div>\n\n                  <div class=\"info-item\">\n                    <span class=\"info-label\">Phone</span>\n                    <div class=\"info-value-with-status\">\n                      <span>{{ clientData?.profile?.phone_number || 'Not provided' }}</span>\n                      <i v-if=\"clientData?.phone_verified\"\n                         class=\"fas fa-check-circle verified\"\n                         title=\"Verified\"></i>\n                      <i v-else\n                         class=\"fas fa-exclamation-circle unverified\"\n                         title=\"Not verified\"></i>\n                    </div>\n                  </div>\n\n                  <div class=\"info-item\">\n                    <span class=\"info-label\">Member Since</span>\n                    <span class=\"info-value\">{{ formatDate(clientData?.created_at) }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Help & Support Content -->\n        <div v-else-if=\"activeMenu === 'help'\" class=\"help-content\">\n          <HelpSupport />\n        </div>\n\n        <!-- Other Menu Content Placeholders -->\n        <div v-else class=\"page-content\">\n          <div class=\"page-header\">\n            <h2 class=\"page-title\">{{ getPageTitle() }}</h2>\n            <p class=\"page-description\">{{ getPageDescription() }}</p>\n          </div>\n\n          <div class=\"page-body\">\n            <div class=\"coming-soon\">\n              <i class=\"fas fa-tools\"></i>\n              <h3>Coming Soon</h3>\n              <p>This feature is under development and will be available soon.</p>\n            </div>\n          </div>\n        </div>\n        </div>\n      </main>\n    </div>\n  </div>\n</template>\n\n<script src=\"./js/clientDashboard.js\"></script>\n\n<style scoped src=\"./css/clientDashboard.css\"></style>\n<style src=\"./css/mobileUtilities.css\"></style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAyBtBA,KAAK,EAAC;AAAkB;;EAepBA,KAAK,EAAC;AAAiB;;;EAEaA,KAAK,EAAC;;;EAExCA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAiB;;EACtBA,KAAK,EAAC;AAAe;;EAIpBA,KAAK,EAAC;AAAgB;;EAc5BA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAKtBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAKtBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAKtBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAQ1BA,KAAK,EAAC;AAAuB;;EAE3BA,KAAK,EAAC;AAAc;;EA4CtBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAe;;;EAEgBA,KAAK,EAAC;;;;EAMlCA,KAAK,EAAC;;;EAETA,KAAK,EAAC;AAAe;;EAGrBA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAgB;;EACvBA,KAAK,EAAC;AAAsB;;EACzBA,KAAK,EAAC;AAAe;;EAM9BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAY;;EAGrBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAwB;;;EAG9BA,KAAK,EAAC,8BAA8B;EACpCC,KAAK,EAAC;;;;EAEND,KAAK,EAAC,sCAAsC;EAC5CC,KAAK,EAAC;;;EAIRD,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAwB;;;EAG9BA,KAAK,EAAC,8BAA8B;EACpCC,KAAK,EAAC;;;;EAEND,KAAK,EAAC,sCAAsC;EAC5CC,KAAK,EAAC;;;EAIRD,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAY;;EASGA,KAAK,EAAC;AAAc;;EAK/CA,KAAK,EAAC;AAAc;;EACzBA,KAAK,EAAC;AAAa;;EAClBA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAkB;;;;;uBAzOrCE,mBAAA,CAuPM,OAvPNC,UAuPM,GAtPJC,mBAAA,sBAAyB,EACzBC,YAAA,CAaEC,uBAAA;IAZC,WAAS,EAAEC,IAAA,CAAAC,WAAW;IACtB,oBAAkB,EAAED,IAAA,CAAAE,iBAAiB;IACrC,oBAAkB,EAAEF,IAAA,CAAAG,iBAAiB;IACrC,oBAAkB,EAAEH,IAAA,CAAAI,gBAAgB;IACpC,mBAAiB,EAAEJ,IAAA,CAAAK,gBAAgB;IACnC,aAAW,EAAEL,IAAA,CAAAM,UAAU;IACvBC,eAAc,EAAEP,IAAA,CAAAQ,aAAa;IAC7BC,oBAAmB,EAAET,IAAA,CAAAU,mBAAmB;IACxCC,oBAAoB,EAAEX,IAAA,CAAAY,kBAAkB;IACxCC,YAAW,EAAEb,IAAA,CAAAc,gBAAgB;IAC7BC,QAAM,EAAEf,IAAA,CAAAgB,MAAM;IACdC,sBAAsB,EAAEjB,IAAA,CAAAkB;yQAG3BrB,mBAAA,oBAAuB,EACvBsB,mBAAA,CAIO;IAHL1B,KAAK,EAAA2B,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GACHrB,IAAA,CAAAK,gBAAgB,IAAIL,IAAA,CAAAsB;IAAQ;IAC9CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEzB,IAAA,CAAA0B,kBAAA,IAAA1B,IAAA,CAAA0B,kBAAA,IAAAD,IAAA,CAAkB;2BAG5B5B,mBAAA,sBAAyB,EACzBsB,mBAAA,CA6NM,OA7NNQ,UA6NM,GA5NJ9B,mBAAA,aAAgB,EAChBC,YAAA,CASE8B,wBAAA;IARCC,SAAS,EAAE7B,IAAA,CAAAK,gBAAgB;IAC3B,aAAW,EAAEL,IAAA,CAAAM,UAAU;IACvB,kBAAgB,EAAEN,IAAA,CAAA8B,eAAe;IACjC,oBAAkB,EAAE9B,IAAA,CAAA+B,iBAAiB;IACrC,gBAAc,EAAE/B,IAAA,CAAAgC,aAAa;IAC7BC,YAAW,EAAEjC,IAAA,CAAAkC,aAAa;IAC1BC,eAAc,EAAEnC,IAAA,CAAAQ,aAAa;IAC7BO,QAAM,EAAEf,IAAA,CAAAgB;oKAGXnB,mBAAA,kBAAqB,EACrBsB,mBAAA,CA8MO;IA9MD1B,KAAK,EAAA2B,eAAA,EAAC,cAAc;MAAA,qBAAgCpB,IAAA,CAAAK;IAAgB;MACxEc,mBAAA,CA4MM,OA5MNiB,UA4MM,GA3MJvC,mBAAA,uBAA0B,EACfG,IAAA,CAAAM,UAAU,oB,cAArBX,mBAAA,CAoLI,OApLJ0C,UAoLI,GAnLJxC,mBAAA,qBAAwB,EACxBsB,mBAAA,CAkBM,OAlBNmB,UAkBM,GAjBJnB,mBAAA,CAgBM,OAhBNoB,UAgBM,GAfJpB,mBAAA,CAWM,OAXNqB,UAWM,GAVJrB,mBAAA,CAAiE,MAAjEsB,UAAiE,EAAvC,gBAAc,GAAAC,gBAAA,CAAG1C,IAAA,CAAAC,WAAW,MAAK,GAAC,iB,0BAC5DkB,mBAAA,CAEI;IAFD1B,KAAK,EAAC;EAAc,GAAC,0EAExB,sBACA0B,mBAAA,CAKM,OALNwB,UAKM,G,0BAJJxB,mBAAA,CAAiD;IAA3C1B,KAAK,EAAC;EAAc,GAAC,iBAAe,sBAC1C0B,mBAAA,CAEO;IAFD1B,KAAK,EAAA2B,eAAA,EAAC,cAAc,EAASpB,IAAA,CAAA4C,mBAAmB;sBACjD5C,IAAA,CAAA6C,aAAa,0B,+BAItB1B,mBAAA,CAEM;IAFD1B,KAAK,EAAC;EAAgB,IACzB0B,mBAAA,CAAkC;IAA/B1B,KAAK,EAAC;EAAoB,G,0BAKnCI,mBAAA,iBAAoB,EACpBsB,mBAAA,CA0CM,OA1CN2B,WA0CM,GAzCJ3B,mBAAA,CAwCM,OAxCN4B,WAwCM,GAvCJ5B,mBAAA,CAQM,OARN6B,WAQM,G,0BAPJ7B,mBAAA,CAEM;IAFD1B,KAAK,EAAC;EAAW,IACpB0B,mBAAA,CAA+B;IAA5B1B,KAAK,EAAC;EAAiB,G,sBAE5B0B,mBAAA,CAGM,OAHN8B,WAGM,GAFJ9B,mBAAA,CAAgD,MAAhD+B,WAAgD,EAAAR,gBAAA,CAArB1C,IAAA,CAAAmD,aAAa,kB,0BACxChC,mBAAA,CAAwC;IAArC1B,KAAK,EAAC;EAAY,GAAC,gBAAc,qB,KAIxC0B,mBAAA,CAQM,OARNiC,WAQM,G,4BAPJjC,mBAAA,CAEM;IAFD1B,KAAK,EAAC;EAAmB,IAC5B0B,mBAAA,CAA4B;IAAzB1B,KAAK,EAAC;EAAc,G,sBAEzB0B,mBAAA,CAGM,OAHNkC,WAGM,GAFJlC,mBAAA,CAAkD,MAAlDmC,WAAkD,EAAAZ,gBAAA,CAAvB1C,IAAA,CAAA8B,eAAe,kB,4BAC1CX,mBAAA,CAAiC;IAA9B1B,KAAK,EAAC;EAAY,GAAC,SAAO,qB,KAIjC0B,mBAAA,CAQM,OARNoC,WAQM,G,4BAPJpC,mBAAA,CAEM;IAFD1B,KAAK,EAAC;EAAqB,IAC9B0B,mBAAA,CAAmC;IAAhC1B,KAAK,EAAC;EAAqB,G,sBAEhC0B,mBAAA,CAGM,OAHNqC,WAGM,GAFJrC,mBAAA,CAAoD,MAApDsC,WAAoD,EAAAf,gBAAA,CAAzB1C,IAAA,CAAA+B,iBAAiB,kB,4BAC5CZ,mBAAA,CAAmC;IAAhC1B,KAAK,EAAC;EAAY,GAAC,WAAS,qB,KAInC0B,mBAAA,CAQM,OARNuC,WAQM,G,4BAPJvC,mBAAA,CAEM;IAFD1B,KAAK,EAAC;EAAW,IACpB0B,mBAAA,CAAgC;IAA7B1B,KAAK,EAAC;EAAkB,G,sBAE7B0B,mBAAA,CAGM,OAHNwC,WAGM,GAFJxC,mBAAA,CAA6C,MAA7CyC,WAA6C,EAArB,GAAC,GAAAlB,gBAAA,CAAG1C,IAAA,CAAA6D,SAAS,kB,4BACrC1C,mBAAA,CAAoC;IAAjC1B,KAAK,EAAC;EAAY,GAAC,YAAU,qB,SAMxCI,mBAAA,mBAAsB,EACtBsB,mBAAA,CA2CM,OA3CN2C,WA2CM,G,4BA1CJ3C,mBAAA,CAA4C;IAAxC1B,KAAK,EAAC;EAAe,GAAC,eAAa,sBACvC0B,mBAAA,CAwCM,OAxCN4C,WAwCM,GAvCJ5C,mBAAA,CAQM;IARD1B,KAAK,EAAC,aAAa;IAAE8B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwC,MAAA,IAAEhE,IAAA,CAAAiE,UAAU;qWAU3C9C,mBAAA,CAQM;IARD1B,KAAK,EAAC,aAAa;IAAE8B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwC,MAAA,IAAEhE,IAAA,CAAAiE,UAAU;kWAU3C9C,mBAAA,CAQM;IARD1B,KAAK,EAAC,aAAa;IAAE8B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwC,MAAA,IAAEhE,IAAA,CAAAiE,UAAU;qWAU3C9C,mBAAA,CAQM;IARD1B,KAAK,EAAC,aAAa;IAAE8B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwC,MAAA,IAAEhE,IAAA,CAAAiE,UAAU;mWAY/CpE,mBAAA,qBAAwB,EACxBsB,mBAAA,CAiEM,OAjEN+C,WAiEM,GAhEJ/C,mBAAA,CA+DM,OA/DNgD,WA+DM,GA9DJhD,mBAAA,CAoBM,OApBNiD,WAoBM,G,4BAnBJjD,mBAAA,CAA8C;IAA1C1B,KAAK,EAAC;EAAe,GAAC,iBAAe,sBAC9BO,IAAA,CAAAqE,cAAc,CAACC,MAAM,U,cAAhC3E,mBAAA,CAIM,OAJN4E,WAIM,EAAA/C,MAAA,SAAAA,MAAA,QAHJL,mBAAA,CAA4B;IAAzB1B,KAAK,EAAC;EAAc,4BACvB0B,mBAAA,CAA2B,YAAvB,oBAAkB,qBACtBA,mBAAA,CAAwD,WAArD,mDAAiD,oB,qBAGtDxB,mBAAA,CAWM,OAXN6E,WAWM,I,kBAVJ7E,mBAAA,CASM8E,SAAA,QAAAC,WAAA,CATkB1E,IAAA,CAAAqE,cAAc,EAA1BM,QAAQ;yBAApBhF,mBAAA,CASM;MATmCiF,GAAG,EAAED,QAAQ,CAACE,EAAE;MAAEpF,KAAK,EAAC;QAC/D0B,mBAAA,CAEM,OAFN2D,WAEM,GADJ3D,mBAAA,CAA8B;MAA1B1B,KAAK,EAAA2B,eAAA,CAAEuD,QAAQ,CAACI,IAAI;+BAE1B5D,mBAAA,CAIM,OAJN6D,WAIM,GAHJ7D,mBAAA,CAAoD,MAApD8D,WAAoD,EAAAvC,gBAAA,CAAtBiC,QAAQ,CAACjF,KAAK,kBAC5CyB,mBAAA,CAA8D,KAA9D+D,WAA8D,EAAAxC,gBAAA,CAA3BiC,QAAQ,CAACQ,WAAW,kBACvDhE,mBAAA,CAAsD,QAAtDiE,WAAsD,EAAA1C,gBAAA,CAAvBiC,QAAQ,CAACU,IAAI,iB;uCAMpDlE,mBAAA,CAuCM,OAvCNmE,WAuCM,G,4BAtCJnE,mBAAA,CAAkD;IAA9C1B,KAAK,EAAC;EAAe,GAAC,qBAAmB,sBAC7C0B,mBAAA,CAoCM,OApCNoE,WAoCM,GAnCJpE,mBAAA,CAGM,OAHNqE,WAGM,G,4BAFJrE,mBAAA,CAAwC;IAAlC1B,KAAK,EAAC;EAAY,GAAC,UAAQ,sBACjC0B,mBAAA,CAA0D,QAA1DsE,WAA0D,EAAA/C,gBAAA,CAA9B1C,IAAA,CAAA0F,UAAU,EAAEC,QAAQ,iB,GAGlDxE,mBAAA,CAWM,OAXNyE,WAWM,G,4BAVJzE,mBAAA,CAAqC;IAA/B1B,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC9B0B,mBAAA,CAQM,OARN0E,WAQM,GAPJ1E,mBAAA,CAA+D,cAAAuB,gBAAA,CAAtD1C,IAAA,CAAA0F,UAAU,EAAEI,OAAO,EAAEC,KAAK,oCAC1B/F,IAAA,CAAA0F,UAAU,EAAEM,cAAc,I,cAAnCrG,mBAAA,CAEwB,KAFxBsG,WAEwB,M,cACxBtG,mBAAA,CAE4B,KAF5BuG,WAE4B,G,KAIhC/E,mBAAA,CAWM,OAXNgF,WAWM,G,4BAVJhF,mBAAA,CAAqC;IAA/B1B,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC9B0B,mBAAA,CAQM,OARNiF,WAQM,GAPJjF,mBAAA,CAAsE,cAAAuB,gBAAA,CAA7D1C,IAAA,CAAA0F,UAAU,EAAEI,OAAO,EAAEO,YAAY,oCACjCrG,IAAA,CAAA0F,UAAU,EAAEY,cAAc,I,cAAnC3G,mBAAA,CAEwB,KAFxB4G,WAEwB,M,cACxB5G,mBAAA,CAE4B,KAF5B6G,WAE4B,G,KAIhCrF,mBAAA,CAGM,OAHNsF,WAGM,G,4BAFJtF,mBAAA,CAA4C;IAAtC1B,KAAK,EAAC;EAAY,GAAC,cAAY,sBACrC0B,mBAAA,CAAwE,QAAxEuF,WAAwE,EAAAhE,gBAAA,CAA5C1C,IAAA,CAAA2G,UAAU,CAAC3G,IAAA,CAAA0F,UAAU,EAAEkB,UAAU,kB,eASzD5G,IAAA,CAAAM,UAAU,e,cAA1BX,mBAAA,CAEM8E,SAAA;IAAAG,GAAA;EAAA,IAHN/E,mBAAA,4BAA+B,EAC/BsB,mBAAA,CAEM,OAFN0F,WAEM,GADJ/G,YAAA,CAAegH,sBAAA,E,qEAIjBnH,mBAAA,CAaM8E,SAAA;IAAAG,GAAA;EAAA,IAdN/E,mBAAA,qCAAwC,EACxCsB,mBAAA,CAaM,OAbN4F,WAaM,GAZJ5F,mBAAA,CAGM,OAHN6F,WAGM,GAFJ7F,mBAAA,CAAgD,MAAhD8F,WAAgD,EAAAvE,gBAAA,CAAtB1C,IAAA,CAAAkH,YAAY,oBACtC/F,mBAAA,CAA0D,KAA1DgG,WAA0D,EAAAzE,gBAAA,CAA3B1C,IAAA,CAAAoH,kBAAkB,mB,+BAGnDjG,mBAAA,CAMM;IAND1B,KAAK,EAAC;EAAW,IACpB0B,mBAAA,CAIM;IAJD1B,KAAK,EAAC;EAAa,IACtB0B,mBAAA,CAA4B;IAAzB1B,KAAK,EAAC;EAAc,IACvB0B,mBAAA,CAAoB,YAAhB,aAAW,GACfA,mBAAA,CAAoE,WAAjE,+DAA6D,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}