import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.VUE_APP_API_URL || 'http://localhost:3000/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: true, // Enable credentials for CORS
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const adminToken = localStorage.getItem('adminToken');
    const clientToken = localStorage.getItem('clientToken');

    // Admin routes that require admin token
    const adminRoutes = [
      '/admin/',
      '/users/',
      '/users?',
      '/notifications/admin',
      '/document-requests/admin'
    ];

    // Check if this is an admin route
    const isAdminRoute = adminRoutes.some(route => {
      if (!config.url) return false;

      // Check for exact match or starts with pattern
      if (route.endsWith('/') || route.includes('?')) {
        return config.url.includes(route) || config.url.startsWith(route.replace('?', ''));
      }
      return config.url.includes(route);
    });

    // Also check for exact /users path
    if (config.url === '/users' || config.url?.startsWith('/users?') || config.url?.startsWith('/users/')) {
      if (adminToken) {
        config.headers.Authorization = `Bearer ${adminToken}`;
      }
    } else if (isAdminRoute && adminToken) {
      config.headers.Authorization = `Bearer ${adminToken}`;
    } else if (clientToken) {
      config.headers.Authorization = `Bearer ${clientToken}`;
    }

    // Debug logging for admin routes only
    if (config.url?.includes('/users') || config.url?.includes('/admin/')) {
      console.log('🔗 API Request:', {
        url: config.url,
        isAdminRoute: isAdminRoute || config.url === '/users' || config.url?.startsWith('/users'),
        hasAdminToken: !!adminToken,
        authHeader: config.headers.Authorization ? 'Bearer ***' : 'None'
      });
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Log error for debugging
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      data: error.response?.data
    });

    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('clientToken');
      localStorage.removeItem('clientData');
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminData');
      // Don't redirect here, let components handle it
    }

    // Add more helpful error messages
    if (!error.response) {
      error.message = 'Network error - please check if the backend server is running on port 3000';
    } else if (error.response.status === 500) {
      error.message = 'Server error - please check the backend logs for details';
    }

    return Promise.reject(error);
  }
);

export default api;
