const axios = require('axios');

async function testCreateUser() {
  console.log('🔍 Testing Create User API...');
  
  try {
    // First login to get a fresh token
    console.log('\n1. Getting fresh admin token...');
    const loginResponse = await axios.post('http://localhost:3000/api/admin/auth/login', {
      username: 'admin12345',
      password: '12345QWERTqwert'
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed');
      return;
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ Got token');
    
    // Test creating a client user
    console.log('\n2. Testing create client user...');
    try {
      const clientUserData = {
        username: `testclient_${Date.now()}`,
        email: `testclient_${Date.now()}@example.com`,
        password: 'TestPassword123!',
        first_name: 'Test',
        last_name: 'Client',
        role: 'client',
        phone_number: '09123456789'
      };
      
      const clientResponse = await axios.post('http://localhost:3000/api/users', clientUserData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Client user created successfully');
      console.log('Response:', JSON.stringify(clientResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Client user creation failed:', error.response?.status);
      console.log('Error data:', error.response?.data);
      console.log('Error message:', error.message);
    }
    
    // Test creating an admin user
    console.log('\n3. Testing create admin user...');
    try {
      const adminUserData = {
        username: `testadmin_${Date.now()}`,
        email: `testadmin_${Date.now()}@example.com`,
        password: 'TestPassword123!',
        first_name: 'Test',
        last_name: 'Admin',
        role: 'admin',
        phone_number: '09987654321'
      };
      
      const adminResponse = await axios.post('http://localhost:3000/api/users', adminUserData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Admin user created successfully');
      console.log('Response:', JSON.stringify(adminResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Admin user creation failed:', error.response?.status);
      console.log('Error data:', error.response?.data);
      console.log('Error message:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
  
  process.exit(0);
}

testCreateUser();
