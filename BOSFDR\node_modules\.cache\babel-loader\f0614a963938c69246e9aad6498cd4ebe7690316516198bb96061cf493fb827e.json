{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, vModelSelect as _vModelSelect, vModelRadio as _vModelRadio, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-requests\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"d-flex justify-content-center align-items-center\",\n  style: {\n    \"min-height\": \"400px\"\n  }\n};\nconst _hoisted_4 = {\n  class: \"container-fluid py-4\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"alert alert-danger alert-dismissible fade show\",\n  role: \"alert\"\n};\nconst _hoisted_6 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_7 = {\n  class: \"col-12\"\n};\nconst _hoisted_8 = {\n  class: \"d-flex justify-content-between align-items-center flex-wrap\"\n};\nconst _hoisted_9 = {\n  class: \"text-muted mb-0\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"ms-2 small\"\n};\nconst _hoisted_11 = {\n  class: \"d-flex gap-2 align-items-center\"\n};\nconst _hoisted_12 = {\n  class: \"real-time-status me-2\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"fas fa-circle pulse\"\n};\nconst _hoisted_14 = {\n  key: 1,\n  class: \"fas fa-pause\"\n};\nconst _hoisted_15 = [\"title\"];\nconst _hoisted_16 = [\"disabled\"];\nconst _hoisted_17 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_18 = {\n  class: \"col-6 col-md-3 mb-2\"\n};\nconst _hoisted_19 = {\n  class: \"card border-left-primary shadow py-1\"\n};\nconst _hoisted_20 = {\n  class: \"card-body p-2\"\n};\nconst _hoisted_21 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_22 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_23 = {\n  class: \"h6 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_24 = {\n  class: \"col-6 col-md-3 mb-2\"\n};\nconst _hoisted_25 = {\n  class: \"card border-left-warning shadow py-1\"\n};\nconst _hoisted_26 = {\n  class: \"card-body p-2\"\n};\nconst _hoisted_27 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_28 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_29 = {\n  class: \"h6 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_30 = {\n  class: \"col-6 col-md-3 mb-2\"\n};\nconst _hoisted_31 = {\n  class: \"card border-left-success shadow py-1\"\n};\nconst _hoisted_32 = {\n  class: \"card-body p-2\"\n};\nconst _hoisted_33 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_34 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_35 = {\n  class: \"h6 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_36 = {\n  class: \"col-6 col-md-3 mb-2\"\n};\nconst _hoisted_37 = {\n  class: \"card border-left-info shadow py-1\"\n};\nconst _hoisted_38 = {\n  class: \"card-body p-2\"\n};\nconst _hoisted_39 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_40 = {\n  class: \"flex-grow-1\"\n};\nconst _hoisted_41 = {\n  class: \"h6 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_42 = {\n  key: 1,\n  class: \"card shadow mb-4\"\n};\nconst _hoisted_43 = {\n  class: \"card-body\"\n};\nconst _hoisted_44 = {\n  class: \"row\"\n};\nconst _hoisted_45 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_46 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_47 = [\"value\"];\nconst _hoisted_48 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_49 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_50 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_51 = {\n  class: \"col-md-1 mb-3 d-flex align-items-end\"\n};\nconst _hoisted_52 = {\n  class: \"d-flex gap-1 w-100\"\n};\nconst _hoisted_53 = {\n  key: 2,\n  class: \"card shadow mb-4\"\n};\nconst _hoisted_54 = {\n  class: \"card-header py-3 bg-warning\"\n};\nconst _hoisted_55 = {\n  class: \"m-0 fw-bold text-dark\"\n};\nconst _hoisted_56 = {\n  class: \"card-body\"\n};\nconst _hoisted_57 = {\n  class: \"row align-items-end\"\n};\nconst _hoisted_58 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_59 = [\"value\"];\nconst _hoisted_60 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_61 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_62 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_63 = [\"disabled\"];\nconst _hoisted_64 = {\n  class: \"d-flex justify-content-between align-items-center mb-4\"\n};\nconst _hoisted_65 = {\n  class: \"d-flex align-items-center gap-3\"\n};\nconst _hoisted_66 = {\n  class: \"btn-group\",\n  role: \"group\",\n  \"aria-label\": \"View toggle\"\n};\nconst _hoisted_67 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_68 = {\n  class: \"text-muted small\"\n};\nconst _hoisted_69 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_70 = {\n  key: 3,\n  class: \"requests-grid\"\n};\nconst _hoisted_71 = {\n  key: 0,\n  class: \"empty-state text-center py-5\"\n};\nconst _hoisted_72 = {\n  class: \"row g-4\"\n};\nconst _hoisted_73 = {\n  class: \"request-card-header\"\n};\nconst _hoisted_74 = {\n  class: \"d-flex justify-content-between align-items-start\"\n};\nconst _hoisted_75 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_76 = [\"checked\", \"onChange\"];\nconst _hoisted_77 = {\n  class: \"request-number\"\n};\nconst _hoisted_78 = {\n  class: \"badge bg-primary\"\n};\nconst _hoisted_79 = {\n  class: \"request-actions-simple\"\n};\nconst _hoisted_80 = [\"onClick\"];\nconst _hoisted_81 = [\"onClick\", \"disabled\"];\nconst _hoisted_82 = [\"onClick\", \"disabled\"];\nconst _hoisted_83 = {\n  class: \"request-card-body\"\n};\nconst _hoisted_84 = {\n  class: \"client-info mb-3\"\n};\nconst _hoisted_85 = {\n  class: \"d-flex align-items-center gap-2 mb-2\"\n};\nconst _hoisted_86 = {\n  class: \"mb-0 fw-bold\"\n};\nconst _hoisted_87 = {\n  class: \"text-muted\"\n};\nconst _hoisted_88 = {\n  class: \"document-type mb-3\"\n};\nconst _hoisted_89 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_90 = {\n  class: \"badge bg-info-subtle text-info-emphasis px-3 py-2\"\n};\nconst _hoisted_91 = {\n  class: \"request-meta mb-3\"\n};\nconst _hoisted_92 = {\n  class: \"row g-2\"\n};\nconst _hoisted_93 = {\n  class: \"col-6\"\n};\nconst _hoisted_94 = {\n  class: \"meta-item\"\n};\nconst _hoisted_95 = {\n  class: \"col-6\"\n};\nconst _hoisted_96 = {\n  class: \"meta-item\"\n};\nconst _hoisted_97 = {\n  class: \"fw-bold text-success\"\n};\nconst _hoisted_98 = {\n  class: \"request-date\"\n};\nconst _hoisted_99 = {\n  class: \"text-muted\"\n};\nconst _hoisted_100 = {\n  class: \"request-card-footer\"\n};\nconst _hoisted_101 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_102 = [\"onClick\"];\nconst _hoisted_103 = [\"onClick\", \"disabled\"];\nconst _hoisted_104 = [\"onClick\", \"disabled\"];\nconst _hoisted_105 = {\n  class: \"modern-table-container\"\n};\nconst _hoisted_106 = {\n  key: 0,\n  class: \"modern-table-empty\"\n};\nconst _hoisted_107 = {\n  class: \"compact-table-wrapper\"\n};\nconst _hoisted_108 = {\n  class: \"compact-table-header\"\n};\nconst _hoisted_109 = {\n  class: \"header-cell selection-header\"\n};\nconst _hoisted_110 = [\"checked\"];\nconst _hoisted_111 = {\n  class: \"compact-table-body\"\n};\nconst _hoisted_112 = {\n  class: \"row-cell selection-cell\"\n};\nconst _hoisted_113 = [\"checked\", \"onChange\"];\nconst _hoisted_114 = {\n  class: \"row-cell request-id-cell\"\n};\nconst _hoisted_115 = {\n  class: \"request-id-content\"\n};\nconst _hoisted_116 = {\n  class: \"request-number\"\n};\nconst _hoisted_117 = {\n  class: \"request-id-small\"\n};\nconst _hoisted_118 = {\n  class: \"row-cell client-cell\"\n};\nconst _hoisted_119 = {\n  class: \"client-compact\"\n};\nconst _hoisted_120 = {\n  class: \"client-info-compact\"\n};\nconst _hoisted_121 = {\n  class: \"client-name-compact\"\n};\nconst _hoisted_122 = {\n  class: \"client-email-compact\"\n};\nconst _hoisted_123 = {\n  class: \"row-cell document-cell\"\n};\nconst _hoisted_124 = {\n  class: \"document-badge\"\n};\nconst _hoisted_125 = {\n  class: \"row-cell status-cell\"\n};\nconst _hoisted_126 = {\n  class: \"row-cell amount-cell\"\n};\nconst _hoisted_127 = {\n  class: \"amount-compact\"\n};\nconst _hoisted_128 = {\n  class: \"row-cell date-cell\"\n};\nconst _hoisted_129 = {\n  class: \"date-compact\"\n};\nconst _hoisted_130 = {\n  class: \"date-main\"\n};\nconst _hoisted_131 = {\n  class: \"time-small\"\n};\nconst _hoisted_132 = {\n  class: \"row-cell actions-cell\"\n};\nconst _hoisted_133 = {\n  class: \"actions-simple\"\n};\nconst _hoisted_134 = [\"onClick\"];\nconst _hoisted_135 = [\"onClick\", \"disabled\"];\nconst _hoisted_136 = [\"onClick\", \"disabled\"];\nconst _hoisted_137 = {\n  key: 5,\n  class: \"pagination-container\"\n};\nconst _hoisted_138 = {\n  \"aria-label\": \"Requests pagination\"\n};\nconst _hoisted_139 = {\n  class: \"pagination pagination-sm justify-content-center mb-0\"\n};\nconst _hoisted_140 = [\"onClick\"];\nconst _hoisted_141 = {\n  key: 2,\n  class: \"modal fade show d-block\",\n  tabindex: \"-1\",\n  style: {\n    \"background-color\": \"rgba(0,0,0,0.5)\"\n  }\n};\nconst _hoisted_142 = {\n  class: \"modal-dialog modal-xl modal-dialog-scrollable\"\n};\nconst _hoisted_143 = {\n  class: \"modal-content\"\n};\nconst _hoisted_144 = {\n  class: \"modal-header\"\n};\nconst _hoisted_145 = {\n  class: \"modal-title\"\n};\nconst _hoisted_146 = {\n  class: \"modal-body\"\n};\nconst _hoisted_147 = {\n  class: \"row\"\n};\nconst _hoisted_148 = {\n  class: \"col-lg-8\"\n};\nconst _hoisted_149 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_150 = {\n  class: \"card-body\"\n};\nconst _hoisted_151 = {\n  class: \"row\"\n};\nconst _hoisted_152 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_153 = {\n  class: \"mb-3\"\n};\nconst _hoisted_154 = {\n  class: \"mb-0\"\n};\nconst _hoisted_155 = {\n  class: \"mb-3\"\n};\nconst _hoisted_156 = {\n  class: \"mb-0\"\n};\nconst _hoisted_157 = {\n  class: \"badge bg-info\"\n};\nconst _hoisted_158 = {\n  class: \"mb-3\"\n};\nconst _hoisted_159 = {\n  class: \"mb-0\"\n};\nconst _hoisted_160 = {\n  class: \"mb-3\"\n};\nconst _hoisted_161 = {\n  class: \"mb-0\"\n};\nconst _hoisted_162 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_163 = {\n  class: \"mb-3\"\n};\nconst _hoisted_164 = {\n  class: \"mb-0\"\n};\nconst _hoisted_165 = {\n  class: \"mb-3\"\n};\nconst _hoisted_166 = {\n  class: \"mb-0\"\n};\nconst _hoisted_167 = {\n  class: \"mb-3\"\n};\nconst _hoisted_168 = {\n  class: \"mb-0\"\n};\nconst _hoisted_169 = {\n  class: \"mb-3\"\n};\nconst _hoisted_170 = {\n  class: \"mb-0\"\n};\nconst _hoisted_171 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_172 = {\n  class: \"card-body\"\n};\nconst _hoisted_173 = {\n  class: \"row\"\n};\nconst _hoisted_174 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_175 = {\n  class: \"mb-3\"\n};\nconst _hoisted_176 = {\n  class: \"mb-0\"\n};\nconst _hoisted_177 = {\n  class: \"mb-3\"\n};\nconst _hoisted_178 = {\n  class: \"mb-0\"\n};\nconst _hoisted_179 = [\"href\"];\nconst _hoisted_180 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_181 = {\n  class: \"mb-3\"\n};\nconst _hoisted_182 = {\n  class: \"mb-0\"\n};\nconst _hoisted_183 = [\"href\"];\nconst _hoisted_184 = {\n  class: \"mb-3\"\n};\nconst _hoisted_185 = {\n  class: \"mb-0\"\n};\nconst _hoisted_186 = {\n  key: 0,\n  class: \"card mb-4\"\n};\nconst _hoisted_187 = {\n  class: \"card-body\"\n};\nconst _hoisted_188 = {\n  key: 0\n};\nconst _hoisted_189 = {\n  class: \"row\"\n};\nconst _hoisted_190 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_191 = {\n  class: \"mb-3\"\n};\nconst _hoisted_192 = {\n  class: \"mb-0\"\n};\nconst _hoisted_193 = {\n  class: \"mb-3\"\n};\nconst _hoisted_194 = {\n  class: \"mb-0\"\n};\nconst _hoisted_195 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_196 = {\n  class: \"mb-3\"\n};\nconst _hoisted_197 = {\n  class: \"mb-0\"\n};\nconst _hoisted_198 = {\n  class: \"mb-3\"\n};\nconst _hoisted_199 = {\n  class: \"mb-0\"\n};\nconst _hoisted_200 = {\n  class: \"row\"\n};\nconst _hoisted_201 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_202 = {\n  class: \"mb-3\"\n};\nconst _hoisted_203 = {\n  class: \"mb-0\"\n};\nconst _hoisted_204 = {\n  class: \"mb-3\"\n};\nconst _hoisted_205 = {\n  class: \"mb-0\"\n};\nconst _hoisted_206 = {\n  class: \"mb-3\"\n};\nconst _hoisted_207 = {\n  class: \"mb-0\"\n};\nconst _hoisted_208 = {\n  class: \"mb-3\"\n};\nconst _hoisted_209 = {\n  class: \"mb-0\"\n};\nconst _hoisted_210 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_211 = {\n  class: \"mb-3\"\n};\nconst _hoisted_212 = {\n  class: \"mb-0\"\n};\nconst _hoisted_213 = {\n  class: \"mb-3\"\n};\nconst _hoisted_214 = {\n  class: \"mb-0\"\n};\nconst _hoisted_215 = {\n  class: \"mb-3\"\n};\nconst _hoisted_216 = {\n  class: \"mb-0\"\n};\nconst _hoisted_217 = {\n  class: \"mb-3\"\n};\nconst _hoisted_218 = {\n  class: \"mb-0\"\n};\nconst _hoisted_219 = {\n  class: \"col-lg-4\"\n};\nconst _hoisted_220 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_221 = {\n  class: \"card-body\"\n};\nconst _hoisted_222 = {\n  class: \"mb-3\"\n};\nconst _hoisted_223 = [\"value\"];\nconst _hoisted_224 = {\n  class: \"mb-3\"\n};\nconst _hoisted_225 = {\n  class: \"d-grid gap-2\"\n};\nconst _hoisted_226 = [\"disabled\"];\nconst _hoisted_227 = [\"disabled\", \"title\"];\nconst _hoisted_228 = [\"disabled\", \"title\"];\nconst _hoisted_229 = {\n  key: 0,\n  class: \"mt-3 p-3 border rounded bg-light\"\n};\nconst _hoisted_230 = {\n  class: \"mb-3\"\n};\nconst _hoisted_231 = {\n  class: \"d-grid\"\n};\nconst _hoisted_232 = [\"disabled\"];\nconst _hoisted_233 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_234 = {\n  class: \"card-body\"\n};\nconst _hoisted_235 = {\n  class: \"mb-3\"\n};\nconst _hoisted_236 = {\n  class: \"mb-0\"\n};\nconst _hoisted_237 = {\n  class: \"mb-3\"\n};\nconst _hoisted_238 = {\n  class: \"mb-0\"\n};\nconst _hoisted_239 = {\n  class: \"row\"\n};\nconst _hoisted_240 = {\n  class: \"col-6\"\n};\nconst _hoisted_241 = {\n  class: \"mb-2\"\n};\nconst _hoisted_242 = {\n  class: \"mb-0\"\n};\nconst _hoisted_243 = {\n  class: \"col-6\"\n};\nconst _hoisted_244 = {\n  class: \"mb-2\"\n};\nconst _hoisted_245 = {\n  class: \"mb-0\"\n};\nconst _hoisted_246 = {\n  class: \"col-6\"\n};\nconst _hoisted_247 = {\n  class: \"mb-2\"\n};\nconst _hoisted_248 = {\n  class: \"mb-0\"\n};\nconst _hoisted_249 = {\n  class: \"col-6\"\n};\nconst _hoisted_250 = {\n  class: \"mb-2\"\n};\nconst _hoisted_251 = {\n  class: \"mb-0 fw-bold text-primary\"\n};\nconst _hoisted_252 = {\n  class: \"card\"\n};\nconst _hoisted_253 = {\n  class: \"card-body\"\n};\nconst _hoisted_254 = {\n  key: 0,\n  class: \"timeline\"\n};\nconst _hoisted_255 = {\n  class: \"timeline-content\"\n};\nconst _hoisted_256 = {\n  class: \"timeline-header\"\n};\nconst _hoisted_257 = {\n  class: \"text-muted ms-2\"\n};\nconst _hoisted_258 = {\n  class: \"timeline-body\"\n};\nconst _hoisted_259 = {\n  class: \"mb-1\"\n};\nconst _hoisted_260 = {\n  key: 0,\n  class: \"mb-1\"\n};\nconst _hoisted_261 = {\n  key: 1,\n  class: \"mb-0\"\n};\nconst _hoisted_262 = {\n  key: 1,\n  class: \"text-center text-muted py-3\"\n};\nconst _hoisted_263 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_264 = {\n  key: 3,\n  class: \"modal fade show d-block\",\n  tabindex: \"-1\",\n  style: {\n    \"background-color\": \"rgba(0,0,0,0.5)\"\n  }\n};\nconst _hoisted_265 = {\n  class: \"modal-dialog\"\n};\nconst _hoisted_266 = {\n  class: \"modal-content\"\n};\nconst _hoisted_267 = {\n  class: \"modal-header\"\n};\nconst _hoisted_268 = {\n  class: \"modal-body\"\n};\nconst _hoisted_269 = {\n  class: \"mb-3\"\n};\nconst _hoisted_270 = {\n  class: \"list-unstyled mt-2\"\n};\nconst _hoisted_271 = {\n  class: \"mb-3\"\n};\nconst _hoisted_272 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_273 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_274 = [\"disabled\"];\nconst _hoisted_275 = [\"disabled\"];\nconst _hoisted_276 = {\n  key: 0\n};\nconst _hoisted_277 = {\n  key: 1\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_AdminHeader = _resolveComponent(\"AdminHeader\");\n  const _component_AdminSidebar = _resolveComponent(\"AdminSidebar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_AdminHeader, {\n    userName: $data.adminData?.first_name || 'Admin',\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout\n  }, null, 8 /* PROPS */, [\"userName\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\"]), _createCommentVNode(\" Mobile Overlay \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"mobile-overlay\", {\n      active: !$data.sidebarCollapsed && $data.isMobile\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.closeMobileSidebar && $options.closeMobileSidebar(...args))\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_AdminSidebar, {\n    collapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onMenuChange: $options.handleMenuChange,\n    onLogout: $options.handleLogout,\n    onToggleSidebar: $options.handleSidebarToggle\n  }, null, 8 /* PROPS */, [\"collapsed\", \"activeMenu\", \"onMenuChange\", \"onLogout\", \"onToggleSidebar\"]), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[39] || (_cache[39] = [_createElementVNode(\"div\", {\n    class: \"spinner-border text-primary\",\n    role: \"status\"\n  }, [_createElementVNode(\"span\", {\n    class: \"visually-hidden\"\n  }, \"Loading...\")], -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Main Content \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" Error Message \"), $data.errorMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_cache[40] || (_cache[40] = _createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.errorMessage) + \" \", 1 /* TEXT */), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[1] || (_cache[1] = $event => $data.errorMessage = ''),\n    \"aria-label\": \"Close\"\n  })])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Page Header \"), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", null, [_createElementVNode(\"p\", _hoisted_9, [$data.lastRefresh ? (_openBlock(), _createElementBlock(\"span\", _hoisted_10, [_cache[41] || (_cache[41] = _createElementVNode(\"i\", {\n    class: \"fas fa-clock text-muted\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Last updated: \" + _toDisplayString($options.formatTime($data.lastRefresh)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_11, [_createCommentVNode(\" Real-time status indicator \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.autoRefreshEnabled ? 'bg-success' : 'bg-secondary'])\n  }, [$data.autoRefreshEnabled ? (_openBlock(), _createElementBlock(\"i\", _hoisted_13)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_14)), _createTextVNode(\" \" + _toDisplayString($data.autoRefreshEnabled ? 'Live' : 'Paused'), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary btn-sm\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.toggleAutoRefresh && $options.toggleAutoRefresh(...args)),\n    title: $data.autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas\", $data.autoRefreshEnabled ? 'fa-pause' : 'fa-play'])\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_15), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    onClick: _cache[3] || (_cache[3] = $event => $data.showFilters = !$data.showFilters)\n  }, [_cache[42] || (_cache[42] = _createElementVNode(\"i\", {\n    class: \"fas fa-filter me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.showFilters ? 'Hide' : 'Show') + \" Filters \", 1 /* TEXT */)]), _createCommentVNode(\" <button class=\\\"btn btn-success btn-sm\\\" @click=\\\"exportRequests\\\" :disabled=\\\"loading\\\">\\n                    <i class=\\\"fas fa-download me-1\\\"></i>\\n                    Export CSV\\n                  </button> \"), _createElementVNode(\"button\", {\n    class: \"btn btn-primary btn-sm\",\n    onClick: _cache[4] || (_cache[4] = (...args) => $options.refreshRequestsData && $options.refreshRequestsData(...args)),\n    disabled: $data.loading\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-sync-alt me-1\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */), _cache[43] || (_cache[43] = _createTextVNode(\" Refresh \"))], 8 /* PROPS */, _hoisted_16)])])])]), _createCommentVNode(\" Request Statistics \"), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[44] || (_cache[44] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-primary text-uppercase mb-1\"\n  }, \"Total Requests\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_23, _toDisplayString($data.requestStats.total || 0), 1 /* TEXT */)]), _cache[45] || (_cache[45] = _createElementVNode(\"i\", {\n    class: \"fas fa-file-alt fa-lg text-muted ms-2\"\n  }, null, -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_cache[46] || (_cache[46] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-warning text-uppercase mb-1\"\n  }, \"Pending\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_29, _toDisplayString($data.requestStats.pending || 0), 1 /* TEXT */)]), _cache[47] || (_cache[47] = _createElementVNode(\"i\", {\n    class: \"fas fa-clock fa-lg text-muted ms-2\"\n  }, null, -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, [_cache[48] || (_cache[48] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-success text-uppercase mb-1\"\n  }, \"Completed\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_35, _toDisplayString($data.requestStats.completed || 0), 1 /* TEXT */)]), _cache[49] || (_cache[49] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-circle fa-lg text-muted ms-2\"\n  }, null, -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_cache[50] || (_cache[50] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-info text-uppercase mb-1\"\n  }, \"Approved\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_41, _toDisplayString($data.requestStats.approved || 0), 1 /* TEXT */)]), _cache[51] || (_cache[51] = _createElementVNode(\"i\", {\n    class: \"fas fa-thumbs-up fa-lg text-muted ms-2\"\n  }, null, -1 /* HOISTED */))])])])])]), _createCommentVNode(\" Filters Panel \"), $data.showFilters ? (_openBlock(), _createElementBlock(\"div\", _hoisted_42, [_cache[61] || (_cache[61] = _createElementVNode(\"div\", {\n    class: \"card-header py-3\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"m-0 fw-bold text-primary\"\n  }, \"Filter Requests\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_cache[52] || (_cache[52] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Search\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filters.search = $event),\n    placeholder: \"Search by name, email, or request number\",\n    onKeyup: _cache[6] || (_cache[6] = _withKeys((...args) => $options.applyFilters && $options.applyFilters(...args), [\"enter\"]))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.filters.search]])]), _createElementVNode(\"div\", _hoisted_46, [_cache[54] || (_cache[54] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Status\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.filters.status = $event)\n  }, [_cache[53] || (_cache[53] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Statuses\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.statusOptions, status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.status_name\n    }, _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_47);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.filters.status]])]), _createElementVNode(\"div\", _hoisted_48, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Document Type\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.filters.document_type = $event)\n  }, _cache[55] || (_cache[55] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Types\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"barangay_clearance\"\n  }, \"Barangay Clearance\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"cedula\"\n  }, \"Cedula\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.filters.document_type]])]), _createElementVNode(\"div\", _hoisted_49, [_cache[57] || (_cache[57] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Date From\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.filters.date_from = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filters.date_from]])]), _createElementVNode(\"div\", _hoisted_50, [_cache[58] || (_cache[58] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Date To\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.filters.date_to = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filters.date_to]])]), _createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary btn-sm\",\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.applyFilters && $options.applyFilters(...args))\n  }, _cache[59] || (_cache[59] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\"\n  }, null, -1 /* HOISTED */)])), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary btn-sm\",\n    onClick: _cache[12] || (_cache[12] = (...args) => $options.clearFilters && $options.clearFilters(...args))\n  }, _cache[60] || (_cache[60] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* HOISTED */)]))])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Bulk Actions Panel \"), $data.selectedRequests.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_53, [_createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"h6\", _hoisted_55, [_cache[62] || (_cache[62] = _createElementVNode(\"i\", {\n    class: \"fas fa-tasks me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Bulk Actions (\" + _toDisplayString($data.selectedRequests.length) + \" selected) \", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [_cache[64] || (_cache[64] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Action\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.bulkAction = $event)\n  }, [_cache[63] || (_cache[63] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select Action\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.statusOptions, status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.id\n    }, \" Change to \" + _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_59);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.bulkAction]])]), _createElementVNode(\"div\", _hoisted_60, [_cache[65] || (_cache[65] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Reason (Optional)\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.bulkReason = $event),\n    placeholder: \"Enter reason for bulk action\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.bulkReason]])]), _createElementVNode(\"div\", _hoisted_61, [_createElementVNode(\"div\", _hoisted_62, [_createElementVNode(\"button\", {\n    class: \"btn btn-warning\",\n    onClick: _cache[15] || (_cache[15] = (...args) => $options.performBulkAction && $options.performBulkAction(...args)),\n    disabled: !$data.bulkAction\n  }, _cache[66] || (_cache[66] = [_createElementVNode(\"i\", {\n    class: \"fas fa-play me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Apply \")]), 8 /* PROPS */, _hoisted_63), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary\",\n    onClick: _cache[16] || (_cache[16] = $event => $data.selectedRequests = [])\n  }, _cache[67] || (_cache[67] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Cancel \")]))])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" View Toggle \"), _createElementVNode(\"div\", _hoisted_64, [_createElementVNode(\"div\", _hoisted_65, [_createElementVNode(\"div\", _hoisted_66, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    class: \"btn-check\",\n    name: \"viewMode\",\n    id: \"cardView\",\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.viewMode = $event),\n    value: \"card\",\n    autocomplete: \"off\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.viewMode]]), _cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    for: \"cardView\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-th-large me-1\"\n  }), _createTextVNode(\"Cards \")], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    class: \"btn-check\",\n    name: \"viewMode\",\n    id: \"tableView\",\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.viewMode = $event),\n    value: \"table\",\n    autocomplete: \"off\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.viewMode]]), _cache[69] || (_cache[69] = _createElementVNode(\"label\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    for: \"tableView\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-table me-1\"\n  }), _createTextVNode(\"Table \")], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"span\", _hoisted_68, \" Showing \" + _toDisplayString(($data.pagination.currentPage - 1) * $data.pagination.itemsPerPage + 1) + \" - \" + _toDisplayString(Math.min($data.pagination.currentPage * $data.pagination.itemsPerPage, $data.pagination.totalItems)) + \" of \" + _toDisplayString($data.pagination.totalItems) + \" requests \", 1 /* TEXT */), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    style: {\n      \"width\": \"auto\"\n    },\n    \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.pagination.itemsPerPage = $event),\n    onChange: _cache[20] || (_cache[20] = $event => $options.changeItemsPerPage($data.pagination.itemsPerPage))\n  }, _cache[70] || (_cache[70] = [_createElementVNode(\"option\", {\n    value: \"10\"\n  }, \"10 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"25\"\n  }, \"25 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"50\"\n  }, \"50 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"100\"\n  }, \"100 per page\", -1 /* HOISTED */)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.pagination.itemsPerPage]])])]), _createElementVNode(\"div\", _hoisted_69, [$data.requests.length > 0 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    class: \"btn btn-sm btn-outline-secondary\",\n    onClick: _cache[21] || (_cache[21] = (...args) => $options.selectAllRequests && $options.selectAllRequests(...args))\n  }, [_cache[71] || (_cache[71] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-square me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequests.length === $data.requests.length ? 'Deselect All' : 'Select All'), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Card View \"), $data.viewMode === 'card' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_70, [_createCommentVNode(\" Empty State \"), $data.requests.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_71, _cache[72] || (_cache[72] = [_createElementVNode(\"div\", {\n    class: \"empty-state-icon mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-inbox fa-4x text-muted\"\n  })], -1 /* HOISTED */), _createElementVNode(\"h5\", {\n    class: \"text-muted mb-2\"\n  }, \"No Document Requests Found\", -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"text-muted\"\n  }, \"There are no document requests matching your current filters.\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Request Cards \"), _createElementVNode(\"div\", _hoisted_72, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.requests, request => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: request.id,\n      class: \"col-xl-4 col-lg-6 col-md-6\"\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"request-card\", {\n        'selected': $data.selectedRequests.includes(request.id)\n      }])\n    }, [_createCommentVNode(\" Card Header \"), _createElementVNode(\"div\", _hoisted_73, [_createElementVNode(\"div\", _hoisted_74, [_createElementVNode(\"div\", _hoisted_75, [_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      class: \"form-check-input\",\n      checked: $data.selectedRequests.includes(request.id),\n      onChange: $event => $options.toggleRequestSelection(request.id)\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_76), _createElementVNode(\"div\", _hoisted_77, [_createElementVNode(\"span\", _hoisted_78, _toDisplayString(request.request_number), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_79, [_createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-outline-primary\",\n      onClick: $event => $options.viewRequestDetails(request.id),\n      title: \"View Details\"\n    }, [...(_cache[73] || (_cache[73] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_80), $options.canApprove(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      class: \"btn btn-sm btn-success\",\n      onClick: $event => $options.quickApprove(request),\n      title: \"Quick Approve\",\n      disabled: $data.loading\n    }, [...(_cache[74] || (_cache[74] = [_createElementVNode(\"i\", {\n      class: \"fas fa-check\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_81)) : _createCommentVNode(\"v-if\", true), $options.canReject(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 1,\n      class: \"btn btn-sm btn-danger\",\n      onClick: $event => $options.showQuickRejectModal(request),\n      title: \"Quick Reject\",\n      disabled: $data.loading\n    }, [...(_cache[75] || (_cache[75] = [_createElementVNode(\"i\", {\n      class: \"fas fa-times\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_82)) : _createCommentVNode(\"v-if\", true)])])]), _createCommentVNode(\" Card Body \"), _createElementVNode(\"div\", _hoisted_83, [_createCommentVNode(\" Client Info \"), _createElementVNode(\"div\", _hoisted_84, [_createElementVNode(\"div\", _hoisted_85, [_cache[76] || (_cache[76] = _createElementVNode(\"div\", {\n      class: \"client-avatar\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-user-circle fa-2x text-primary\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_createElementVNode(\"h6\", _hoisted_86, _toDisplayString(request.client_name), 1 /* TEXT */), _createElementVNode(\"small\", _hoisted_87, _toDisplayString(request.client_email), 1 /* TEXT */)])])]), _createCommentVNode(\" Document Type \"), _createElementVNode(\"div\", _hoisted_88, [_createElementVNode(\"div\", _hoisted_89, [_cache[77] || (_cache[77] = _createElementVNode(\"i\", {\n      class: \"fas fa-file-alt text-info\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_90, _toDisplayString(request.document_type), 1 /* TEXT */)])]), _createCommentVNode(\" Status and Amount \"), _createElementVNode(\"div\", _hoisted_91, [_createElementVNode(\"div\", _hoisted_92, [_createElementVNode(\"div\", _hoisted_93, [_createElementVNode(\"div\", _hoisted_94, [_cache[78] || (_cache[78] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Status\", -1 /* HOISTED */)), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor(request.status_name)}`])\n    }, _toDisplayString($options.formatStatus(request.status_name)), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_95, [_createElementVNode(\"div\", _hoisted_96, [_cache[79] || (_cache[79] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Amount\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_97, _toDisplayString($options.formatCurrency(request.total_fee)), 1 /* TEXT */)])])])]), _createCommentVNode(\" Date \"), _createElementVNode(\"div\", _hoisted_98, [_createElementVNode(\"small\", _hoisted_99, [_cache[80] || (_cache[80] = _createElementVNode(\"i\", {\n      class: \"fas fa-calendar-alt me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" Submitted \" + _toDisplayString($options.formatDate(request.requested_at)), 1 /* TEXT */)])])]), _createCommentVNode(\" Card Footer \"), _createElementVNode(\"div\", _hoisted_100, [_createElementVNode(\"div\", _hoisted_101, [_createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-outline-primary flex-fill\",\n      onClick: $event => $options.viewRequestDetails(request.id)\n    }, [...(_cache[81] || (_cache[81] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye me-1\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"View \")]))], 8 /* PROPS */, _hoisted_102), $options.canApprove(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      class: \"btn btn-sm btn-success\",\n      onClick: $event => $options.quickApprove(request),\n      title: 'Approve Request',\n      disabled: $data.loading\n    }, [...(_cache[82] || (_cache[82] = [_createElementVNode(\"i\", {\n      class: \"fas fa-check\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_103)) : _createCommentVNode(\"v-if\", true), $options.canReject(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 1,\n      class: \"btn btn-sm btn-danger\",\n      onClick: $event => $options.showQuickRejectModal(request),\n      title: 'Reject Request',\n      disabled: $data.loading\n    }, [...(_cache[83] || (_cache[83] = [_createElementVNode(\"i\", {\n      class: \"fas fa-times\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_104)) : _createCommentVNode(\"v-if\", true)])])], 2 /* CLASS */)]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 4\n  }, [_createCommentVNode(\" Table View \"), _createElementVNode(\"div\", _hoisted_105, [_createCommentVNode(\" Empty State \"), $data.requests.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_106, _cache[84] || (_cache[84] = [_createStaticVNode(\"<div class=\\\"empty-content\\\" data-v-1d5e65f3><div class=\\\"empty-icon\\\" data-v-1d5e65f3><i class=\\\"fas fa-inbox\\\" data-v-1d5e65f3></i></div><h6 class=\\\"empty-title\\\" data-v-1d5e65f3>No Document Requests Found</h6><p class=\\\"empty-text\\\" data-v-1d5e65f3>There are no document requests matching your current filters.</p></div>\", 1)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Modern Compact Table \"), _createElementVNode(\"div\", _hoisted_107, [_createCommentVNode(\" Table Header \"), _createElementVNode(\"div\", _hoisted_108, [_createElementVNode(\"div\", _hoisted_109, [_createElementVNode(\"input\", {\n    type: \"checkbox\",\n    class: \"form-check-input\",\n    checked: $data.selectedRequests.length === $data.requests.length && $data.requests.length > 0,\n    onChange: _cache[22] || (_cache[22] = (...args) => $options.selectAllRequests && $options.selectAllRequests(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_110)]), _cache[85] || (_cache[85] = _createStaticVNode(\"<div class=\\\"header-cell\\\" data-v-1d5e65f3>Request ID</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Client</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Document</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Status</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Amount</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Date</div><div class=\\\"header-cell\\\" data-v-1d5e65f3>Actions</div>\", 7))]), _createCommentVNode(\" Table Body \"), _createElementVNode(\"div\", _hoisted_111, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.requests, request => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: request.id,\n      class: _normalizeClass([\"compact-row\", {\n        'selected': $data.selectedRequests.includes(request.id)\n      }])\n    }, [_createCommentVNode(\" Selection \"), _createElementVNode(\"div\", _hoisted_112, [_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      class: \"form-check-input\",\n      checked: $data.selectedRequests.includes(request.id),\n      onChange: $event => $options.toggleRequestSelection(request.id)\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_113)]), _createCommentVNode(\" Request ID \"), _createElementVNode(\"div\", _hoisted_114, [_createElementVNode(\"div\", _hoisted_115, [_createElementVNode(\"span\", _hoisted_116, _toDisplayString(request.request_number), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_117, _toDisplayString(request.id), 1 /* TEXT */)])]), _createCommentVNode(\" Client \"), _createElementVNode(\"div\", _hoisted_118, [_createElementVNode(\"div\", _hoisted_119, [_cache[86] || (_cache[86] = _createElementVNode(\"div\", {\n      class: \"client-avatar-tiny\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-user\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_120, [_createElementVNode(\"div\", _hoisted_121, _toDisplayString(request.client_name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_122, _toDisplayString(request.client_email), 1 /* TEXT */)])])]), _createCommentVNode(\" Document Type \"), _createElementVNode(\"div\", _hoisted_123, [_createElementVNode(\"span\", _hoisted_124, [_cache[87] || (_cache[87] = _createElementVNode(\"i\", {\n      class: \"fas fa-file-alt\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString(request.document_type), 1 /* TEXT */)])]), _createCommentVNode(\" Status \"), _createElementVNode(\"div\", _hoisted_125, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"status-compact\", `status-${$options.getStatusColor(request.status_name)}`])\n    }, [_cache[88] || (_cache[88] = _createElementVNode(\"i\", {\n      class: \"fas fa-circle\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.formatStatus(request.status_name)), 1 /* TEXT */)], 2 /* CLASS */)]), _createCommentVNode(\" Amount \"), _createElementVNode(\"div\", _hoisted_126, [_createElementVNode(\"span\", _hoisted_127, _toDisplayString($options.formatCurrency(request.total_fee)), 1 /* TEXT */)]), _createCommentVNode(\" Date \"), _createElementVNode(\"div\", _hoisted_128, [_createElementVNode(\"div\", _hoisted_129, [_createElementVNode(\"span\", _hoisted_130, _toDisplayString($options.formatDate(request.requested_at)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_131, _toDisplayString($options.formatTime(request.requested_at)), 1 /* TEXT */)])]), _createCommentVNode(\" Actions \"), _createElementVNode(\"div\", _hoisted_132, [_createElementVNode(\"div\", _hoisted_133, [_createElementVNode(\"button\", {\n      class: \"action-btn-sm view-btn-sm\",\n      onClick: $event => $options.viewRequestDetails(request.id),\n      title: \"View Details\"\n    }, [...(_cache[89] || (_cache[89] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_134), $options.canApprove(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      class: \"action-btn-sm approve-btn-sm\",\n      onClick: $event => $options.quickApprove(request),\n      title: \"Quick Approve\",\n      disabled: $data.loading\n    }, [...(_cache[90] || (_cache[90] = [_createElementVNode(\"i\", {\n      class: \"fas fa-check\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_135)) : _createCommentVNode(\"v-if\", true), $options.canReject(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 1,\n      class: \"action-btn-sm reject-btn-sm\",\n      onClick: $event => $options.showQuickRejectModal(request),\n      title: \"Quick Reject\",\n      disabled: $data.loading\n    }, [...(_cache[91] || (_cache[91] = [_createElementVNode(\"i\", {\n      class: \"fas fa-times\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_136)) : _createCommentVNode(\"v-if\", true)])])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Pagination \"), $data.pagination.totalPages > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_137, [_createElementVNode(\"nav\", _hoisted_138, [_createElementVNode(\"ul\", _hoisted_139, [_createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.pagination.currentPage === 1\n    }])\n  }, [_createElementVNode(\"a\", {\n    class: \"page-link\",\n    href: \"#\",\n    onClick: _cache[23] || (_cache[23] = _withModifiers($event => $options.changePage($data.pagination.currentPage - 1), [\"prevent\"]))\n  }, _cache[92] || (_cache[92] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chevron-left\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(Math.min($data.pagination.totalPages, 10), page => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: page,\n      class: _normalizeClass([\"page-item\", {\n        active: page === $data.pagination.currentPage\n      }])\n    }, [_createElementVNode(\"a\", {\n      class: \"page-link\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.changePage(page), [\"prevent\"])\n    }, _toDisplayString(page), 9 /* TEXT, PROPS */, _hoisted_140)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.pagination.currentPage === $data.pagination.totalPages\n    }])\n  }, [_createElementVNode(\"a\", {\n    class: \"page-link\",\n    href: \"#\",\n    onClick: _cache[24] || (_cache[24] = _withModifiers($event => $options.changePage($data.pagination.currentPage + 1), [\"prevent\"]))\n  }, _cache[93] || (_cache[93] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chevron-right\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */)])])])) : _createCommentVNode(\"v-if\", true)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Request Details Modal \"), $data.showRequestDetails && $data.currentRequest ? (_openBlock(), _createElementBlock(\"div\", _hoisted_141, [_createElementVNode(\"div\", _hoisted_142, [_createElementVNode(\"div\", _hoisted_143, [_createElementVNode(\"div\", _hoisted_144, [_createElementVNode(\"h5\", _hoisted_145, [_cache[94] || (_cache[94] = _createElementVNode(\"i\", {\n    class: \"fas fa-file-alt me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Request Details - \" + _toDisplayString($data.currentRequest.request_number), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[25] || (_cache[25] = $event => $data.showRequestDetails = false)\n  })]), _createElementVNode(\"div\", _hoisted_146, [_createElementVNode(\"div\", _hoisted_147, [_createCommentVNode(\" Left Column - Request Information \"), _createElementVNode(\"div\", _hoisted_148, [_createCommentVNode(\" Basic Information \"), _createElementVNode(\"div\", _hoisted_149, [_cache[103] || (_cache[103] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-info-circle me-2\"\n  }), _createTextVNode(\"Request Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_150, [_createElementVNode(\"div\", _hoisted_151, [_createElementVNode(\"div\", _hoisted_152, [_createElementVNode(\"div\", _hoisted_153, [_cache[95] || (_cache[95] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Request Number\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_154, _toDisplayString($data.currentRequest.request_number), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_155, [_cache[96] || (_cache[96] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Document Type\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_156, [_createElementVNode(\"span\", _hoisted_157, _toDisplayString($data.currentRequest.document_type), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_158, [_cache[97] || (_cache[97] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Purpose Category\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_159, _toDisplayString($data.currentRequest.purpose_category), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_160, [_cache[98] || (_cache[98] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Purpose Details\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_161, _toDisplayString($data.currentRequest.purpose_details || 'Not specified'), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_162, [_createElementVNode(\"div\", _hoisted_163, [_cache[99] || (_cache[99] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Current Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_164, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor($data.currentRequest.status_name)}`])\n  }, _toDisplayString($options.formatStatus($data.currentRequest.status_name)), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_165, [_cache[100] || (_cache[100] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Priority\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_166, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.currentRequest.priority === 'high' ? 'bg-danger' : $data.currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'])\n  }, _toDisplayString($data.currentRequest.priority || 'Normal'), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_167, [_cache[101] || (_cache[101] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Delivery Method\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_168, _toDisplayString($data.currentRequest.delivery_method || 'Pickup'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_169, [_cache[102] || (_cache[102] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Date Submitted\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_170, _toDisplayString($options.formatDateTime($data.currentRequest.requested_at)), 1 /* TEXT */)])])])])]), _createCommentVNode(\" Client Information \"), _createElementVNode(\"div\", _hoisted_171, [_cache[108] || (_cache[108] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }), _createTextVNode(\"Client Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_172, [_createElementVNode(\"div\", _hoisted_173, [_createElementVNode(\"div\", _hoisted_174, [_createElementVNode(\"div\", _hoisted_175, [_cache[104] || (_cache[104] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Full Name\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_176, _toDisplayString($data.currentRequest.client_name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_177, [_cache[105] || (_cache[105] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Email Address\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_178, [_createElementVNode(\"a\", {\n    href: `mailto:${$data.currentRequest.client_email}`\n  }, _toDisplayString($data.currentRequest.client_email), 9 /* TEXT, PROPS */, _hoisted_179)])])]), _createElementVNode(\"div\", _hoisted_180, [_createElementVNode(\"div\", _hoisted_181, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Phone Number\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_182, [_createElementVNode(\"a\", {\n    href: `tel:${$data.currentRequest.client_phone}`\n  }, _toDisplayString($data.currentRequest.client_phone || 'Not provided'), 9 /* TEXT, PROPS */, _hoisted_183)])]), _createElementVNode(\"div\", _hoisted_184, [_cache[107] || (_cache[107] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Address\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_185, _toDisplayString($data.currentRequest.client_address || 'Not provided'), 1 /* TEXT */)])])])])]), _createCommentVNode(\" Document-Specific Details \"), $data.currentRequest.specific_details ? (_openBlock(), _createElementBlock(\"div\", _hoisted_186, [_cache[121] || (_cache[121] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clipboard-list me-2\"\n  }), _createTextVNode(\"Document-Specific Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_187, [_createCommentVNode(\" Barangay Clearance Details \"), $data.currentRequest.document_type === 'Barangay Clearance' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_188, [_createElementVNode(\"div\", _hoisted_189, [_createElementVNode(\"div\", _hoisted_190, [_createElementVNode(\"div\", _hoisted_191, [_cache[109] || (_cache[109] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Residency Period\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_192, _toDisplayString($data.currentRequest.specific_details.residency_period || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_193, [_cache[110] || (_cache[110] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Civil Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_194, _toDisplayString($data.currentRequest.specific_details.civil_status || 'Not specified'), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_195, [_createElementVNode(\"div\", _hoisted_196, [_cache[111] || (_cache[111] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Occupation\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_197, _toDisplayString($data.currentRequest.specific_details.occupation || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_198, [_cache[112] || (_cache[112] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Monthly Income\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_199, _toDisplayString($options.formatCurrency($data.currentRequest.specific_details.monthly_income) || 'Not specified'), 1 /* TEXT */)])])])])) : $data.currentRequest.document_type === 'Cedula' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Cedula Details \"), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_200, [_createElementVNode(\"div\", _hoisted_201, [_createElementVNode(\"div\", _hoisted_202, [_cache[113] || (_cache[113] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Birth Date\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_203, _toDisplayString($options.formatDate($data.currentRequest.specific_details.birth_date) || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_204, [_cache[114] || (_cache[114] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Birth Place\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_205, _toDisplayString($data.currentRequest.specific_details.birth_place || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_206, [_cache[115] || (_cache[115] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Civil Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_207, _toDisplayString($data.currentRequest.specific_details.civil_status || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_208, [_cache[116] || (_cache[116] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Citizenship\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_209, _toDisplayString($data.currentRequest.specific_details.citizenship || 'Not specified'), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_210, [_createElementVNode(\"div\", _hoisted_211, [_cache[117] || (_cache[117] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Occupation\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_212, _toDisplayString($data.currentRequest.specific_details.occupation || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_213, [_cache[118] || (_cache[118] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Annual Income\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_214, _toDisplayString($options.formatCurrency($data.currentRequest.specific_details.annual_income) || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_215, [_cache[119] || (_cache[119] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Tax Amount\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_216, _toDisplayString($options.formatCurrency($data.currentRequest.specific_details.tax_amount) || 'Not calculated'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_217, [_cache[120] || (_cache[120] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Interest/Penalty\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_218, _toDisplayString($options.formatCurrency($data.currentRequest.specific_details.interest_penalty) || 'None'), 1 /* TEXT */)])])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" Right Column - Status Management \"), _createElementVNode(\"div\", _hoisted_219, [_createCommentVNode(\" Status Management \"), _createElementVNode(\"div\", _hoisted_220, [_cache[130] || (_cache[130] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-tasks me-2\"\n  }), _createTextVNode(\"Status Management\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_221, [_createElementVNode(\"div\", _hoisted_222, [_cache[123] || (_cache[123] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Change Status\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.statusUpdateForm.status_id = $event)\n  }, [_cache[122] || (_cache[122] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select new status\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.statusOptions, status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.id\n    }, _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_223);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusUpdateForm.status_id]])]), _createElementVNode(\"div\", _hoisted_224, [_cache[124] || (_cache[124] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Reason/Notes\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    class: \"form-control\",\n    rows: \"3\",\n    \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.statusUpdateForm.reason = $event),\n    placeholder: \"Enter reason for status change (optional)\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.statusUpdateForm.reason]])]), _createElementVNode(\"div\", _hoisted_225, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary\",\n    onClick: _cache[28] || (_cache[28] = (...args) => $options.updateRequestStatusFromModal && $options.updateRequestStatusFromModal(...args)),\n    disabled: !$data.statusUpdateForm.status_id\n  }, _cache[125] || (_cache[125] = [_createElementVNode(\"i\", {\n    class: \"fas fa-save me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Update Status \")]), 8 /* PROPS */, _hoisted_226), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"btn btn-success\", {\n      'btn-outline-success': !$options.canApprove($data.currentRequest)\n    }]),\n    onClick: _cache[29] || (_cache[29] = (...args) => $options.approveRequestFromModal && $options.approveRequestFromModal(...args)),\n    disabled: !$options.canApprove($data.currentRequest),\n    title: $options.canApprove($data.currentRequest) ? 'Approve this request' : $options.getStatusExplanation($data.currentRequest, 'approve')\n  }, _cache[126] || (_cache[126] = [_createElementVNode(\"i\", {\n    class: \"fas fa-check me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Quick Approve \")]), 10 /* CLASS, PROPS */, _hoisted_227), _createElementVNode(\"button\", {\n    class: _normalizeClass([\"btn btn-danger\", {\n      'btn-outline-danger': !$options.canReject($data.currentRequest)\n    }]),\n    onClick: _cache[30] || (_cache[30] = $event => $data.showRejectForm = !$data.showRejectForm),\n    disabled: !$options.canReject($data.currentRequest),\n    title: $options.canReject($data.currentRequest) ? 'Reject this request' : $options.getStatusExplanation($data.currentRequest, 'reject')\n  }, [_cache[127] || (_cache[127] = _createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.showRejectForm ? 'Cancel' : 'Reject Request'), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_228)]), _createCommentVNode(\" Rejection Form \"), $data.showRejectForm ? (_openBlock(), _createElementBlock(\"div\", _hoisted_229, [_createElementVNode(\"div\", _hoisted_230, [_cache[128] || (_cache[128] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold text-danger\"\n  }, \"Rejection Reason *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    class: \"form-control\",\n    rows: \"3\",\n    \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.rejectForm.reason = $event),\n    placeholder: \"Please provide a detailed reason for rejection\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.rejectForm.reason]])]), _createElementVNode(\"div\", _hoisted_231, [_createElementVNode(\"button\", {\n    class: \"btn btn-danger\",\n    onClick: _cache[32] || (_cache[32] = (...args) => $options.rejectRequestFromModal && $options.rejectRequestFromModal(...args)),\n    disabled: !$data.rejectForm.reason || $data.rejectForm.reason.trim() === ''\n  }, _cache[129] || (_cache[129] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Confirm Rejection \")]), 8 /* PROPS */, _hoisted_232)])])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Payment Information \"), _createElementVNode(\"div\", _hoisted_233, [_cache[137] || (_cache[137] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-credit-card me-2\"\n  }), _createTextVNode(\"Payment Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_234, [_createElementVNode(\"div\", _hoisted_235, [_cache[131] || (_cache[131] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Payment Method\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_236, _toDisplayString($data.currentRequest.payment_method || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_237, [_cache[132] || (_cache[132] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Payment Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_238, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.currentRequest.payment_status === 'paid' ? 'bg-success' : $data.currentRequest.payment_status === 'pending' ? 'bg-warning' : 'bg-secondary'])\n  }, _toDisplayString($data.currentRequest.payment_status || 'Unpaid'), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_239, [_createElementVNode(\"div\", _hoisted_240, [_createElementVNode(\"div\", _hoisted_241, [_cache[133] || (_cache[133] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Base Fee\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_242, _toDisplayString($options.formatCurrency($data.currentRequest.base_fee)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_243, [_createElementVNode(\"div\", _hoisted_244, [_cache[134] || (_cache[134] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Additional Fees\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_245, _toDisplayString($options.formatCurrency($data.currentRequest.additional_fees)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_246, [_createElementVNode(\"div\", _hoisted_247, [_cache[135] || (_cache[135] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Processing Fee\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_248, _toDisplayString($options.formatCurrency($data.currentRequest.processing_fee)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_249, [_createElementVNode(\"div\", _hoisted_250, [_cache[136] || (_cache[136] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Total Amount\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_251, _toDisplayString($options.formatCurrency($data.currentRequest.total_fee)), 1 /* TEXT */)])])])])])])]), _createCommentVNode(\" Status History Timeline \"), _createElementVNode(\"div\", _hoisted_252, [_cache[143] || (_cache[143] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-history me-2\"\n  }), _createTextVNode(\"Status History\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_253, [$data.currentRequest.status_history && $data.currentRequest.status_history.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_254, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.currentRequest.status_history, (history, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: history.id,\n      class: _normalizeClass([\"timeline-item\", {\n        'timeline-item-last': index === $data.currentRequest.status_history.length - 1\n      }])\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"timeline-marker\", `bg-${$options.getStatusColor(history.new_status_name)}`])\n    }, [...(_cache[138] || (_cache[138] = [_createElementVNode(\"i\", {\n      class: \"fas fa-circle\"\n    }, null, -1 /* HOISTED */)]))], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_255, [_createElementVNode(\"div\", _hoisted_256, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor(history.new_status_name)}`])\n    }, _toDisplayString($options.formatStatus(history.new_status_name)), 3 /* TEXT, CLASS */), _createElementVNode(\"small\", _hoisted_257, _toDisplayString($options.formatDateTime(history.changed_at)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_258, [_createElementVNode(\"p\", _hoisted_259, [_cache[139] || (_cache[139] = _createElementVNode(\"strong\", null, \"Changed by:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString(history.changed_by_name), 1 /* TEXT */)]), history.old_status_name ? (_openBlock(), _createElementBlock(\"p\", _hoisted_260, [_cache[140] || (_cache[140] = _createElementVNode(\"strong\", null, \"From:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.formatStatus(history.old_status_name)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), history.change_reason ? (_openBlock(), _createElementBlock(\"p\", _hoisted_261, [_cache[141] || (_cache[141] = _createElementVNode(\"strong\", null, \"Reason:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString(history.change_reason), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_262, _cache[142] || (_cache[142] = [_createElementVNode(\"i\", {\n    class: \"fas fa-history fa-2x mb-2\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", null, \"No status history available\", -1 /* HOISTED */)])))])])]), _createElementVNode(\"div\", _hoisted_263, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    onClick: _cache[33] || (_cache[33] = $event => $data.showRequestDetails = false)\n  }, _cache[144] || (_cache[144] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Close \")])), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[34] || (_cache[34] = (...args) => $options.refreshRequestDetails && $options.refreshRequestDetails(...args))\n  }, _cache[145] || (_cache[145] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sync-alt me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Refresh \")]))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Quick Reject Modal \"), $data.showQuickReject && $data.selectedRequestForReject ? (_openBlock(), _createElementBlock(\"div\", _hoisted_264, [_createElementVNode(\"div\", _hoisted_265, [_createElementVNode(\"div\", _hoisted_266, [_createElementVNode(\"div\", _hoisted_267, [_cache[146] || (_cache[146] = _createElementVNode(\"h5\", {\n    class: \"modal-title\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-times-circle text-danger me-2\"\n  }), _createTextVNode(\" Reject Request \")], -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[35] || (_cache[35] = (...args) => $options.closeQuickRejectModal && $options.closeQuickRejectModal(...args))\n  })]), _createElementVNode(\"div\", _hoisted_268, [_cache[153] || (_cache[153] = _createElementVNode(\"div\", {\n    class: \"alert alert-warning\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2\"\n  }), _createTextVNode(\" You are about to reject this document request. This action will notify the client immediately. \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_269, [_cache[150] || (_cache[150] = _createElementVNode(\"strong\", null, \"Request Details:\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_270, [_createElementVNode(\"li\", null, [_cache[147] || (_cache[147] = _createElementVNode(\"strong\", null, \"Request Number:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.request_number), 1 /* TEXT */)]), _createElementVNode(\"li\", null, [_cache[148] || (_cache[148] = _createElementVNode(\"strong\", null, \"Document Type:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.document_type), 1 /* TEXT */)]), _createElementVNode(\"li\", null, [_cache[149] || (_cache[149] = _createElementVNode(\"strong\", null, \"Client:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.client_name), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_271, [_cache[151] || (_cache[151] = _createElementVNode(\"label\", {\n    for: \"rejectionReason\",\n    class: \"form-label\"\n  }, [_createElementVNode(\"strong\", null, [_createTextVNode(\"Rejection Reason \"), _createElementVNode(\"span\", {\n    class: \"text-danger\"\n  }, \"*\")])], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"rejectionReason\",\n    \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.quickRejectForm.reason = $event),\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.quickRejectForm.error\n    }]),\n    rows: \"4\",\n    placeholder: \"Please provide a clear reason for rejecting this request...\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.quickRejectForm.reason]]), $data.quickRejectForm.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_272, _toDisplayString($data.quickRejectForm.error), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _cache[152] || (_cache[152] = _createElementVNode(\"div\", {\n    class: \"form-text\"\n  }, \" This reason will be visible to the client and included in their notification. \", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_273, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    onClick: _cache[37] || (_cache[37] = (...args) => $options.closeQuickRejectModal && $options.closeQuickRejectModal(...args)),\n    disabled: $data.quickRejectForm.loading\n  }, _cache[154] || (_cache[154] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Cancel \")]), 8 /* PROPS */, _hoisted_274), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-danger\",\n    onClick: _cache[38] || (_cache[38] = (...args) => $options.confirmQuickReject && $options.confirmQuickReject(...args)),\n    disabled: $data.quickRejectForm.loading || !$data.quickRejectForm.reason.trim()\n  }, [_cache[156] || (_cache[156] = _createElementVNode(\"i\", {\n    class: \"fas fa-times-circle me-1\"\n  }, null, -1 /* HOISTED */)), $data.quickRejectForm.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_276, _cache[155] || (_cache[155] = [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Rejecting... \")]))) : (_openBlock(), _createElementBlock(\"span\", _hoisted_277, \"Reject Request\"))], 8 /* PROPS */, _hoisted_275)])])])])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])]);\n}", "map": {"version": 3, "names": ["class", "style", "role", "tabindex", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_AdminHeader", "userName", "$data", "adminData", "first_name", "showUserDropdown", "sidebarCollapsed", "activeMenu", "$options", "onSidebarToggle", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "active", "isMobile", "onClick", "_cache", "args", "closeMobileSidebar", "_hoisted_2", "_component_AdminSidebar", "collapsed", "onMenuChange", "handleMenuChange", "onToggleSidebar", "loading", "_hoisted_3", "_Fragment", "key", "_hoisted_4", "errorMessage", "_hoisted_5", "_toDisplayString", "type", "$event", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "lastRefresh", "_hoisted_10", "formatTime", "_hoisted_11", "_hoisted_12", "autoRefreshEnabled", "_hoisted_13", "_hoisted_14", "toggleAutoRefresh", "title", "showFilters", "refreshRequestsData", "disabled", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "requestStats", "total", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "pending", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "completed", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "approved", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "filters", "search", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "applyFilters", "_hoisted_46", "status", "value", "_renderList", "statusOptions", "id", "status_name", "formatStatus", "_hoisted_47", "_hoisted_48", "document_type", "_hoisted_49", "date_from", "_hoisted_50", "date_to", "_hoisted_51", "_hoisted_52", "clearFilters", "selectedRequests", "length", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "bulkAction", "_hoisted_59", "_hoisted_60", "bulkReason", "_hoisted_61", "_hoisted_62", "performBulkAction", "_hoisted_64", "_hoisted_65", "_hoisted_66", "name", "viewMode", "autocomplete", "for", "_hoisted_67", "_hoisted_68", "pagination", "currentPage", "itemsPerPage", "Math", "min", "totalItems", "onChange", "changeItemsPerPage", "_hoisted_69", "requests", "selectAllRequests", "_hoisted_70", "_hoisted_71", "_hoisted_72", "request", "includes", "_hoisted_73", "_hoisted_74", "_hoisted_75", "checked", "toggleRequestSelection", "_hoisted_77", "_hoisted_78", "request_number", "_hoisted_79", "viewRequestDetails", "canApprove", "quickApprove", "canReject", "showQuickRejectModal", "_hoisted_83", "_hoisted_84", "_hoisted_85", "_hoisted_86", "client_name", "_hoisted_87", "client_email", "_hoisted_88", "_hoisted_89", "_hoisted_90", "_hoisted_91", "_hoisted_92", "_hoisted_93", "_hoisted_94", "getStatusColor", "_hoisted_95", "_hoisted_96", "_hoisted_97", "formatCurrency", "total_fee", "_hoisted_98", "_hoisted_99", "formatDate", "requested_at", "_hoisted_100", "_hoisted_101", "_hoisted_105", "_hoisted_106", "_hoisted_107", "_hoisted_108", "_hoisted_109", "_hoisted_111", "_hoisted_112", "_hoisted_114", "_hoisted_115", "_hoisted_116", "_hoisted_117", "_hoisted_118", "_hoisted_119", "_hoisted_120", "_hoisted_121", "_hoisted_122", "_hoisted_123", "_hoisted_124", "_hoisted_125", "_hoisted_126", "_hoisted_127", "_hoisted_128", "_hoisted_129", "_hoisted_130", "_hoisted_131", "_hoisted_132", "_hoisted_133", "totalPages", "_hoisted_137", "_hoisted_138", "_hoisted_139", "href", "_withModifiers", "changePage", "page", "_hoisted_140", "showRequestDetails", "currentRequest", "_hoisted_141", "_hoisted_142", "_hoisted_143", "_hoisted_144", "_hoisted_145", "_hoisted_146", "_hoisted_147", "_hoisted_148", "_hoisted_149", "_hoisted_150", "_hoisted_151", "_hoisted_152", "_hoisted_153", "_hoisted_154", "_hoisted_155", "_hoisted_156", "_hoisted_157", "_hoisted_158", "_hoisted_159", "purpose_category", "_hoisted_160", "_hoisted_161", "purpose_details", "_hoisted_162", "_hoisted_163", "_hoisted_164", "_hoisted_165", "_hoisted_166", "priority", "_hoisted_167", "_hoisted_168", "delivery_method", "_hoisted_169", "_hoisted_170", "formatDateTime", "_hoisted_171", "_hoisted_172", "_hoisted_173", "_hoisted_174", "_hoisted_175", "_hoisted_176", "_hoisted_177", "_hoisted_178", "_hoisted_179", "_hoisted_180", "_hoisted_181", "_hoisted_182", "client_phone", "_hoisted_183", "_hoisted_184", "_hoisted_185", "client_address", "specific_details", "_hoisted_186", "_hoisted_187", "_hoisted_188", "_hoisted_189", "_hoisted_190", "_hoisted_191", "_hoisted_192", "residency_period", "_hoisted_193", "_hoisted_194", "civil_status", "_hoisted_195", "_hoisted_196", "_hoisted_197", "occupation", "_hoisted_198", "_hoisted_199", "monthly_income", "_hoisted_200", "_hoisted_201", "_hoisted_202", "_hoisted_203", "birth_date", "_hoisted_204", "_hoisted_205", "birth_place", "_hoisted_206", "_hoisted_207", "_hoisted_208", "_hoisted_209", "citizenship", "_hoisted_210", "_hoisted_211", "_hoisted_212", "_hoisted_213", "_hoisted_214", "annual_income", "_hoisted_215", "_hoisted_216", "tax_amount", "_hoisted_217", "_hoisted_218", "interest_penalty", "_hoisted_219", "_hoisted_220", "_hoisted_221", "_hoisted_222", "statusUpdateForm", "status_id", "_hoisted_223", "_hoisted_224", "rows", "reason", "_hoisted_225", "updateRequestStatusFromModal", "approveRequestFromModal", "getStatusExplanation", "showRejectForm", "_hoisted_229", "_hoisted_230", "rejectForm", "required", "_hoisted_231", "rejectRequestFromModal", "trim", "_hoisted_233", "_hoisted_234", "_hoisted_235", "_hoisted_236", "payment_method", "_hoisted_237", "_hoisted_238", "payment_status", "_hoisted_239", "_hoisted_240", "_hoisted_241", "_hoisted_242", "base_fee", "_hoisted_243", "_hoisted_244", "_hoisted_245", "additional_fees", "_hoisted_246", "_hoisted_247", "_hoisted_248", "processing_fee", "_hoisted_249", "_hoisted_250", "_hoisted_251", "_hoisted_252", "_hoisted_253", "status_history", "_hoisted_254", "history", "index", "new_status_name", "_hoisted_255", "_hoisted_256", "_hoisted_257", "changed_at", "_hoisted_258", "_hoisted_259", "changed_by_name", "old_status_name", "_hoisted_260", "change_reason", "_hoisted_261", "_hoisted_262", "_hoisted_263", "refreshRequestDetails", "showQuickReject", "selectedRequestForReject", "_hoisted_264", "_hoisted_265", "_hoisted_266", "_hoisted_267", "closeQuickRejectModal", "_hoisted_268", "_hoisted_269", "_hoisted_270", "_hoisted_271", "quickRejectForm", "error", "_hoisted_272", "_hoisted_273", "confirmQuickReject", "_hoisted_276", "_hoisted_277"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRequests.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-requests\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <!-- Loading State -->\n        <div v-if=\"loading\" class=\"d-flex justify-content-center align-items-center\" style=\"min-height: 400px;\">\n          <div class=\"spinner-border text-primary\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n\n        <!-- Main Content -->\n        <div v-else class=\"container-fluid py-4\">\n          <!-- Error Message -->\n          <div v-if=\"errorMessage\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ errorMessage }}\n            <button type=\"button\" class=\"btn-close\" @click=\"errorMessage = ''\" aria-label=\"Close\"></button>\n          </div>\n\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n                <div>\n                  <p class=\"text-muted mb-0\">\n                    <span v-if=\"lastRefresh\" class=\"ms-2 small\">\n                      <i class=\"fas fa-clock text-muted\"></i>\n                      Last updated: {{ formatTime(lastRefresh) }}\n                    </span>\n                  </p>\n                </div>\n                <div class=\"d-flex gap-2 align-items-center\">\n                  <!-- Real-time status indicator -->\n                  <div class=\"real-time-status me-2\">\n                    <span class=\"badge\" :class=\"autoRefreshEnabled ? 'bg-success' : 'bg-secondary'\">\n                      <i class=\"fas fa-circle pulse\" v-if=\"autoRefreshEnabled\"></i>\n                      <i class=\"fas fa-pause\" v-else></i>\n                      {{ autoRefreshEnabled ? 'Live' : 'Paused' }}\n                    </span>\n                  </div>\n\n                  <button class=\"btn btn-outline-secondary btn-sm\" @click=\"toggleAutoRefresh\" :title=\"autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'\">\n                    <i class=\"fas\" :class=\"autoRefreshEnabled ? 'fa-pause' : 'fa-play'\"></i>\n                  </button>\n                  <button class=\"btn btn-outline-primary btn-sm\" @click=\"showFilters = !showFilters\">\n                    <i class=\"fas fa-filter me-1\"></i>\n                    {{ showFilters ? 'Hide' : 'Show' }} Filters\n                  </button>\n                  <!-- <button class=\"btn btn-success btn-sm\" @click=\"exportRequests\" :disabled=\"loading\">\n                    <i class=\"fas fa-download me-1\"></i>\n                    Export CSV\n                  </button> -->\n                  <button class=\"btn btn-primary btn-sm\" @click=\"refreshRequestsData\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Request Statistics -->\n          <div class=\"row mb-3\">\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-primary shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-primary text-uppercase mb-1\">Total Requests</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.total || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-file-alt fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-warning shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-warning text-uppercase mb-1\">Pending</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.pending || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-clock fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-success shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-success text-uppercase mb-1\">Completed</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.completed || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-check-circle fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-info shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-info text-uppercase mb-1\">Approved</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.approved || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-thumbs-up fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Filters Panel -->\n          <div v-if=\"showFilters\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3\">\n              <h6 class=\"m-0 fw-bold text-primary\">Filter Requests</h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Search</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    v-model=\"filters.search\"\n                    placeholder=\"Search by name, email, or request number\"\n                    @keyup.enter=\"applyFilters\"\n                  >\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Status</label>\n                  <select class=\"form-select\" v-model=\"filters.status\">\n                    <option value=\"\">All Statuses</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.status_name\">\n                      {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Document Type</label>\n                  <select class=\"form-select\" v-model=\"filters.document_type\">\n                    <option value=\"\">All Types</option>\n                    <option value=\"barangay_clearance\">Barangay Clearance</option>\n                    <option value=\"cedula\">Cedula</option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date From</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_from\">\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date To</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_to\">\n                </div>\n                <div class=\"col-md-1 mb-3 d-flex align-items-end\">\n                  <div class=\"d-flex gap-1 w-100\">\n                    <button class=\"btn btn-primary btn-sm\" @click=\"applyFilters\">\n                      <i class=\"fas fa-search\"></i>\n                    </button>\n                    <button class=\"btn btn-outline-secondary btn-sm\" @click=\"clearFilters\">\n                      <i class=\"fas fa-times\"></i>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bulk Actions Panel -->\n          <div v-if=\"selectedRequests.length > 0\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3 bg-warning\">\n              <h6 class=\"m-0 fw-bold text-dark\">\n                <i class=\"fas fa-tasks me-2\"></i>\n                Bulk Actions ({{ selectedRequests.length }} selected)\n              </h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row align-items-end\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Action</label>\n                  <select class=\"form-select\" v-model=\"bulkAction\">\n                    <option value=\"\">Select Action</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.id\">\n                      Change to {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label class=\"form-label\">Reason (Optional)</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    v-model=\"bulkReason\"\n                    placeholder=\"Enter reason for bulk action\"\n                  >\n                </div>\n                <div class=\"col-md-3 mb-3\">\n                  <div class=\"d-flex gap-2\">\n                    <button class=\"btn btn-warning\" @click=\"performBulkAction\" :disabled=\"!bulkAction\">\n                      <i class=\"fas fa-play me-1\"></i>\n                      Apply\n                    </button>\n                    <button class=\"btn btn-outline-secondary\" @click=\"selectedRequests = []\">\n                      <i class=\"fas fa-times me-1\"></i>\n                      Cancel\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- View Toggle -->\n          <div class=\"d-flex justify-content-between align-items-center mb-4\">\n            <div class=\"d-flex align-items-center gap-3\">\n              <div class=\"btn-group\" role=\"group\" aria-label=\"View toggle\">\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"cardView\" v-model=\"viewMode\" value=\"card\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"cardView\">\n                  <i class=\"fas fa-th-large me-1\"></i>Cards\n                </label>\n\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"tableView\" v-model=\"viewMode\" value=\"table\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"tableView\">\n                  <i class=\"fas fa-table me-1\"></i>Table\n                </label>\n              </div>\n\n              <div class=\"d-flex align-items-center gap-2\">\n                <span class=\"text-muted small\">\n                  Showing {{ ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1 }} -\n                  {{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}\n                  of {{ pagination.totalItems }} requests\n                </span>\n                <select class=\"form-select form-select-sm\" style=\"width: auto;\" v-model=\"pagination.itemsPerPage\" @change=\"changeItemsPerPage(pagination.itemsPerPage)\">\n                  <option value=\"10\">10 per page</option>\n                  <option value=\"25\">25 per page</option>\n                  <option value=\"50\">50 per page</option>\n                  <option value=\"100\">100 per page</option>\n                </select>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-items-center gap-2\">\n              <button class=\"btn btn-sm btn-outline-secondary\" @click=\"selectAllRequests\" v-if=\"requests.length > 0\">\n                <i class=\"fas fa-check-square me-1\"></i>\n                {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}\n              </button>\n            </div>\n          </div>\n\n          <!-- Card View -->\n          <div v-if=\"viewMode === 'card'\" class=\"requests-grid\">\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"empty-state text-center py-5\">\n              <div class=\"empty-state-icon mb-3\">\n                <i class=\"fas fa-inbox fa-4x text-muted\"></i>\n              </div>\n              <h5 class=\"text-muted mb-2\">No Document Requests Found</h5>\n              <p class=\"text-muted\">There are no document requests matching your current filters.</p>\n            </div>\n\n            <!-- Request Cards -->\n            <div v-else class=\"row g-4\">\n              <div v-for=\"request in requests\" :key=\"request.id\" class=\"col-xl-4 col-lg-6 col-md-6\">\n                <div class=\"request-card\" :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n                  <!-- Card Header -->\n                  <div class=\"request-card-header\">\n                    <div class=\"d-flex justify-content-between align-items-start\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <input\n                          type=\"checkbox\"\n                          class=\"form-check-input\"\n                          :checked=\"selectedRequests.includes(request.id)\"\n                          @change=\"toggleRequestSelection(request.id)\"\n                        >\n                        <div class=\"request-number\">\n                          <span class=\"badge bg-primary\">{{ request.request_number }}</span>\n                        </div>\n                      </div>\n                      <div class=\"request-actions-simple\">\n                        <button class=\"btn btn-sm btn-outline-primary\" @click=\"viewRequestDetails(request.id)\" title=\"View Details\">\n                          <i class=\"fas fa-eye\"></i>\n                        </button>\n                        <button\n                          v-if=\"canApprove(request)\"\n                          class=\"btn btn-sm btn-success\"\n                          @click=\"quickApprove(request)\"\n                          title=\"Quick Approve\"\n                          :disabled=\"loading\"\n                        >\n                          <i class=\"fas fa-check\"></i>\n                        </button>\n                        <button\n                          v-if=\"canReject(request)\"\n                          class=\"btn btn-sm btn-danger\"\n                          @click=\"showQuickRejectModal(request)\"\n                          title=\"Quick Reject\"\n                          :disabled=\"loading\"\n                        >\n                          <i class=\"fas fa-times\"></i>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Card Body -->\n                  <div class=\"request-card-body\">\n                    <!-- Client Info -->\n                    <div class=\"client-info mb-3\">\n                      <div class=\"d-flex align-items-center gap-2 mb-2\">\n                        <div class=\"client-avatar\">\n                          <i class=\"fas fa-user-circle fa-2x text-primary\"></i>\n                        </div>\n                        <div>\n                          <h6 class=\"mb-0 fw-bold\">{{ request.client_name }}</h6>\n                          <small class=\"text-muted\">{{ request.client_email }}</small>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Document Type -->\n                    <div class=\"document-type mb-3\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <i class=\"fas fa-file-alt text-info\"></i>\n                        <span class=\"badge bg-info-subtle text-info-emphasis px-3 py-2\">\n                          {{ request.document_type }}\n                        </span>\n                      </div>\n                    </div>\n\n                    <!-- Status and Amount -->\n                    <div class=\"request-meta mb-3\">\n                      <div class=\"row g-2\">\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Status</small>\n                            <span class=\"badge\" :class=\"`bg-${getStatusColor(request.status_name)}`\">\n                              {{ formatStatus(request.status_name) }}\n                            </span>\n                          </div>\n                        </div>\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Amount</small>\n                            <span class=\"fw-bold text-success\">{{ formatCurrency(request.total_fee) }}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Date -->\n                    <div class=\"request-date\">\n                      <small class=\"text-muted\">\n                        <i class=\"fas fa-calendar-alt me-1\"></i>\n                        Submitted {{ formatDate(request.requested_at) }}\n                      </small>\n                    </div>\n                  </div>\n\n                  <!-- Card Footer -->\n                  <div class=\"request-card-footer\">\n                    <div class=\"d-flex gap-2\">\n                      <button class=\"btn btn-sm btn-outline-primary flex-fill\" @click=\"viewRequestDetails(request.id)\">\n                        <i class=\"fas fa-eye me-1\"></i>View\n                      </button>\n                      <button\n                        v-if=\"canApprove(request)\"\n                        class=\"btn btn-sm btn-success\"\n                        @click=\"quickApprove(request)\"\n                        :title=\"'Approve Request'\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-check\"></i>\n                      </button>\n                      <button\n                        v-if=\"canReject(request)\"\n                        class=\"btn btn-sm btn-danger\"\n                        @click=\"showQuickRejectModal(request)\"\n                        :title=\"'Reject Request'\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Table View -->\n          <div v-else class=\"modern-table-container\">\n\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"modern-table-empty\">\n              <div class=\"empty-content\">\n                <div class=\"empty-icon\">\n                  <i class=\"fas fa-inbox\"></i>\n                </div>\n                <h6 class=\"empty-title\">No Document Requests Found</h6>\n                <p class=\"empty-text\">There are no document requests matching your current filters.</p>\n              </div>\n            </div>\n\n            <!-- Modern Compact Table -->\n            <div v-else class=\"compact-table-wrapper\">\n              <!-- Table Header -->\n              <div class=\"compact-table-header\">\n                <div class=\"header-cell selection-header\">\n                  <input\n                    type=\"checkbox\"\n                    class=\"form-check-input\"\n                    :checked=\"selectedRequests.length === requests.length && requests.length > 0\"\n                    @change=\"selectAllRequests\"\n                  >\n                </div>\n                <div class=\"header-cell\">Request ID</div>\n                <div class=\"header-cell\">Client</div>\n                <div class=\"header-cell\">Document</div>\n                <div class=\"header-cell\">Status</div>\n                <div class=\"header-cell\">Amount</div>\n                <div class=\"header-cell\">Date</div>\n                <div class=\"header-cell\">Actions</div>\n              </div>\n\n              <!-- Table Body -->\n              <div class=\"compact-table-body\">\n                <div v-for=\"request in requests\" :key=\"request.id\"\n                     class=\"compact-row\"\n                     :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n\n                  <!-- Selection -->\n                  <div class=\"row-cell selection-cell\">\n                    <input\n                      type=\"checkbox\"\n                      class=\"form-check-input\"\n                      :checked=\"selectedRequests.includes(request.id)\"\n                      @change=\"toggleRequestSelection(request.id)\"\n                    >\n                  </div>\n\n                  <!-- Request ID -->\n                  <div class=\"row-cell request-id-cell\">\n                    <div class=\"request-id-content\">\n                      <span class=\"request-number\">{{ request.request_number }}</span>\n                      <span class=\"request-id-small\">{{ request.id }}</span>\n                    </div>\n                  </div>\n\n                  <!-- Client -->\n                  <div class=\"row-cell client-cell\">\n                    <div class=\"client-compact\">\n                      <div class=\"client-avatar-tiny\">\n                        <i class=\"fas fa-user\"></i>\n                      </div>\n                      <div class=\"client-info-compact\">\n                        <div class=\"client-name-compact\">{{ request.client_name }}</div>\n                        <div class=\"client-email-compact\">{{ request.client_email }}</div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Document Type -->\n                  <div class=\"row-cell document-cell\">\n                    <span class=\"document-badge\">\n                      <i class=\"fas fa-file-alt\"></i>\n                      {{ request.document_type }}\n                    </span>\n                  </div>\n\n                  <!-- Status -->\n                  <div class=\"row-cell status-cell\">\n                    <span class=\"status-compact\" :class=\"`status-${getStatusColor(request.status_name)}`\">\n                      <i class=\"fas fa-circle\"></i>\n                      {{ formatStatus(request.status_name) }}\n                    </span>\n                  </div>\n\n                  <!-- Amount -->\n                  <div class=\"row-cell amount-cell\">\n                    <span class=\"amount-compact\">{{ formatCurrency(request.total_fee) }}</span>\n                  </div>\n\n                  <!-- Date -->\n                  <div class=\"row-cell date-cell\">\n                    <div class=\"date-compact\">\n                      <span class=\"date-main\">{{ formatDate(request.requested_at) }}</span>\n                      <span class=\"time-small\">{{ formatTime(request.requested_at) }}</span>\n                    </div>\n                  </div>\n\n                  <!-- Actions -->\n                  <div class=\"row-cell actions-cell\">\n                    <div class=\"actions-simple\">\n                      <button class=\"action-btn-sm view-btn-sm\" @click=\"viewRequestDetails(request.id)\" title=\"View Details\">\n                        <i class=\"fas fa-eye\"></i>\n                      </button>\n                      <button\n                        v-if=\"canApprove(request)\"\n                        class=\"action-btn-sm approve-btn-sm\"\n                        @click=\"quickApprove(request)\"\n                        title=\"Quick Approve\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-check\"></i>\n                      </button>\n                      <button\n                        v-if=\"canReject(request)\"\n                        class=\"action-btn-sm reject-btn-sm\"\n                        @click=\"showQuickRejectModal(request)\"\n                        title=\"Quick Reject\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Pagination -->\n          <div v-if=\"pagination.totalPages > 1\" class=\"pagination-container\">\n              <nav aria-label=\"Requests pagination\">\n                <ul class=\"pagination pagination-sm justify-content-center mb-0\">\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === 1 }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage - 1)\">\n                      <i class=\"fas fa-chevron-left\"></i>\n                    </a>\n                  </li>\n                  <li\n                    v-for=\"page in Math.min(pagination.totalPages, 10)\"\n                    :key=\"page\"\n                    class=\"page-item\"\n                    :class=\"{ active: page === pagination.currentPage }\"\n                  >\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(page)\">{{ page }}</a>\n                  </li>\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === pagination.totalPages }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage + 1)\">\n                      <i class=\"fas fa-chevron-right\"></i>\n                    </a>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n\n          <!-- Request Details Modal -->\n          <div v-if=\"showRequestDetails && currentRequest\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog modal-xl modal-dialog-scrollable\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-file-alt me-2\"></i>\n                    Request Details - {{ currentRequest.request_number }}\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"showRequestDetails = false\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"row\">\n                    <!-- Left Column - Request Information -->\n                    <div class=\"col-lg-8\">\n                      <!-- Basic Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-info-circle me-2\"></i>Request Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Request Number</label>\n                                <p class=\"mb-0\">{{ currentRequest.request_number }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Document Type</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge bg-info\">{{ currentRequest.document_type }}</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Category</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_category }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Details</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_details || 'Not specified' }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Current Status</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"`bg-${getStatusColor(currentRequest.status_name)}`\">\n                                    {{ formatStatus(currentRequest.status_name) }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Priority</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"currentRequest.priority === 'high' ? 'bg-danger' : currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'\">\n                                    {{ currentRequest.priority || 'Normal' }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Delivery Method</label>\n                                <p class=\"mb-0\">{{ currentRequest.delivery_method || 'Pickup' }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Date Submitted</label>\n                                <p class=\"mb-0\">{{ formatDateTime(currentRequest.requested_at) }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Client Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-user me-2\"></i>Client Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Full Name</label>\n                                <p class=\"mb-0\">{{ currentRequest.client_name }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Email Address</label>\n                                <p class=\"mb-0\">\n                                  <a :href=\"`mailto:${currentRequest.client_email}`\">{{ currentRequest.client_email }}</a>\n                                </p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Phone Number</label>\n                                <p class=\"mb-0\">\n                                  <a :href=\"`tel:${currentRequest.client_phone}`\">{{ currentRequest.client_phone || 'Not provided' }}</a>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Address</label>\n                                <p class=\"mb-0\">{{ currentRequest.client_address || 'Not provided' }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Document-Specific Details -->\n                      <div v-if=\"currentRequest.specific_details\" class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-clipboard-list me-2\"></i>Document-Specific Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <!-- Barangay Clearance Details -->\n                          <div v-if=\"currentRequest.document_type === 'Barangay Clearance'\">\n                            <div class=\"row\">\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Residency Period</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.residency_period || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Civil Status</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.civil_status || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Occupation</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.occupation || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Monthly Income</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.monthly_income) || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Cedula Details -->\n                          <div v-else-if=\"currentRequest.document_type === 'Cedula'\">\n                            <div class=\"row\">\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Birth Date</label>\n                                  <p class=\"mb-0\">{{ formatDate(currentRequest.specific_details.birth_date) || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Birth Place</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.birth_place || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Civil Status</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.civil_status || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Citizenship</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.citizenship || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Occupation</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.occupation || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Annual Income</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.annual_income) || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Tax Amount</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.tax_amount) || 'Not calculated' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Interest/Penalty</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.interest_penalty) || 'None' }}</p>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Right Column - Status Management -->\n                    <div class=\"col-lg-4\">\n                      <!-- Status Management -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-tasks me-2\"></i>Status Management</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Change Status</label>\n                            <select class=\"form-select\" v-model=\"statusUpdateForm.status_id\">\n                              <option value=\"\">Select new status</option>\n                              <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.id\">\n                                {{ formatStatus(status.status_name) }}\n                              </option>\n                            </select>\n                          </div>\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Reason/Notes</label>\n                            <textarea\n                              class=\"form-control\"\n                              rows=\"3\"\n                              v-model=\"statusUpdateForm.reason\"\n                              placeholder=\"Enter reason for status change (optional)\"\n                            ></textarea>\n                          </div>\n                          <div class=\"d-grid gap-2\">\n                            <button\n                              class=\"btn btn-primary\"\n                              @click=\"updateRequestStatusFromModal\"\n                              :disabled=\"!statusUpdateForm.status_id\"\n                            >\n                              <i class=\"fas fa-save me-1\"></i>\n                              Update Status\n                            </button>\n                            <button\n                              class=\"btn btn-success\"\n                              @click=\"approveRequestFromModal\"\n                              :disabled=\"!canApprove(currentRequest)\"\n                              :title=\"canApprove(currentRequest) ? 'Approve this request' : getStatusExplanation(currentRequest, 'approve')\"\n                              :class=\"{ 'btn-outline-success': !canApprove(currentRequest) }\"\n                            >\n                              <i class=\"fas fa-check me-1\"></i>\n                              Quick Approve\n                            </button>\n                            <button\n                              class=\"btn btn-danger\"\n                              @click=\"showRejectForm = !showRejectForm\"\n                              :disabled=\"!canReject(currentRequest)\"\n                              :title=\"canReject(currentRequest) ? 'Reject this request' : getStatusExplanation(currentRequest, 'reject')\"\n                              :class=\"{ 'btn-outline-danger': !canReject(currentRequest) }\"\n                            >\n                              <i class=\"fas fa-times me-1\"></i>\n                              {{ showRejectForm ? 'Cancel' : 'Reject Request' }}\n                            </button>\n                          </div>\n\n                          <!-- Rejection Form -->\n                          <div v-if=\"showRejectForm\" class=\"mt-3 p-3 border rounded bg-light\">\n                            <div class=\"mb-3\">\n                              <label class=\"form-label fw-bold text-danger\">Rejection Reason *</label>\n                              <textarea\n                                class=\"form-control\"\n                                rows=\"3\"\n                                v-model=\"rejectForm.reason\"\n                                placeholder=\"Please provide a detailed reason for rejection\"\n                                required\n                              ></textarea>\n                            </div>\n                            <div class=\"d-grid\">\n                              <button\n                                class=\"btn btn-danger\"\n                                @click=\"rejectRequestFromModal\"\n                                :disabled=\"!rejectForm.reason || rejectForm.reason.trim() === ''\"\n                              >\n                                <i class=\"fas fa-times me-1\"></i>\n                                Confirm Rejection\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Payment Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-credit-card me-2\"></i>Payment Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Method</label>\n                            <p class=\"mb-0\">{{ currentRequest.payment_method || 'Not specified' }}</p>\n                          </div>\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Status</label>\n                            <p class=\"mb-0\">\n                              <span class=\"badge\" :class=\"currentRequest.payment_status === 'paid' ? 'bg-success' : currentRequest.payment_status === 'pending' ? 'bg-warning' : 'bg-secondary'\">\n                                {{ currentRequest.payment_status || 'Unpaid' }}\n                              </span>\n                            </p>\n                          </div>\n                          <div class=\"row\">\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Base Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.base_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Additional Fees</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.additional_fees) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Processing Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.processing_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Total Amount</label>\n                                <p class=\"mb-0 fw-bold text-primary\">{{ formatCurrency(currentRequest.total_fee) }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Status History Timeline -->\n                  <div class=\"card\">\n                    <div class=\"card-header\">\n                      <h6 class=\"mb-0\"><i class=\"fas fa-history me-2\"></i>Status History</h6>\n                    </div>\n                    <div class=\"card-body\">\n                      <div v-if=\"currentRequest.status_history && currentRequest.status_history.length > 0\" class=\"timeline\">\n                        <div\n                          v-for=\"(history, index) in currentRequest.status_history\"\n                          :key=\"history.id\"\n                          class=\"timeline-item\"\n                          :class=\"{ 'timeline-item-last': index === currentRequest.status_history.length - 1 }\"\n                        >\n                          <div class=\"timeline-marker\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                            <i class=\"fas fa-circle\"></i>\n                          </div>\n                          <div class=\"timeline-content\">\n                            <div class=\"timeline-header\">\n                              <span class=\"badge\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                                {{ formatStatus(history.new_status_name) }}\n                              </span>\n                              <small class=\"text-muted ms-2\">{{ formatDateTime(history.changed_at) }}</small>\n                            </div>\n                            <div class=\"timeline-body\">\n                              <p class=\"mb-1\">\n                                <strong>Changed by:</strong> {{ history.changed_by_name }}\n                              </p>\n                              <p v-if=\"history.old_status_name\" class=\"mb-1\">\n                                <strong>From:</strong> {{ formatStatus(history.old_status_name) }}\n                              </p>\n                              <p v-if=\"history.change_reason\" class=\"mb-0\">\n                                <strong>Reason:</strong> {{ history.change_reason }}\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div v-else class=\"text-center text-muted py-3\">\n                        <i class=\"fas fa-history fa-2x mb-2\"></i>\n                        <p>No status history available</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"showRequestDetails = false\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Close\n                  </button>\n                  <button type=\"button\" class=\"btn btn-primary\" @click=\"refreshRequestDetails\">\n                    <i class=\"fas fa-sync-alt me-1\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Reject Modal -->\n          <div v-if=\"showQuickReject && selectedRequestForReject\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-times-circle text-danger me-2\"></i>\n                    Reject Request\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"closeQuickRejectModal\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"alert alert-warning\">\n                    <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                    You are about to reject this document request. This action will notify the client immediately.\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <strong>Request Details:</strong>\n                    <ul class=\"list-unstyled mt-2\">\n                      <li><strong>Request Number:</strong> {{ selectedRequestForReject.request_number }}</li>\n                      <li><strong>Document Type:</strong> {{ selectedRequestForReject.document_type }}</li>\n                      <li><strong>Client:</strong> {{ selectedRequestForReject.client_name }}</li>\n                    </ul>\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <label for=\"rejectionReason\" class=\"form-label\">\n                      <strong>Rejection Reason <span class=\"text-danger\">*</span></strong>\n                    </label>\n                    <textarea\n                      id=\"rejectionReason\"\n                      v-model=\"quickRejectForm.reason\"\n                      class=\"form-control\"\n                      rows=\"4\"\n                      placeholder=\"Please provide a clear reason for rejecting this request...\"\n                      :class=\"{ 'is-invalid': quickRejectForm.error }\"\n                    ></textarea>\n                    <div v-if=\"quickRejectForm.error\" class=\"invalid-feedback\">\n                      {{ quickRejectForm.error }}\n                    </div>\n                    <div class=\"form-text\">\n                      This reason will be visible to the client and included in their notification.\n                    </div>\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeQuickRejectModal\" :disabled=\"quickRejectForm.loading\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Cancel\n                  </button>\n                  <button type=\"button\" class=\"btn btn-danger\" @click=\"confirmQuickReject\" :disabled=\"quickRejectForm.loading || !quickRejectForm.reason.trim()\">\n                    <i class=\"fas fa-times-circle me-1\"></i>\n                    <span v-if=\"quickRejectForm.loading\">\n                      <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                      Rejecting...\n                    </span>\n                    <span v-else>Reject Request</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  </template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport notificationService from '@/services/notificationService';\n\nexport default {\n  name: 'AdminRequests',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      loading: true,\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      errorMessage: '',\n      viewMode: 'table', // 'card' or 'table' - default to table view\n\n      // Request Management Data\n      requests: [],\n      selectedRequests: [],\n      currentRequest: null,\n      statusOptions: [],\n\n      // Pagination\n      pagination: {\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n      },\n\n      // Filters\n      filters: {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      },\n\n      // Statistics\n      requestStats: {\n        total: 0,\n        pending: 0,\n        approved: 0,\n        completed: 0,\n        thisMonth: 0\n      },\n\n      // UI State\n      showFilters: false,\n      showBulkActions: false,\n      showRequestDetails: false,\n      showRejectForm: false,\n      showQuickReject: false,\n      bulkAction: '',\n      bulkReason: '',\n\n      // Status Update Forms\n      statusUpdateForm: {\n        status_id: '',\n        reason: ''\n      },\n      rejectForm: {\n        reason: ''\n      },\n      quickRejectForm: {\n        reason: '',\n        loading: false,\n        error: ''\n      },\n      selectedRequestForReject: null,\n\n      // Real-time features\n      refreshInterval: null,\n      autoRefreshEnabled: true,\n      refreshRate: 30000, // 30 seconds\n      lastRefresh: null\n    };\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load component data\n    await this.loadComponentData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load all component data\n    async loadComponentData() {\n      this.loading = true;\n      try {\n        await Promise.all([\n          this.loadAdminProfile(),\n          this.loadStatusOptions(),\n          this.loadRequests(),\n          this.loadDashboardStats()\n        ]);\n      } catch (error) {\n        console.error('Failed to load component data:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request data';\n\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Load status options\n    async loadStatusOptions() {\n      try {\n        console.log('🔄 Loading status options...');\n        const response = await adminDocumentService.getStatusOptions();\n        console.log('📋 Status options response:', response);\n\n        if (response.success) {\n          this.statusOptions = response.data || [];\n          console.log('✅ Status options loaded:', this.statusOptions);\n        } else {\n          console.error('❌ Failed to load status options:', response.message);\n          this.statusOptions = [];\n        }\n      } catch (error) {\n        console.error('❌ Error loading status options:', error);\n        this.statusOptions = [];\n        this.showToast('Error', 'Failed to load status options', 'error');\n      }\n    },\n\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        const response = await adminDocumentService.getDashboardStats();\n        if (response.success) {\n          this.requestStats = {\n            total: response.data.totalRequests || 0,\n            pending: response.data.pendingRequests || 0,\n            approved: response.data.approvedRequests || 0,\n            completed: response.data.completedRequests || 0,\n            thisMonth: response.data.todayRequests || 0\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load dashboard stats:', error);\n      }\n    },\n\n    // Load requests with current filters and pagination\n    async loadRequests() {\n      try {\n        const params = {\n          page: this.pagination.currentPage,\n          limit: this.pagination.itemsPerPage,\n          ...this.filters\n        };\n\n        const response = await adminDocumentService.getAllRequests(params);\n        if (response.success) {\n          this.requests = response.data.requests || [];\n          this.pagination = {\n            currentPage: response.data.pagination?.current_page || 1,\n            totalPages: response.data.pagination?.total_pages || 1,\n            totalItems: response.data.pagination?.total_records || 0,\n            itemsPerPage: response.data.pagination?.per_page || 10\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load requests';\n        this.requests = [];\n      }\n    },\n\n    // Filter and search methods\n    applyFilters() {\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    clearFilters() {\n      this.filters = {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      };\n      this.applyFilters();\n    },\n\n    // Pagination methods\n    changePage(page) {\n      if (page >= 1 && page <= this.pagination.totalPages) {\n        this.pagination.currentPage = page;\n        this.loadRequests();\n      }\n    },\n\n    changeItemsPerPage(itemsPerPage) {\n      this.pagination.itemsPerPage = itemsPerPage;\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    goBack() {\n      this.$router.push('/admin/dashboard');\n    },\n\n    // Request selection methods\n    toggleRequestSelection(requestId) {\n      const index = this.selectedRequests.indexOf(requestId);\n      if (index > -1) {\n        this.selectedRequests.splice(index, 1);\n      } else {\n        this.selectedRequests.push(requestId);\n      }\n    },\n\n    selectAllRequests() {\n      if (this.selectedRequests.length === this.requests.length) {\n        this.selectedRequests = [];\n      } else {\n        this.selectedRequests = this.requests.map(r => r.id);\n      }\n    },\n\n    // Request details\n    async viewRequestDetails(requestId) {\n      console.log('🚀 View details clicked for request ID:', requestId);\n\n      try {\n        const response = await adminDocumentService.getRequestDetails(requestId);\n        if (response.success) {\n          this.currentRequest = response.data;\n          this.showRequestDetails = true;\n          // Reset forms\n          this.statusUpdateForm = { status_id: '', reason: '' };\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n          console.log('📋 Request details loaded:', response.data);\n          this.showToast('Success', `Request details loaded for ${response.data.request_number}`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to load request details:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request details';\n        this.showToast('Error', 'Failed to load request details', 'error');\n      }\n    },\n\n    // Refresh request details\n    async refreshRequestDetails() {\n      if (this.currentRequest) {\n        await this.viewRequestDetails(this.currentRequest.id);\n      }\n    },\n\n    // Update request status from modal\n    async updateRequestStatusFromModal() {\n      console.log('🔄 Updating request status...');\n      console.log('📋 Status form data:', this.statusUpdateForm);\n      console.log('📋 Current request:', this.currentRequest);\n\n      if (!this.statusUpdateForm.status_id || !this.currentRequest) {\n        console.error('❌ Missing required data for status update');\n        this.showToast('Error', 'Please select a status to update', 'error');\n        return;\n      }\n\n      try {\n        const updateData = {\n          status_id: parseInt(this.statusUpdateForm.status_id),\n          reason: this.statusUpdateForm.reason\n        };\n\n        console.log('📤 Sending status update:', updateData);\n\n        const response = await adminDocumentService.updateRequestStatus(\n          this.currentRequest.id,\n          updateData\n        );\n\n        console.log('📥 Status update response:', response);\n\n        if (response.success) {\n          // Refresh the request details\n          await this.refreshRequestDetails();\n          // Refresh the main requests list\n          await this.loadRequests();\n          // Reset form\n          this.statusUpdateForm = { status_id: '', reason: '' };\n\n          // Show success message\n          this.errorMessage = '';\n          this.showToast('Success', 'Request status updated successfully', 'success');\n        } else {\n          console.error('❌ Status update failed:', response.message);\n          this.showToast('Error', response.message || 'Failed to update request status', 'error');\n        }\n      } catch (error) {\n        console.error('❌ Error updating request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n        this.showToast('Error', errorData.message || 'Failed to update request status', 'error');\n      }\n    },\n\n    // Approve request from modal\n    async approveRequestFromModal() {\n      if (!this.currentRequest) return;\n\n      try {\n        const response = await adminDocumentService.approveRequest(\n          this.currentRequest.id,\n          { reason: 'Approved from request details' }\n        );\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n\n    // Reject request from modal\n    async rejectRequestFromModal() {\n      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.currentRequest.id,\n          { reason: this.rejectForm.reason }\n        );\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Status update methods\n    async updateRequestStatus(requestId, statusId, reason = '') {\n      try {\n        const response = await adminDocumentService.updateRequestStatus(requestId, {\n          status_id: statusId,\n          reason: reason\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n\n    async approveRequest(requestId, reason = '') {\n      try {\n        const response = await adminDocumentService.approveRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n\n    async rejectRequest(requestId, reason) {\n      if (!reason || reason.trim() === '') {\n        this.errorMessage = 'Rejection reason is required';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.rejectRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Quick approval/rejection methods\n    canApprove(request) {\n      // Can only approve requests that are not already approved or rejected\n      return ['pending', 'under_review', 'additional_info_required'].includes(request.status_name);\n    },\n\n    canReject(request) {\n      // Can only reject requests that are not already approved or rejected\n      // Once approved or rejected, the request status is final\n      return ['pending', 'under_review', 'additional_info_required'].includes(request.status_name);\n    },\n\n    // Helper method to get status explanation for disabled buttons\n    getStatusExplanation(request, action) {\n      const status = request.status_name;\n\n      if (action === 'approve') {\n        if (status === 'approved') {\n          return 'This request has already been approved';\n        } else if (status === 'rejected') {\n          return 'Cannot approve a rejected request';\n        } else if (status === 'completed') {\n          return 'This request has already been completed';\n        }\n      } else if (action === 'reject') {\n        if (status === 'approved') {\n          return 'Cannot reject an approved request';\n        } else if (status === 'rejected') {\n          return 'This request has already been rejected';\n        } else if (status === 'completed') {\n          return 'Cannot reject a completed request';\n        }\n      }\n\n      return `Request status: ${this.formatStatus(status)}`;\n    },\n\n    async quickApprove(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n\n      try {\n        this.loading = true;\n        const response = await adminDocumentService.approveRequest(request.id, {\n          reason: 'Quick approval from admin interface'\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    showQuickRejectModal(request) {\n      console.log('🚀 Quick reject clicked for request:', request);\n\n      this.selectedRequestForReject = request;\n      this.quickRejectForm = {\n        reason: '',\n        loading: false,\n        error: ''\n      };\n      this.showQuickReject = true;\n    },\n\n    closeQuickRejectModal() {\n      this.showQuickReject = false;\n      this.selectedRequestForReject = null;\n      this.quickRejectForm = {\n        reason: '',\n        loading: false,\n        error: ''\n      };\n    },\n\n    async confirmQuickReject() {\n      if (!this.quickRejectForm.reason.trim()) {\n        this.quickRejectForm.error = 'Rejection reason is required';\n        return;\n      }\n\n      this.quickRejectForm.loading = true;\n      this.quickRejectForm.error = '';\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.selectedRequestForReject.id,\n          { reason: this.quickRejectForm.reason }\n        );\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');\n          this.closeQuickRejectModal();\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickRejectForm.error = errorData.message || 'Failed to reject request';\n      } finally {\n        this.quickRejectForm.loading = false;\n      }\n    },\n\n    // Bulk operations\n    async performBulkAction() {\n      if (this.selectedRequests.length === 0) {\n        this.errorMessage = 'Please select at least one request';\n        return;\n      }\n\n      if (!this.bulkAction) {\n        this.errorMessage = 'Please select a bulk action';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.bulkUpdateRequests({\n          request_ids: this.selectedRequests,\n          status_id: parseInt(this.bulkAction),\n          reason: this.bulkReason\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.selectedRequests = [];\n          this.bulkAction = '';\n          this.bulkReason = '';\n          this.showBulkActions = false;\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to perform bulk action:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to perform bulk action';\n      }\n    },\n\n    // Export functionality\n    async exportRequests() {\n      try {\n        const csvData = await adminDocumentService.exportRequests(this.filters);\n        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;\n        adminDocumentService.downloadCSV(csvData, filename);\n      } catch (error) {\n        console.error('Failed to export requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to export requests';\n      }\n    },\n\n    // Utility methods\n    formatStatus(status) {\n      return adminDocumentService.formatStatus(status);\n    },\n\n    getStatusColor(status) {\n      return adminDocumentService.getStatusColor(status);\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        style: 'currency',\n        currency: 'PHP'\n      }).format(amount || 0);\n    },\n\n    formatDateTime(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    formatTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminRequests');\n\n      try {\n        // Initialize notification service\n        await notificationService.init('admin');\n\n        // Listen for request-related notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('new_request', this.handleNewRequest);\n\n        // Start auto-refresh if enabled\n        if (this.autoRefreshEnabled) {\n          this.startAutoRefresh();\n        }\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminRequests');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('new_request', this.handleNewRequest);\n\n      // Cleanup (simplified)\n      notificationService.cleanup();\n\n      // Stop auto-refresh\n      this.stopAutoRefresh();\n    },\n\n    startAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n      }\n\n      this.refreshInterval = setInterval(() => {\n        if (this.autoRefreshEnabled && !this.loading) {\n          console.log('Auto-refreshing requests data...');\n          this.refreshRequestsData();\n        }\n      }, this.refreshRate);\n\n      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);\n    },\n\n    stopAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n        this.refreshInterval = null;\n        console.log('Auto-refresh stopped');\n      }\n    },\n\n    toggleAutoRefresh() {\n      this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n      if (this.autoRefreshEnabled) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n\n    async refreshRequestsData() {\n      try {\n        this.lastRefresh = new Date();\n\n        // Refresh requests list\n        await this.loadRequests();\n\n        // Refresh statistics\n        await this.loadDashboardStats();\n\n        // If request details modal is open, refresh it\n        if (this.showRequestDetails && this.currentRequest) {\n          await this.refreshRequestDetails();\n        }\n\n        console.log('Requests data refreshed successfully');\n      } catch (error) {\n        console.error('Failed to refresh requests data:', error);\n      }\n    },\n\n    handleRealTimeNotification(notification) {\n      console.log('Real-time notification received:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_updated':\n          this.handleRequestUpdate(notification.data);\n          break;\n        case 'unread_count_update':\n        case 'heartbeat':\n          // Polling system notifications - handled by notification service\n          break;\n        default:\n          // Only log unknown types, not system types\n          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {\n            console.log('Unhandled notification type:', notification.type);\n          }\n      }\n    },\n\n    handleStatusChange(data) {\n      console.log('Request status changed:', data);\n\n      // Update the request in the list if it exists\n      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);\n      if (requestIndex !== -1) {\n        // Refresh the specific request or reload all requests\n        this.refreshRequestsData();\n      }\n\n      // Show toast notification\n      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');\n    },\n\n    handleNewRequest(data) {\n      console.log('New request received:', data);\n\n      // Refresh requests to show the new request\n      this.refreshRequestsData();\n\n      // Show toast notification\n      this.showToast('New Request', `New ${data.document_type} request received`, 'success');\n    },\n\n    handleRequestUpdate(data) {\n      console.log('Request updated:', data);\n\n      // If the updated request is currently being viewed, refresh details\n      if (this.currentRequest && this.currentRequest.id === data.request_id) {\n        this.refreshRequestDetails();\n      }\n\n      // Refresh the requests list\n      this.refreshRequestsData();\n    },\n\n    showToast(title, message, type = 'info') {\n      // Log to console for debugging\n      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);\n\n      // Create a simple toast notification\n      const toast = document.createElement('div');\n      toast.className = `toast-notification toast-${type}`;\n      toast.innerHTML = `\n        <div class=\"toast-header\">\n          <strong>${title}</strong>\n          <button type=\"button\" class=\"toast-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>\n        </div>\n        <div class=\"toast-body\">${message}</div>\n      `;\n\n      // Add toast styles if not already added\n      if (!document.getElementById('toast-styles')) {\n        const styles = document.createElement('style');\n        styles.id = 'toast-styles';\n        styles.textContent = `\n          .toast-notification {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            min-width: 300px;\n            background: white;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n            z-index: 9999;\n            animation: slideIn 0.3s ease;\n          }\n          .toast-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 12px 16px 8px;\n            border-bottom: 1px solid #e9ecef;\n          }\n          .toast-body {\n            padding: 8px 16px 12px;\n            color: #6c757d;\n          }\n          .toast-close {\n            background: none;\n            border: none;\n            font-size: 18px;\n            cursor: pointer;\n            color: #6c757d;\n          }\n          .toast-success { border-left: 4px solid #28a745; }\n          .toast-error { border-left: 4px solid #dc3545; }\n          .toast-info { border-left: 4px solid #17a2b8; }\n          .toast-warning { border-left: 4px solid #ffc107; }\n          @keyframes slideIn {\n            from { transform: translateX(100%); opacity: 0; }\n            to { transform: translateX(0); opacity: 1; }\n          }\n        `;\n        document.head.appendChild(styles);\n      }\n\n      // Add toast to page\n      document.body.appendChild(toast);\n\n      // Auto-remove after 5 seconds\n      setTimeout(() => {\n        if (toast.parentElement) {\n          toast.style.animation = 'slideIn 0.3s ease reverse';\n          setTimeout(() => toast.remove(), 300);\n        }\n      }, 5000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* Additional styles specific to AdminRequests */\n.admin-requests {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);\n}\n\n/* Ensure proper spacing for request statistics cards */\n.card.border-left-primary {\n  border-left: 4px solid #3b82f6 !important;\n}\n\n.card.border-left-warning {\n  border-left: 4px solid #f59e0b !important;\n}\n\n.card.border-left-success {\n  border-left: 4px solid #059669 !important;\n}\n\n.card.border-left-info {\n  border-left: 4px solid #06b6d4 !important;\n}\n\n/* Bootstrap utility classes for compatibility */\n.text-xs {\n  font-size: 0.75rem !important;\n}\n\n.text-gray-800 {\n  color: #1f2937 !important;\n}\n\n.text-gray-300 {\n  color: #d1d5db !important;\n}\n\n.text-muted {\n  color: #6c757d !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.g-0 {\n  --bs-gutter-x: 0;\n  --bs-gutter-y: 0;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n/* Improve button spacing */\n.d-flex.gap-2 {\n  gap: 0.5rem !important;\n}\n\n/* Timeline Styles */\n.timeline {\n  position: relative;\n  padding-left: 2rem;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 1rem;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  background: #e3e6f0;\n}\n\n.timeline-item {\n  position: relative;\n  margin-bottom: 2rem;\n}\n\n.timeline-item:last-child {\n  margin-bottom: 0;\n}\n\n.timeline-item.timeline-item-last::after {\n  display: none;\n}\n\n.timeline-marker {\n  position: absolute;\n  left: -2rem;\n  top: 0.25rem;\n  width: 2rem;\n  height: 2rem;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  z-index: 1;\n}\n\n.timeline-content {\n  background: #f8f9fc;\n  border-radius: 8px;\n  padding: 1rem;\n  border-left: 3px solid #e3e6f0;\n}\n\n.timeline-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.timeline-body p {\n  margin-bottom: 0.25rem;\n  font-size: 0.875rem;\n}\n\n.timeline-body p:last-child {\n  margin-bottom: 0;\n}\n\n/* Modal Styles */\n.modal-xl {\n  max-width: 1200px;\n}\n\n.modal-dialog-scrollable .modal-body {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n/* Real-time status indicator styles */\n.real-time-status .badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 1rem;\n}\n\n.pulse {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* Card View Styles */\n.requests-grid {\n  min-height: 400px;\n}\n\n.empty-state {\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 3rem 2rem;\n  margin: 2rem 0;\n}\n\n.empty-state-icon {\n  opacity: 0.5;\n}\n\n.request-card {\n  background: #ffffff;\n  border: 1px solid #e3e6f0;\n  border-radius: 12px;\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.request-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);\n  border-color: #5a5c69;\n}\n\n.request-card.selected {\n  border-color: #4e73df;\n  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);\n}\n\n.request-card-header {\n  padding: 1rem 1.25rem 0.5rem;\n  border-bottom: 1px solid #f1f1f1;\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n}\n\n.request-card-body {\n  padding: 1.25rem;\n  flex-grow: 1;\n}\n\n.request-card-footer {\n  padding: 0.75rem 1.25rem 1.25rem;\n  background: #f8f9fa;\n  border-top: 1px solid #e3e6f0;\n}\n\n.client-avatar {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  color: white;\n  font-size: 1.2rem;\n}\n\n.client-info h6 {\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n.document-type {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n  border-left: 4px solid #17a2b8;\n}\n\n.document-type .badge {\n  font-size: 0.875rem;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n}\n\n.request-meta {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n}\n\n.meta-item {\n  text-align: center;\n}\n\n.meta-item small {\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.request-date {\n  padding-top: 0.75rem;\n  border-top: 1px solid #e9ecef;\n  margin-top: 0.75rem;\n}\n\n.request-actions .dropdown-toggle {\n  border: none;\n  background: transparent;\n  color: #6c757d;\n  padding: 0.25rem 0.5rem;\n  border-radius: 6px;\n}\n\n.request-actions .dropdown-toggle:hover {\n  background: #e9ecef;\n  color: #495057;\n}\n\n/* View Toggle Styles */\n.btn-check:checked + .btn-outline-primary {\n  background-color: #4e73df;\n  border-color: #4e73df;\n  color: white;\n}\n\n/* Badge Enhancements */\n.badge.bg-info-subtle {\n  background-color: #cff4fc !important;\n  color: #055160 !important;\n  border: 1px solid #b6effb;\n}\n\n/* Button Enhancements */\n.request-card-footer .btn {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.request-card-footer .btn:hover {\n  transform: translateY(-1px);\n}\n\n/* Modern Table Styles */\n.modern-table-container {\n  background: #ffffff;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: visible;\n  border: 1px solid #e8ecef;\n}\n\n.modern-table-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.modern-table-header h5 {\n  color: white;\n  margin: 0;\n  font-weight: 600;\n}\n\n.table-actions .btn {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.table-actions .btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n.modern-table-empty {\n  padding: 4rem 2rem;\n  text-align: center;\n  background: #f8f9fa;\n}\n\n.empty-content {\n  max-width: 400px;\n  margin: 0 auto;\n}\n\n.empty-icon {\n  font-size: 4rem;\n  color: #6c757d;\n  margin-bottom: 1.5rem;\n  opacity: 0.5;\n}\n\n.empty-title {\n  color: #495057;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n}\n\n.empty-text {\n  color: #6c757d;\n  margin: 0;\n}\n\n/* Compact Table Styles */\n.compact-table-wrapper {\n  background: white;\n  border-radius: 12px;\n  overflow: visible;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  border: 1px solid #e8ecef;\n}\n\n.compact-table-header {\n  display: grid;\n  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  gap: 0.5rem;\n}\n\n.header-cell {\n  display: flex;\n  align-items: center;\n  padding: 0 0.5rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  font-size: 0.75rem;\n}\n\n.selection-header {\n  justify-content: center;\n}\n\n.compact-table-body {\n  background: white;\n  overflow: visible;\n}\n\n.compact-row {\n  display: grid;\n  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-bottom: 1px solid #f1f3f4;\n  transition: all 0.15s ease;\n  position: relative;\n  min-height: 48px;\n  gap: 0.5rem;\n}\n\n.compact-row:hover {\n  background: #f8f9fa;\n  transform: translateX(2px);\n  box-shadow: 2px 0 0 #667eea;\n}\n\n.compact-row.selected {\n  background: #e3f2fd;\n  border-left: 3px solid #2196f3;\n}\n\n.compact-row:last-child {\n  border-bottom: none;\n}\n\n.row-cell {\n  display: flex;\n  align-items: center;\n  padding: 0 0.5rem;\n  font-size: 0.875rem;\n}\n\n/* Selection Cell */\n.selection-cell {\n  justify-content: center;\n}\n\n.selection-cell .form-check-input {\n  width: 18px;\n  height: 18px;\n  border-radius: 4px;\n  border: 2px solid #dee2e6;\n}\n\n.selection-cell .form-check-input:checked {\n  background-color: #667eea;\n  border-color: #667eea;\n}\n\n/* Request Number Cell */\n.request-number-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.request-number {\n  font-weight: 700;\n  color: #667eea;\n  font-size: 1rem;\n  letter-spacing: 0.5px;\n}\n\n.request-id {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Client Cell */\n.client-info {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.client-avatar-sm {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1rem;\n  flex-shrink: 0;\n}\n\n.client-details {\n  min-width: 0;\n  flex: 1;\n}\n\n.client-name {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.95rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email {\n  font-size: 0.8rem;\n  color: #6c757d;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-top: 2px;\n}\n\n/* Document Type Cell */\n.document-type-badge {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n/* Status Cell */\n.status-badge {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-indicator {\n  font-size: 0.6rem;\n  animation: pulse 2s infinite;\n}\n\n.status-success {\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\n}\n\n.status-warning {\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\n  color: #212529;\n  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);\n}\n\n.status-danger {\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);\n}\n\n.status-info {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n.status-secondary {\n  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);\n}\n\n/* Amount Cell */\n.amount-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.amount {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 1.1rem;\n}\n\n.currency {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Date Cell */\n.date-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.9rem;\n}\n\n.time {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Actions Cell */\n.action-buttons {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n}\n\n.action-btn {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.view-btn {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn:hover {\n  background: #bbdefb;\n  transform: translateY(-2px);\n}\n\n.approve-btn {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn:hover {\n  background: #c8e6c9;\n  transform: translateY(-2px);\n}\n\n.reject-btn {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn:hover {\n  background: #ffcdd2;\n  transform: translateY(-2px);\n}\n\n.more-btn {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn:hover {\n  background: #e0e0e0;\n  transform: translateY(-2px);\n}\n\n.more-btn::after {\n  display: none;\n}\n\n/* Dropdown positioning fixes */\n.modern-table {\n  overflow: visible;\n}\n\n.table-row {\n  overflow: visible;\n}\n\n.action-buttons .dropdown {\n  position: static;\n}\n\n.action-buttons .dropdown-menu {\n  position: absolute !important;\n  top: 100% !important;\n  right: 0 !important;\n  left: auto !important;\n  z-index: 1050 !important;\n  transform: none !important;\n  margin-top: 0.25rem;\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 8px;\n  background: white;\n  min-width: 160px;\n}\n\n.action-buttons .dropdown-menu.show {\n  display: block !important;\n}\n\n/* Ensure dropdown appears above other elements */\n.action-buttons .dropdown.show {\n  z-index: 1051;\n}\n\n/* Pagination Container */\n.pagination-container {\n  background: white;\n  border-radius: 0 0 16px 16px;\n  padding: 1.5rem 2rem;\n  border-top: 1px solid #f1f3f4;\n  margin-top: -1px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.pagination-container .pagination {\n  margin: 0;\n}\n\n.pagination-container .page-link {\n  border: 1px solid #e3e6f0;\n  color: #667eea;\n  padding: 0.5rem 0.75rem;\n  margin: 0 2px;\n  border-radius: 8px;\n  transition: all 0.2s ease;\n}\n\n.pagination-container .page-link:hover {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  transform: translateY(-1px);\n}\n\n.pagination-container .page-item.active .page-link {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n\n.pagination-container .page-item.disabled .page-link {\n  color: #6c757d;\n  background: #f8f9fa;\n  border-color: #e3e6f0;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .d-flex.gap-2 .btn {\n    margin-bottom: 0.5rem;\n  }\n\n  .modal-xl {\n    max-width: 95%;\n    margin: 1rem auto;\n  }\n\n  .timeline {\n    padding-left: 1.5rem;\n  }\n\n  .timeline-marker {\n    left: -1.5rem;\n    width: 1.5rem;\n    height: 1.5rem;\n    font-size: 0.625rem;\n  }\n\n  /* Compact table mobile adjustments */\n  .compact-table-header {\n    display: none;\n  }\n\n  .compact-row {\n    grid-template-columns: 1fr;\n    padding: 1rem;\n    gap: 0.75rem;\n    border-radius: 8px;\n    margin-bottom: 0.75rem;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  }\n\n  .row-cell {\n    min-height: auto;\n    flex-direction: column;\n    align-items: flex-start;\n    padding: 0.25rem 0;\n    border-bottom: 1px solid #f1f3f4;\n  }\n\n  .row-cell:last-child {\n    border-bottom: none;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .client-info {\n    width: 100%;\n  }\n\n  .client-details {\n    flex: 1;\n  }\n\n  .document-type-badge,\n  .status-badge {\n    align-self: flex-start;\n  }\n\n  .amount-content,\n  .date-content {\n    align-items: flex-start;\n  }\n\n  .action-buttons {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .action-btn {\n    flex: 1;\n    max-width: 60px;\n  }\n\n  /* Mobile fixes for simple actions */\n  .actions-simple {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  .request-actions-simple {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  /* Card view mobile adjustments */\n  .request-card {\n    margin-bottom: 1rem;\n  }\n\n  .request-card-header,\n  .request-card-body,\n  .request-card-footer {\n    padding: 1rem;\n  }\n\n  .client-info .d-flex {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.5rem;\n  }\n\n  .client-avatar {\n    align-self: center;\n  }\n\n  .request-meta .row {\n    text-align: center;\n  }\n\n  .request-card-footer .d-flex {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .request-card-footer .btn {\n    width: 100%;\n  }\n\n  /* View toggle mobile */\n  .btn-group {\n    width: 100%;\n  }\n\n  .btn-group .btn {\n    flex: 1;\n  }\n}\n\n@media (max-width: 576px) {\n  .empty-state {\n    padding: 2rem 1rem;\n  }\n\n  .empty-state-icon {\n    font-size: 3rem;\n  }\n\n  .request-card-body {\n    padding: 1rem;\n  }\n\n  .document-type,\n  .request-meta {\n    padding: 0.5rem;\n  }\n}\n\n/* Compact Table Additional Styles */\n.document-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  box-shadow: 0 1px 4px rgba(23, 162, 184, 0.3);\n}\n\n.client-compact {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  width: 100%;\n}\n\n.client-avatar-tiny {\n  width: 28px;\n  height: 28px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  flex-shrink: 0;\n}\n\n.client-info-compact {\n  flex: 1;\n  min-width: 0;\n}\n\n.client-name-compact {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.8rem;\n  line-height: 1.2;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email-compact {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.request-id-small {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n}\n\n.status-compact {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-compact i {\n  font-size: 0.5rem;\n}\n\n.amount-compact {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 0.85rem;\n}\n\n.date-compact {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date-main {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.8rem;\n  line-height: 1.2;\n}\n\n.time-small {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n}\n\n.actions-compact {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n/* Fixed Actions Layout */\n.actions-compact-fixed {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  min-width: 120px;\n}\n\n.primary-actions {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.dropdown-wrapper .dropdown {\n  position: static;\n}\n\n.compact-dropdown {\n  z-index: 1050 !important;\n}\n\n.compact-dropdown .dropdown-item {\n  padding: 0.5rem 1rem !important;\n  font-size: 0.875rem !important;\n  white-space: nowrap !important;\n  display: flex !important;\n  align-items: center !important;\n}\n\n.compact-dropdown .dropdown-item:hover {\n  background-color: #f8f9fa !important;\n}\n\n.compact-dropdown .dropdown-divider {\n  margin: 0.25rem 0 !important;\n}\n\n/* Ensure dropdown appears above table rows */\n.compact-row {\n  position: relative;\n  z-index: 1;\n}\n\n.compact-row:hover {\n  z-index: 2;\n}\n\n.dropdown-wrapper .dropdown.show {\n  z-index: 1051 !important;\n}\n\n.compact-dropdown.show {\n  display: block !important;\n}\n\n.action-btn-sm {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.75rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.view-btn-sm {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn-sm:hover {\n  background: #bbdefb;\n  transform: translateY(-1px);\n}\n\n.approve-btn-sm {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn-sm:hover {\n  background: #c8e6c9;\n  transform: translateY(-1px);\n}\n\n.reject-btn-sm {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn-sm:hover {\n  background: #ffcdd2;\n  transform: translateY(-1px);\n}\n\n.more-btn-sm {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn-sm:hover {\n  background: #e0e0e0;\n  transform: translateY(-1px);\n}\n\n.more-btn-sm::after {\n  display: none;\n}\n\n/* Simple Actions Layout */\n.actions-simple {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  justify-content: center;\n}\n\n.request-actions-simple {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n/* Disabled button styles */\n.btn:disabled,\n.action-btn-sm:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n\n.btn:disabled:hover,\n.action-btn-sm:disabled:hover {\n  transform: none;\n  box-shadow: none;\n}\n\n/* Status-based button styling */\n.btn-outline-success:disabled {\n  background-color: #f8f9fa;\n  border-color: #28a745;\n  color: #28a745;\n}\n\n.btn-outline-danger:disabled {\n  background-color: #f8f9fa;\n  border-color: #dc3545;\n  color: #dc3545;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAmBpBA,KAAK,EAAC;AAAqB;;;EAWRA,KAAK,EAAC,kDAAkD;EAACC,KAA0B,EAA1B;IAAA;EAAA;;;EAOjED,KAAK,EAAC;AAAsB;;;EAEbA,KAAK,EAAC,gDAAgD;EAACE,IAAI,EAAC;;;EAOhFF,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAA6D;;EAEjEA,KAAK,EAAC;AAAiB;;;EACCA,KAAK,EAAC;;;EAM9BA,KAAK,EAAC;AAAiC;;EAErCA,KAAK,EAAC;AAAuB;;;EAE3BA,KAAK,EAAC;;;;EACNA,KAAK,EAAC;;;;;EA0BhBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAA2B;;EAO3CA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAA2B;;EAO3CA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAA2B;;EAO3CA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAA2B;;;EAU1BA,KAAK,EAAC;;;EAIvBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;;EASrBA,KAAK,EAAC;AAAe;;EAQrBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAoB;;;EAcCA,KAAK,EAAC;;;EACvCA,KAAK,EAAC;AAA6B;;EAClCA,KAAK,EAAC;AAAuB;;EAK9BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAe;;;EASrBA,KAAK,EAAC;AAAe;;EASrBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;;EAgB5BA,KAAK,EAAC;AAAwD;;EAC5DA,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC,WAAW;EAACE,IAAI,EAAC,OAAO;EAAC,YAAU,EAAC;;;EAY1CF,KAAK,EAAC;AAAiC;;EACpCA,KAAK,EAAC;AAAkB;;EAc7BA,KAAK,EAAC;AAAiC;;;EASdA,KAAK,EAAC;;;;EAEFA,KAAK,EAAC;;;EAS5BA,KAAK,EAAC;AAAS;;EAIhBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkD;;EACtDA,KAAK,EAAC;AAAiC;;;EAOrCA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAkB;;EAG7BA,KAAK,EAAC;AAAwB;;;;;EA2BlCA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAsC;;EAKzCA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAY;;EAM1BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiC;;EAEpCA,KAAK,EAAC;AAAmD;;EAO9DA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAW;;EAOnBA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAsB;;EAOrCA,KAAK,EAAC;AAAc;;EAChBA,KAAK,EAAC;AAAY;;EAQxBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAc;;;;;EA8BvBA,KAAK,EAAC;AAAwB;;;EAGNA,KAAK,EAAC;;;EAW5BA,KAAK,EAAC;AAAuB;;EAElCA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAA8B;;;EAkBtCA,KAAK,EAAC;AAAoB;;EAMtBA,KAAK,EAAC;AAAyB;;;EAU/BA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAoB;;EACvBA,KAAK,EAAC;AAAgB;;EACtBA,KAAK,EAAC;AAAkB;;EAK7BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAgB;;EAIpBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAqB;;EAC3BA,KAAK,EAAC;AAAsB;;EAMlCA,KAAK,EAAC;AAAwB;;EAC3BA,KAAK,EAAC;AAAgB;;EAOzBA,KAAK,EAAC;AAAsB;;EAQ5BA,KAAK,EAAC;AAAsB;;EACzBA,KAAK,EAAC;AAAgB;;EAIzBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAY;;EAKvBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAgB;;;;;;EA8BCA,KAAK,EAAC;;;EACnC,YAAU,EAAC;AAAqB;;EAC/BA,KAAK,EAAC;AAAsD;;;;EAyBrBA,KAAK,EAAC,yBAAyB;EAACG,QAAQ,EAAC,IAAI;EAACF,KAA0C,EAA1C;IAAA;EAAA;;;EACxFD,KAAK,EAAC;AAA+C;;EACnDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAMpBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAK;;EAETA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EACPA,KAAK,EAAC;AAAe;;EAG1BA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAQpBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;;EAKdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;;EAIZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;;EAQmBA,KAAK,EAAC;;;EAI3CA,KAAK,EAAC;AAAW;;;;;EAGbA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAQhBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAUxBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAM;;;EASZA,KAAK,EAAC;AAAM;;EASZA,KAAK,EAAC;AAAc;;;;;;EAgCEA,KAAK,EAAC;;;EAC1BA,KAAK,EAAC;AAAM;;EAUZA,KAAK,EAAC;AAAQ;;;EAepBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAA2B;;EAU7CA,KAAK,EAAC;AAAM;;EAIVA,KAAK,EAAC;AAAW;;;EACkEA,KAAK,EAAC;;;EAUnFA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAiB;;EAInBA,KAAK,EAAC;AAAiB;;EAE3BA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAM;;;EAGmBA,KAAK,EAAC;;;;EAGRA,KAAK,EAAC;;;;EAOlCA,KAAK,EAAC;;;EAOnBA,KAAK,EAAC;AAAc;;;EAeyBA,KAAK,EAAC,yBAAyB;EAACG,QAAQ,EAAC,IAAI;EAACF,KAA0C,EAA1C;IAAA;EAAA;;;EAC/FD,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAOpBA,KAAK,EAAC;AAAY;;EAMhBA,KAAK,EAAC;AAAM;;EAEXA,KAAK,EAAC;AAAoB;;EAO3BA,KAAK,EAAC;AAAM;;;EAYmBA,KAAK,EAAC;;;EAQvCA,KAAK,EAAC;AAAc;;;;;;;;;;;;uBAx+BvCI,mBAAA,CA2/BQ,OA3/BRC,UA2/BQ,GA1/BNC,YAAA,CASEC,sBAAA;IARCC,QAAQ,EAAEC,KAAA,CAAAC,SAAS,EAAEC,UAAU;IAC/BC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBE,eAAc,EAAED,QAAA,CAAAE,mBAAmB;IACnCC,oBAAoB,EAAEH,QAAA,CAAAI,wBAAwB;IAC9CC,YAAW,EAAEL,QAAA,CAAAM,gBAAgB;IAC7BC,QAAM,EAAEP,QAAA,CAAAQ;sKAGXC,mBAAA,oBAAuB,EACvBC,mBAAA,CAIO;IAHLzB,KAAK,EAAA0B,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GACHlB,KAAA,CAAAI,gBAAgB,IAAIJ,KAAA,CAAAmB;IAAQ;IAC9CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiB,kBAAA,IAAAjB,QAAA,CAAAiB,kBAAA,IAAAD,IAAA,CAAkB;2BAG5BN,mBAAA,CAu+BQ,OAv+BRQ,UAu+BQ,GAt+BN3B,YAAA,CAME4B,uBAAA;IALCC,SAAS,EAAE1B,KAAA,CAAAI,gBAAgB;IAC3BC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBsB,YAAW,EAAErB,QAAA,CAAAsB,gBAAgB;IAC7Bf,QAAM,EAAEP,QAAA,CAAAQ,YAAY;IACpBe,eAAc,EAAEvB,QAAA,CAAAE;uGAGnBQ,mBAAA,CA69BS;IA79BHzB,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,qBAAgCjB,KAAA,CAAAI;IAAgB;MACxEW,mBAAA,mBAAsB,EACXf,KAAA,CAAA8B,OAAO,I,cAAlBnC,mBAAA,CAIM,OAJNoC,UAIM,EAAAV,MAAA,SAAAA,MAAA,QAHJL,mBAAA,CAEM;IAFDzB,KAAK,EAAC,6BAA6B;IAACE,IAAI,EAAC;MAC5CuB,mBAAA,CAA+C;IAAzCzB,KAAK,EAAC;EAAiB,GAAC,YAAU,E,yCAK5CI,mBAAA,CA6hBQqC,SAAA;IAAAC,GAAA;EAAA,IA9hBRlB,mBAAA,kBAAqB,EACrBC,mBAAA,CA6hBQ,OA7hBRkB,UA6hBQ,GA5hBNnB,mBAAA,mBAAsB,EACXf,KAAA,CAAAmC,YAAY,I,cAAvBxC,mBAAA,CAIM,OAJNyC,UAIM,G,4BAHJpB,mBAAA,CAAgD;IAA7CzB,KAAK,EAAC;EAAkC,6B,iBAAK,GAChD,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAmC,YAAY,IAAG,GAClB,iBAAAnB,mBAAA,CAA+F;IAAvFsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEvC,KAAA,CAAAmC,YAAY;IAAO,YAAU,EAAC;6CAGhFpB,mBAAA,iBAAoB,EACpBC,mBAAA,CAuCM,OAvCNwB,UAuCM,GAtCJxB,mBAAA,CAqCM,OArCNyB,UAqCM,GApCJzB,mBAAA,CAmCM,OAnCN0B,UAmCM,GAlCJ1B,mBAAA,CAOM,cANJA,mBAAA,CAKI,KALJ2B,UAKI,GAJU3C,KAAA,CAAA4C,WAAW,I,cAAvBjD,mBAAA,CAGO,QAHPkD,WAGO,G,4BAFL7B,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,6B,iBAAK,iBACzB,GAAA8C,gBAAA,CAAG/B,QAAA,CAAAwC,UAAU,CAAC9C,KAAA,CAAA4C,WAAW,kB,4CAI7C5B,mBAAA,CAyBM,OAzBN+B,WAyBM,GAxBJhC,mBAAA,gCAAmC,EACnCC,mBAAA,CAMM,OANNgC,WAMM,GALJhC,mBAAA,CAIO;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASjB,KAAA,CAAAiD,kBAAkB;MACPjD,KAAA,CAAAiD,kBAAkB,I,cAAvDtD,mBAAA,CAA6D,KAA7DuD,WAA6D,M,cAC7DvD,mBAAA,CAAmC,KAAnCwD,WAAmC,I,iBAAA,GACnC,GAAAd,gBAAA,CAAGrC,KAAA,CAAAiD,kBAAkB,qC,oBAIzBjC,mBAAA,CAES;IAFDzB,KAAK,EAAC,kCAAkC;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAA8C,iBAAA,IAAA9C,QAAA,CAAA8C,iBAAA,IAAA9B,IAAA,CAAiB;IAAG+B,KAAK,EAAErD,KAAA,CAAAiD,kBAAkB;MACpGjC,mBAAA,CAAwE;IAArEzB,KAAK,EAAA0B,eAAA,EAAC,KAAK,EAASjB,KAAA,CAAAiD,kBAAkB;yDAE3CjC,mBAAA,CAGS;IAHDzB,KAAK,EAAC,gCAAgC;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEvC,KAAA,CAAAsD,WAAW,IAAItD,KAAA,CAAAsD,WAAW;kCAC/EtC,mBAAA,CAAkC;IAA/BzB,KAAK,EAAC;EAAoB,6B,iBAAK,GAClC,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAsD,WAAW,sBAAqB,WACrC,gB,GACAvC,mBAAA,wNAGa,EACbC,mBAAA,CAGS;IAHDzB,KAAK,EAAC,wBAAwB;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiD,mBAAA,IAAAjD,QAAA,CAAAiD,mBAAA,IAAAjC,IAAA,CAAmB;IAAGkC,QAAQ,EAAExD,KAAA,CAAA8B;MAC7Ed,mBAAA,CAAoE;IAAjEzB,KAAK,EAAA0B,eAAA,EAAC,sBAAsB;MAAA,WAAsBjB,KAAA,CAAA8B;IAAO;wEAAQ,WAEtE,G,uCAMRf,mBAAA,wBAA2B,EAC3BC,mBAAA,CAqDM,OArDNyC,WAqDM,GApDJzC,mBAAA,CAYM,OAZN0C,WAYM,GAXJ1C,mBAAA,CAUM,OAVN2C,WAUM,GATJ3C,mBAAA,CAQM,OARN4C,WAQM,GAPJ5C,mBAAA,CAMM,OANN6C,WAMM,GALJ7C,mBAAA,CAGM,OAHN8C,WAGM,G,4BAFJ9C,mBAAA,CAAkF;IAA7EzB,KAAK,EAAC;EAAkD,GAAC,gBAAc,sBAC5EyB,mBAAA,CAA0E,OAA1E+C,WAA0E,EAAA1B,gBAAA,CAAhCrC,KAAA,CAAAgE,YAAY,CAACC,KAAK,sB,+BAE9DjD,mBAAA,CAAqD;IAAlDzB,KAAK,EAAC;EAAuC,4B,SAKxDyB,mBAAA,CAYM,OAZNkD,WAYM,GAXJlD,mBAAA,CAUM,OAVNmD,WAUM,GATJnD,mBAAA,CAQM,OARNoD,WAQM,GAPJpD,mBAAA,CAMM,OANNqD,WAMM,GALJrD,mBAAA,CAGM,OAHNsD,WAGM,G,4BAFJtD,mBAAA,CAA2E;IAAtEzB,KAAK,EAAC;EAAkD,GAAC,SAAO,sBACrEyB,mBAAA,CAA4E,OAA5EuD,WAA4E,EAAAlC,gBAAA,CAAlCrC,KAAA,CAAAgE,YAAY,CAACQ,OAAO,sB,+BAEhExD,mBAAA,CAAkD;IAA/CzB,KAAK,EAAC;EAAoC,4B,SAKrDyB,mBAAA,CAYM,OAZNyD,WAYM,GAXJzD,mBAAA,CAUM,OAVN0D,WAUM,GATJ1D,mBAAA,CAQM,OARN2D,WAQM,GAPJ3D,mBAAA,CAMM,OANN4D,WAMM,GALJ5D,mBAAA,CAGM,OAHN6D,WAGM,G,4BAFJ7D,mBAAA,CAA6E;IAAxEzB,KAAK,EAAC;EAAkD,GAAC,WAAS,sBACvEyB,mBAAA,CAA8E,OAA9E8D,WAA8E,EAAAzC,gBAAA,CAApCrC,KAAA,CAAAgE,YAAY,CAACe,SAAS,sB,+BAElE/D,mBAAA,CAAyD;IAAtDzB,KAAK,EAAC;EAA2C,4B,SAK5DyB,mBAAA,CAYM,OAZNgE,WAYM,GAXJhE,mBAAA,CAUM,OAVNiE,WAUM,GATJjE,mBAAA,CAQM,OARNkE,WAQM,GAPJlE,mBAAA,CAMM,OANNmE,WAMM,GALJnE,mBAAA,CAGM,OAHNoE,WAGM,G,4BAFJpE,mBAAA,CAAyE;IAApEzB,KAAK,EAAC;EAA+C,GAAC,UAAQ,sBACnEyB,mBAAA,CAA6E,OAA7EqE,WAA6E,EAAAhD,gBAAA,CAAnCrC,KAAA,CAAAgE,YAAY,CAACsB,QAAQ,sB,+BAEjEtE,mBAAA,CAAsD;IAAnDzB,KAAK,EAAC;EAAwC,4B,WAO3DwB,mBAAA,mBAAsB,EACXf,KAAA,CAAAsD,WAAW,I,cAAtB3D,mBAAA,CAqDM,OArDN4F,WAqDM,G,4BApDJvE,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAkB,IAC3ByB,mBAAA,CAAyD;IAArDzB,KAAK,EAAC;EAA0B,GAAC,iBAAe,E,sBAEtDyB,mBAAA,CAgDM,OAhDNwE,WAgDM,GA/CJxE,mBAAA,CA8CM,OA9CNyE,WA8CM,GA7CJzE,mBAAA,CASM,OATN0E,WASM,G,4BARJ1E,mBAAA,CAAwC;IAAjCzB,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCyB,mBAAA,CAMC;IALCsB,IAAI,EAAC,MAAM;IACX/C,KAAK,EAAC,cAAc;+DACXS,KAAA,CAAA2F,OAAO,CAACC,MAAM,GAAArD,MAAA;IACvBsD,WAAW,EAAC,0CAA0C;IACrDC,OAAK,EAAAzE,MAAA,QAAAA,MAAA,MAAA0E,SAAA,KAAAzE,IAAA,KAAQhB,QAAA,CAAA0F,YAAA,IAAA1F,QAAA,CAAA0F,YAAA,IAAA1E,IAAA,CAAY;iEAFjBtB,KAAA,CAAA2F,OAAO,CAACC,MAAM,E,KAK3B5E,mBAAA,CAQM,OARNiF,WAQM,G,4BAPJjF,mBAAA,CAAwC;IAAjCzB,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCyB,mBAAA,CAKS;IALDzB,KAAK,EAAC,aAAa;+DAAUS,KAAA,CAAA2F,OAAO,CAACO,MAAM,GAAA3D,MAAA;kCACjDvB,mBAAA,CAAsC;IAA9BmF,KAAK,EAAC;EAAE,GAAC,cAAY,uB,kBAC7BxG,mBAAA,CAESqC,SAAA,QAAAoE,WAAA,CAFgBpG,KAAA,CAAAqG,aAAa,EAAvBH,MAAM;yBAArBvG,mBAAA,CAES;MAFgCsC,GAAG,EAAEiE,MAAM,CAACI,EAAE;MAAGH,KAAK,EAAED,MAAM,CAACK;wBACnEjG,QAAA,CAAAkG,YAAY,CAACN,MAAM,CAACK,WAAW,yBAAAE,WAAA;2EAHDzG,KAAA,CAAA2F,OAAO,CAACO,MAAM,E,KAOrDlF,mBAAA,CAOM,OAPN0F,WAOM,G,4BANJ1F,mBAAA,CAA+C;IAAxCzB,KAAK,EAAC;EAAY,GAAC,eAAa,sB,gBACvCyB,mBAAA,CAIS;IAJDzB,KAAK,EAAC,aAAa;+DAAUS,KAAA,CAAA2F,OAAO,CAACgB,aAAa,GAAApE,MAAA;kCACxDvB,mBAAA,CAAmC;IAA3BmF,KAAK,EAAC;EAAE,GAAC,WAAS,qBAC1BnF,mBAAA,CAA8D;IAAtDmF,KAAK,EAAC;EAAoB,GAAC,oBAAkB,qBACrDnF,mBAAA,CAAsC;IAA9BmF,KAAK,EAAC;EAAQ,GAAC,QAAM,oB,2CAHMnG,KAAA,CAAA2F,OAAO,CAACgB,aAAa,E,KAM5D3F,mBAAA,CAGM,OAHN4F,WAGM,G,4BAFJ5F,mBAAA,CAA2C;IAApCzB,KAAK,EAAC;EAAY,GAAC,WAAS,sB,gBACnCyB,mBAAA,CAAoE;IAA7DsB,IAAI,EAAC,MAAM;IAAC/C,KAAK,EAAC,cAAc;+DAAUS,KAAA,CAAA2F,OAAO,CAACkB,SAAS,GAAAtE,MAAA;iDAAjBvC,KAAA,CAAA2F,OAAO,CAACkB,SAAS,E,KAEpE7F,mBAAA,CAGM,OAHN8F,WAGM,G,4BAFJ9F,mBAAA,CAAyC;IAAlCzB,KAAK,EAAC;EAAY,GAAC,SAAO,sB,gBACjCyB,mBAAA,CAAkE;IAA3DsB,IAAI,EAAC,MAAM;IAAC/C,KAAK,EAAC,cAAc;iEAAUS,KAAA,CAAA2F,OAAO,CAACoB,OAAO,GAAAxE,MAAA;iDAAfvC,KAAA,CAAA2F,OAAO,CAACoB,OAAO,E,KAElE/F,mBAAA,CASM,OATNgG,WASM,GARJhG,mBAAA,CAOM,OAPNiG,WAOM,GANJjG,mBAAA,CAES;IAFDzB,KAAK,EAAC,wBAAwB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA0F,YAAA,IAAA1F,QAAA,CAAA0F,YAAA,IAAA1E,IAAA,CAAY;kCACzDN,mBAAA,CAA6B;IAA1BzB,KAAK,EAAC;EAAe,2B,IAE1ByB,mBAAA,CAES;IAFDzB,KAAK,EAAC,kCAAkC;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA4G,YAAA,IAAA5G,QAAA,CAAA4G,YAAA,IAAA5F,IAAA,CAAY;kCACnEN,mBAAA,CAA4B;IAAzBzB,KAAK,EAAC;EAAc,2B,mDAQnCwB,mBAAA,wBAA2B,EAChBf,KAAA,CAAAmH,gBAAgB,CAACC,MAAM,Q,cAAlCzH,mBAAA,CAyCM,OAzCN0H,WAyCM,GAxCJrG,mBAAA,CAKM,OALNsG,WAKM,GAJJtG,mBAAA,CAGK,MAHLuG,WAGK,G,4BAFHvG,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,6B,iBAAK,iBACnB,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAmH,gBAAgB,CAACC,MAAM,IAAG,aAC7C,gB,KAEFpG,mBAAA,CAiCM,OAjCNwG,WAiCM,GAhCJxG,mBAAA,CA+BM,OA/BNyG,WA+BM,GA9BJzG,mBAAA,CAQM,OARN0G,WAQM,G,4BAPJ1G,mBAAA,CAAwC;IAAjCzB,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCyB,mBAAA,CAKS;IALDzB,KAAK,EAAC,aAAa;iEAAUS,KAAA,CAAA2H,UAAU,GAAApF,MAAA;kCAC7CvB,mBAAA,CAAuC;IAA/BmF,KAAK,EAAC;EAAE,GAAC,eAAa,uB,kBAC9BxG,mBAAA,CAESqC,SAAA,QAAAoE,WAAA,CAFgBpG,KAAA,CAAAqG,aAAa,EAAvBH,MAAM;yBAArBvG,mBAAA,CAES;MAFgCsC,GAAG,EAAEiE,MAAM,CAACI,EAAE;MAAGH,KAAK,EAAED,MAAM,CAACI;OAAI,aAChE,GAAAjE,gBAAA,CAAG/B,QAAA,CAAAkG,YAAY,CAACN,MAAM,CAACK,WAAW,yBAAAqB,WAAA;2EAHX5H,KAAA,CAAA2H,UAAU,E,KAOjD3G,mBAAA,CAQM,OARN6G,WAQM,G,4BAPJ7G,mBAAA,CAAmD;IAA5CzB,KAAK,EAAC;EAAY,GAAC,mBAAiB,sB,gBAC3CyB,mBAAA,CAKC;IAJCsB,IAAI,EAAC,MAAM;IACX/C,KAAK,EAAC,cAAc;iEACXS,KAAA,CAAA8H,UAAU,GAAAvF,MAAA;IACnBsD,WAAW,EAAC;iDADH7F,KAAA,CAAA8H,UAAU,E,KAIvB9G,mBAAA,CAWM,OAXN+G,WAWM,GAVJ/G,mBAAA,CASM,OATNgH,WASM,GARJhH,mBAAA,CAGS;IAHDzB,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA2H,iBAAA,IAAA3H,QAAA,CAAA2H,iBAAA,IAAA3G,IAAA,CAAiB;IAAGkC,QAAQ,GAAGxD,KAAA,CAAA2H;kCACrE3G,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,4B,iBAAK,SAElC,E,gCACAyB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,2BAA2B;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEvC,KAAA,CAAAmH,gBAAgB;kCAChEnG,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,UAEnC,E,mDAOVwB,mBAAA,iBAAoB,EACpBC,mBAAA,CAmCM,OAnCNkH,WAmCM,GAlCJlH,mBAAA,CA0BM,OA1BNmH,WA0BM,GAzBJnH,mBAAA,CAUM,OAVNoH,WAUM,G,gBATJpH,mBAAA,CAAuH;IAAhHsB,IAAI,EAAC,OAAO;IAAC/C,KAAK,EAAC,WAAW;IAAC8I,IAAI,EAAC,UAAU;IAAC/B,EAAE,EAAC,UAAU;iEAAUtG,KAAA,CAAAsI,QAAQ,GAAA/F,MAAA;IAAE4D,KAAK,EAAC,MAAM;IAACoC,YAAY,EAAC;kDAApCvI,KAAA,CAAAsI,QAAQ,E,+BACrFtH,mBAAA,CAEQ;IAFDzB,KAAK,EAAC,gCAAgC;IAACiJ,GAAG,EAAC;MAChDxH,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,I,iBAAK,QACtC,E,sCAEAyB,mBAAA,CAAyH;IAAlHsB,IAAI,EAAC,OAAO;IAAC/C,KAAK,EAAC,WAAW;IAAC8I,IAAI,EAAC,UAAU;IAAC/B,EAAE,EAAC,WAAW;iEAAUtG,KAAA,CAAAsI,QAAQ,GAAA/F,MAAA;IAAE4D,KAAK,EAAC,OAAO;IAACoC,YAAY,EAAC;kDAArCvI,KAAA,CAAAsI,QAAQ,E,+BACtFtH,mBAAA,CAEQ;IAFDzB,KAAK,EAAC,gCAAgC;IAACiJ,GAAG,EAAC;MAChDxH,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,I,iBAAK,QACnC,E,wBAGFyB,mBAAA,CAYM,OAZNyH,WAYM,GAXJzH,mBAAA,CAIO,QAJP0H,WAIO,EAJwB,WACrB,GAAArG,gBAAA,EAAKrC,KAAA,CAAA2I,UAAU,CAACC,WAAW,QAAQ5I,KAAA,CAAA2I,UAAU,CAACE,YAAY,QAAQ,KAC1E,GAAAxG,gBAAA,CAAGyG,IAAI,CAACC,GAAG,CAAC/I,KAAA,CAAA2I,UAAU,CAACC,WAAW,GAAG5I,KAAA,CAAA2I,UAAU,CAACE,YAAY,EAAE7I,KAAA,CAAA2I,UAAU,CAACK,UAAU,KAAI,MACpF,GAAA3G,gBAAA,CAAGrC,KAAA,CAAA2I,UAAU,CAACK,UAAU,IAAG,YAChC,iB,gBACAhI,mBAAA,CAKS;IALDzB,KAAK,EAAC,4BAA4B;IAACC,KAAoB,EAApB;MAAA;IAAA,CAAoB;iEAAUQ,KAAA,CAAA2I,UAAU,CAACE,YAAY,GAAAtG,MAAA;IAAG0G,QAAM,EAAA5H,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEjC,QAAA,CAAA4I,kBAAkB,CAAClJ,KAAA,CAAA2I,UAAU,CAACE,YAAY;kCACnJ7H,mBAAA,CAAuC;IAA/BmF,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BnF,mBAAA,CAAuC;IAA/BmF,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BnF,mBAAA,CAAuC;IAA/BmF,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BnF,mBAAA,CAAyC;IAAjCmF,KAAK,EAAC;EAAK,GAAC,cAAY,oB,2DAJuCnG,KAAA,CAAA2I,UAAU,CAACE,YAAY,E,OASpG7H,mBAAA,CAKM,OALNmI,WAKM,GAJ8EnJ,KAAA,CAAAoJ,QAAQ,CAAChC,MAAM,Q,cAAjGzH,mBAAA,CAGS;;IAHDJ,KAAK,EAAC,kCAAkC;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA+I,iBAAA,IAAA/I,QAAA,CAAA+I,iBAAA,IAAA/H,IAAA,CAAiB;kCACxEN,mBAAA,CAAwC;IAArCzB,KAAK,EAAC;EAA0B,6B,iBAAK,GACxC,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAmH,gBAAgB,CAACC,MAAM,KAAKpH,KAAA,CAAAoJ,QAAQ,CAAChC,MAAM,iD,4CAKpDrG,mBAAA,eAAkB,EACPf,KAAA,CAAAsI,QAAQ,e,cAAnB3I,mBAAA,CAyIM,OAzIN2J,WAyIM,GAxIJvI,mBAAA,iBAAoB,EACTf,KAAA,CAAAoJ,QAAQ,CAAChC,MAAM,U,cAA1BzH,mBAAA,CAMM,OANN4J,WAMM,EAAAlI,MAAA,SAAAA,MAAA,QALJL,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAuB,IAChCyB,mBAAA,CAA6C;IAA1CzB,KAAK,EAAC;EAA+B,G,qBAE1CyB,mBAAA,CAA2D;IAAvDzB,KAAK,EAAC;EAAiB,GAAC,4BAA0B,qBACtDyB,mBAAA,CAAuF;IAApFzB,KAAK,EAAC;EAAY,GAAC,+DAA6D,oB,qBAIrFI,mBAAA,CA6HMqC,SAAA;IAAAC,GAAA;EAAA,IA9HNlB,mBAAA,mBAAsB,EACtBC,mBAAA,CA6HM,OA7HNwI,WA6HM,I,kBA5HJ7J,mBAAA,CA2HMqC,SAAA,QAAAoE,WAAA,CA3HiBpG,KAAA,CAAAoJ,QAAQ,EAAnBK,OAAO;yBAAnB9J,mBAAA,CA2HM;MA3H4BsC,GAAG,EAAEwH,OAAO,CAACnD,EAAE;MAAE/G,KAAK,EAAC;QACvDyB,mBAAA,CAyHM;MAzHDzB,KAAK,EAAA0B,eAAA,EAAC,cAAc;QAAA,YAAuBjB,KAAA,CAAAmH,gBAAgB,CAACuC,QAAQ,CAACD,OAAO,CAACnD,EAAE;MAAA;QAClFvF,mBAAA,iBAAoB,EACpBC,mBAAA,CAqCM,OArCN2I,WAqCM,GApCJ3I,mBAAA,CAmCM,OAnCN4I,WAmCM,GAlCJ5I,mBAAA,CAUM,OAVN6I,WAUM,GATJ7I,mBAAA,CAKC;MAJCsB,IAAI,EAAC,UAAU;MACf/C,KAAK,EAAC,kBAAkB;MACvBuK,OAAO,EAAE9J,KAAA,CAAAmH,gBAAgB,CAACuC,QAAQ,CAACD,OAAO,CAACnD,EAAE;MAC7C2C,QAAM,EAAA1G,MAAA,IAAEjC,QAAA,CAAAyJ,sBAAsB,CAACN,OAAO,CAACnD,EAAE;2DAE5CtF,mBAAA,CAEM,OAFNgJ,WAEM,GADJhJ,mBAAA,CAAkE,QAAlEiJ,WAAkE,EAAA5H,gBAAA,CAAhCoH,OAAO,CAACS,cAAc,iB,KAG5DlJ,mBAAA,CAsBM,OAtBNmJ,WAsBM,GArBJnJ,mBAAA,CAES;MAFDzB,KAAK,EAAC,gCAAgC;MAAE6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAA8J,kBAAkB,CAACX,OAAO,CAACnD,EAAE;MAAGjD,KAAK,EAAC;yCAC3FrC,mBAAA,CAA0B;MAAvBzB,KAAK,EAAC;IAAY,2B,kCAGfe,QAAA,CAAA+J,UAAU,CAACZ,OAAO,K,cAD1B9J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,wBAAwB;MAC7B6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAgK,YAAY,CAACb,OAAO;MAC5BpG,KAAK,EAAC,eAAe;MACpBG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B,uEAGjBe,QAAA,CAAAiK,SAAS,CAACd,OAAO,K,cADzB9J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,uBAAuB;MAC5B6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAkK,oBAAoB,CAACf,OAAO;MACpCpG,KAAK,EAAC,cAAc;MACnBG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B,6EAM/BwB,mBAAA,eAAkB,EAClBC,mBAAA,CAmDM,OAnDNyJ,WAmDM,GAlDJ1J,mBAAA,iBAAoB,EACpBC,mBAAA,CAUM,OAVN0J,WAUM,GATJ1J,mBAAA,CAQM,OARN2J,WAQM,G,4BAPJ3J,mBAAA,CAEM;MAFDzB,KAAK,EAAC;IAAe,IACxByB,mBAAA,CAAqD;MAAlDzB,KAAK,EAAC;IAAuC,G,sBAElDyB,mBAAA,CAGM,cAFJA,mBAAA,CAAuD,MAAvD4J,WAAuD,EAAAvI,gBAAA,CAA3BoH,OAAO,CAACoB,WAAW,kBAC/C7J,mBAAA,CAA4D,SAA5D8J,WAA4D,EAAAzI,gBAAA,CAA/BoH,OAAO,CAACsB,YAAY,iB,OAKvDhK,mBAAA,mBAAsB,EACtBC,mBAAA,CAOM,OAPNgK,WAOM,GANJhK,mBAAA,CAKM,OALNiK,WAKM,G,4BAJJjK,mBAAA,CAAyC;MAAtCzB,KAAK,EAAC;IAA2B,6BACpCyB,mBAAA,CAEO,QAFPkK,WAEO,EAAA7I,gBAAA,CADFoH,OAAO,CAAC9C,aAAa,iB,KAK9B5F,mBAAA,uBAA0B,EAC1BC,mBAAA,CAiBM,OAjBNmK,WAiBM,GAhBJnK,mBAAA,CAeM,OAfNoK,WAeM,GAdJpK,mBAAA,CAOM,OAPNqK,WAOM,GANJrK,mBAAA,CAKM,OALNsK,WAKM,G,4BAJJtK,mBAAA,CAAgD;MAAzCzB,KAAK,EAAC;IAAoB,GAAC,QAAM,sBACxCyB,mBAAA,CAEO;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,QAAeX,QAAA,CAAAiL,cAAc,CAAC9B,OAAO,CAAClD,WAAW;wBAC/DjG,QAAA,CAAAkG,YAAY,CAACiD,OAAO,CAAClD,WAAW,yB,KAIzCvF,mBAAA,CAKM,OALNwK,WAKM,GAJJxK,mBAAA,CAGM,OAHNyK,WAGM,G,4BAFJzK,mBAAA,CAAgD;MAAzCzB,KAAK,EAAC;IAAoB,GAAC,QAAM,sBACxCyB,mBAAA,CAAiF,QAAjF0K,WAAiF,EAAArJ,gBAAA,CAA3C/B,QAAA,CAAAqL,cAAc,CAAClC,OAAO,CAACmC,SAAS,kB,SAM9E7K,mBAAA,UAAa,EACbC,mBAAA,CAKM,OALN6K,WAKM,GAJJ7K,mBAAA,CAGQ,SAHR8K,WAGQ,G,4BAFN9K,mBAAA,CAAwC;MAArCzB,KAAK,EAAC;IAA0B,6B,iBAAK,aAC9B,GAAA8C,gBAAA,CAAG/B,QAAA,CAAAyL,UAAU,CAACtC,OAAO,CAACuC,YAAY,kB,OAKlDjL,mBAAA,iBAAoB,EACpBC,mBAAA,CAwBM,OAxBNiL,YAwBM,GAvBJjL,mBAAA,CAsBM,OAtBNkL,YAsBM,GArBJlL,mBAAA,CAES;MAFDzB,KAAK,EAAC,0CAA0C;MAAE6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAA8J,kBAAkB,CAACX,OAAO,CAACnD,EAAE;yCAC5FtF,mBAAA,CAA+B;MAA5BzB,KAAK,EAAC;IAAiB,4B,iBAAK,OACjC,E,mCAEQe,QAAA,CAAA+J,UAAU,CAACZ,OAAO,K,cAD1B9J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,wBAAwB;MAC7B6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAgK,YAAY,CAACb,OAAO;MAC3BpG,KAAK,EAAE,iBAAiB;MACxBG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B,wEAGjBe,QAAA,CAAAiK,SAAS,CAACd,OAAO,K,cADzB9J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,uBAAuB;MAC5B6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAkK,oBAAoB,CAACf,OAAO;MACnCpG,KAAK,EAAE,gBAAgB;MACvBG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B;yGAUrCI,mBAAA,CAiIMqC,SAAA;IAAAC,GAAA;EAAA,IAlINlB,mBAAA,gBAAmB,EACnBC,mBAAA,CAiIM,OAjINmL,YAiIM,GA/HJpL,mBAAA,iBAAoB,EACTf,KAAA,CAAAoJ,QAAQ,CAAChC,MAAM,U,cAA1BzH,mBAAA,CAQM,OARNyM,YAQM,EAAA/K,MAAA,SAAAA,MAAA,Q,kXAGN1B,mBAAA,CAkHMqC,SAAA;IAAAC,GAAA;EAAA,IAnHNlB,mBAAA,0BAA6B,EAC7BC,mBAAA,CAkHM,OAlHNqL,YAkHM,GAjHJtL,mBAAA,kBAAqB,EACrBC,mBAAA,CAgBM,OAhBNsL,YAgBM,GAfJtL,mBAAA,CAOM,OAPNuL,YAOM,GANJvL,mBAAA,CAKC;IAJCsB,IAAI,EAAC,UAAU;IACf/C,KAAK,EAAC,kBAAkB;IACvBuK,OAAO,EAAE9J,KAAA,CAAAmH,gBAAgB,CAACC,MAAM,KAAKpH,KAAA,CAAAoJ,QAAQ,CAAChC,MAAM,IAAIpH,KAAA,CAAAoJ,QAAQ,CAAChC,MAAM;IACvE6B,QAAM,EAAA5H,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA+I,iBAAA,IAAA/I,QAAA,CAAA+I,iBAAA,IAAA/H,IAAA,CAAiB;4fAYhCP,mBAAA,gBAAmB,EACnBC,mBAAA,CA4FM,OA5FNwL,YA4FM,I,kBA3FJ7M,mBAAA,CA0FMqC,SAAA,QAAAoE,WAAA,CA1FiBpG,KAAA,CAAAoJ,QAAQ,EAAnBK,OAAO;yBAAnB9J,mBAAA,CA0FM;MA1F4BsC,GAAG,EAAEwH,OAAO,CAACnD,EAAE;MAC5C/G,KAAK,EAAA0B,eAAA,EAAC,aAAa;QAAA,YACGjB,KAAA,CAAAmH,gBAAgB,CAACuC,QAAQ,CAACD,OAAO,CAACnD,EAAE;MAAA;QAE7DvF,mBAAA,eAAkB,EAClBC,mBAAA,CAOM,OAPNyL,YAOM,GANJzL,mBAAA,CAKC;MAJCsB,IAAI,EAAC,UAAU;MACf/C,KAAK,EAAC,kBAAkB;MACvBuK,OAAO,EAAE9J,KAAA,CAAAmH,gBAAgB,CAACuC,QAAQ,CAACD,OAAO,CAACnD,EAAE;MAC7C2C,QAAM,EAAA1G,MAAA,IAAEjC,QAAA,CAAAyJ,sBAAsB,CAACN,OAAO,CAACnD,EAAE;8DAI9CvF,mBAAA,gBAAmB,EACnBC,mBAAA,CAKM,OALN0L,YAKM,GAJJ1L,mBAAA,CAGM,OAHN2L,YAGM,GAFJ3L,mBAAA,CAAgE,QAAhE4L,YAAgE,EAAAvK,gBAAA,CAAhCoH,OAAO,CAACS,cAAc,kBACtDlJ,mBAAA,CAAsD,QAAtD6L,YAAsD,EAAAxK,gBAAA,CAApBoH,OAAO,CAACnD,EAAE,iB,KAIhDvF,mBAAA,YAAe,EACfC,mBAAA,CAUM,OAVN8L,YAUM,GATJ9L,mBAAA,CAQM,OARN+L,YAQM,G,4BAPJ/L,mBAAA,CAEM;MAFDzB,KAAK,EAAC;IAAoB,IAC7ByB,mBAAA,CAA2B;MAAxBzB,KAAK,EAAC;IAAa,G,sBAExByB,mBAAA,CAGM,OAHNgM,YAGM,GAFJhM,mBAAA,CAAgE,OAAhEiM,YAAgE,EAAA5K,gBAAA,CAA5BoH,OAAO,CAACoB,WAAW,kBACvD7J,mBAAA,CAAkE,OAAlEkM,YAAkE,EAAA7K,gBAAA,CAA7BoH,OAAO,CAACsB,YAAY,iB,OAK/DhK,mBAAA,mBAAsB,EACtBC,mBAAA,CAKM,OALNmM,YAKM,GAJJnM,mBAAA,CAGO,QAHPoM,YAGO,G,4BAFLpM,mBAAA,CAA+B;MAA5BzB,KAAK,EAAC;IAAiB,6B,iBAAK,GAC/B,GAAA8C,gBAAA,CAAGoH,OAAO,CAAC9C,aAAa,iB,KAI5B5F,mBAAA,YAAe,EACfC,mBAAA,CAKM,OALNqM,YAKM,GAJJrM,mBAAA,CAGO;MAHDzB,KAAK,EAAA0B,eAAA,EAAC,gBAAgB,YAAmBX,QAAA,CAAAiL,cAAc,CAAC9B,OAAO,CAAClD,WAAW;oCAC/EvF,mBAAA,CAA6B;MAA1BzB,KAAK,EAAC;IAAe,6B,iBAAK,GAC7B,GAAA8C,gBAAA,CAAG/B,QAAA,CAAAkG,YAAY,CAACiD,OAAO,CAAClD,WAAW,kB,oBAIvCxF,mBAAA,YAAe,EACfC,mBAAA,CAEM,OAFNsM,YAEM,GADJtM,mBAAA,CAA2E,QAA3EuM,YAA2E,EAAAlL,gBAAA,CAA3C/B,QAAA,CAAAqL,cAAc,CAAClC,OAAO,CAACmC,SAAS,kB,GAGlE7K,mBAAA,UAAa,EACbC,mBAAA,CAKM,OALNwM,YAKM,GAJJxM,mBAAA,CAGM,OAHNyM,YAGM,GAFJzM,mBAAA,CAAqE,QAArE0M,YAAqE,EAAArL,gBAAA,CAA1C/B,QAAA,CAAAyL,UAAU,CAACtC,OAAO,CAACuC,YAAY,mBAC1DhL,mBAAA,CAAsE,QAAtE2M,YAAsE,EAAAtL,gBAAA,CAA1C/B,QAAA,CAAAwC,UAAU,CAAC2G,OAAO,CAACuC,YAAY,kB,KAI/DjL,mBAAA,aAAgB,EAChBC,mBAAA,CAwBM,OAxBN4M,YAwBM,GAvBJ5M,mBAAA,CAsBM,OAtBN6M,YAsBM,GArBJ7M,mBAAA,CAES;MAFDzB,KAAK,EAAC,2BAA2B;MAAE6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAA8J,kBAAkB,CAACX,OAAO,CAACnD,EAAE;MAAGjD,KAAK,EAAC;yCACtFrC,mBAAA,CAA0B;MAAvBzB,KAAK,EAAC;IAAY,2B,mCAGfe,QAAA,CAAA+J,UAAU,CAACZ,OAAO,K,cAD1B9J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,8BAA8B;MACnC6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAgK,YAAY,CAACb,OAAO;MAC5BpG,KAAK,EAAC,eAAe;MACpBG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B,wEAGjBe,QAAA,CAAAiK,SAAS,CAACd,OAAO,K,cADzB9J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,6BAA6B;MAClC6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAkK,oBAAoB,CAACf,OAAO;MACpCpG,KAAK,EAAC,cAAc;MACnBG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B;4IASrCwB,mBAAA,gBAAmB,EACRf,KAAA,CAAA2I,UAAU,CAACmF,UAAU,Q,cAAhCnO,mBAAA,CAuBQ,OAvBRoO,YAuBQ,GAtBJ/M,mBAAA,CAqBM,OArBNgN,YAqBM,GApBJhN,mBAAA,CAmBK,MAnBLiN,YAmBK,GAlBHjN,mBAAA,CAIK;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,WAAW;MAAAuC,QAAA,EAAqBxD,KAAA,CAAA2I,UAAU,CAACC,WAAW;IAAA;MAC9D5H,mBAAA,CAEI;IAFDzB,KAAK,EAAC,WAAW;IAAC2O,IAAI,EAAC,GAAG;IAAE9M,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA8M,cAAA,CAAA5L,MAAA,IAAUjC,QAAA,CAAA8N,UAAU,CAACpO,KAAA,CAAA2I,UAAU,CAACC,WAAW;kCAC7E5H,mBAAA,CAAmC;IAAhCzB,KAAK,EAAC;EAAqB,2B,wCAGlCI,mBAAA,CAOKqC,SAAA,QAAAoE,WAAA,CANY0C,IAAI,CAACC,GAAG,CAAC/I,KAAA,CAAA2I,UAAU,CAACmF,UAAU,OAAtCO,IAAI;yBADb1O,mBAAA,CAOK;MALFsC,GAAG,EAAEoM,IAAI;MACV9O,KAAK,EAAA0B,eAAA,EAAC,WAAW;QAAAC,MAAA,EACCmN,IAAI,KAAKrO,KAAA,CAAA2I,UAAU,CAACC;MAAW;QAEjD5H,mBAAA,CAA8E;MAA3EzB,KAAK,EAAC,WAAW;MAAC2O,IAAI,EAAC,GAAG;MAAE9M,OAAK,EAAA+M,cAAA,CAAA5L,MAAA,IAAUjC,QAAA,CAAA8N,UAAU,CAACC,IAAI;wBAAMA,IAAI,wBAAAC,YAAA,E;kCAEzEtN,mBAAA,CAIK;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,WAAW;MAAAuC,QAAA,EAAqBxD,KAAA,CAAA2I,UAAU,CAACC,WAAW,KAAK5I,KAAA,CAAA2I,UAAU,CAACmF;IAAU;MACxF9M,mBAAA,CAEI;IAFDzB,KAAK,EAAC,WAAW;IAAC2O,IAAI,EAAC,GAAG;IAAE9M,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA8M,cAAA,CAAA5L,MAAA,IAAUjC,QAAA,CAAA8N,UAAU,CAACpO,KAAA,CAAA2I,UAAU,CAACC,WAAW;kCAC7E5H,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,2B,oHAQ3CwB,mBAAA,2BAA8B,EACnBf,KAAA,CAAAuO,kBAAkB,IAAIvO,KAAA,CAAAwO,cAAc,I,cAA/C7O,mBAAA,CAmXM,OAnXN8O,YAmXM,GAlXJzN,mBAAA,CAiXM,OAjXN0N,YAiXM,GAhXJ1N,mBAAA,CA+WM,OA/WN2N,YA+WM,GA9WJ3N,mBAAA,CAMM,OANN4N,YAMM,GALJ5N,mBAAA,CAGK,MAHL6N,YAGK,G,4BAFH7N,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,6B,iBAAK,qBAClB,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAwO,cAAc,CAACtE,cAAc,iB,GAEpDlJ,mBAAA,CAAqF;IAA7EsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEvC,KAAA,CAAAuO,kBAAkB;QAEpEvN,mBAAA,CA4VM,OA5VN8N,YA4VM,GA3VJ9N,mBAAA,CA8SM,OA9SN+N,YA8SM,GA7SJhO,mBAAA,uCAA0C,EAC1CC,mBAAA,CAwKM,OAxKNgO,YAwKM,GAvKJjO,mBAAA,uBAA0B,EAC1BC,mBAAA,CAsDM,OAtDNiO,YAsDM,G,8BArDJjO,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAgF;IAA5EzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,I,iBAAK,qBAAmB,E,wBAE7EyB,mBAAA,CAiDM,OAjDNkO,YAiDM,GAhDJlO,mBAAA,CA+CM,OA/CNmO,YA+CM,GA9CJnO,mBAAA,CAmBM,OAnBNoO,YAmBM,GAlBJpO,mBAAA,CAGM,OAHNqO,YAGM,G,4BAFJrO,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAAuD,KAAvDsO,YAAuD,EAAAjN,gBAAA,CAApCrC,KAAA,CAAAwO,cAAc,CAACtE,cAAc,iB,GAElDlJ,mBAAA,CAKM,OALNuO,YAKM,G,4BAJJvO,mBAAA,CAAuD;IAAhDzB,KAAK,EAAC;EAAoB,GAAC,eAAa,sBAC/CyB,mBAAA,CAEI,KAFJwO,YAEI,GADFxO,mBAAA,CAAqE,QAArEyO,YAAqE,EAAApN,gBAAA,CAAtCrC,KAAA,CAAAwO,cAAc,CAAC7H,aAAa,iB,KAG/D3F,mBAAA,CAGM,OAHN0O,YAGM,G,4BAFJ1O,mBAAA,CAA0D;IAAnDzB,KAAK,EAAC;EAAoB,GAAC,kBAAgB,sBAClDyB,mBAAA,CAAyD,KAAzD2O,YAAyD,EAAAtN,gBAAA,CAAtCrC,KAAA,CAAAwO,cAAc,CAACoB,gBAAgB,iB,GAEpD5O,mBAAA,CAGM,OAHN6O,YAGM,G,4BAFJ7O,mBAAA,CAAyD;IAAlDzB,KAAK,EAAC;EAAoB,GAAC,iBAAe,sBACjDyB,mBAAA,CAA2E,KAA3E8O,YAA2E,EAAAzN,gBAAA,CAAxDrC,KAAA,CAAAwO,cAAc,CAACuB,eAAe,oC,KAGrD/O,mBAAA,CAyBM,OAzBNgP,YAyBM,GAxBJhP,mBAAA,CAOM,OAPNiP,YAOM,G,4BANJjP,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAII,KAJJkP,YAII,GAHFlP,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,QAAeX,QAAA,CAAAiL,cAAc,CAACvL,KAAA,CAAAwO,cAAc,CAACjI,WAAW;sBACtEjG,QAAA,CAAAkG,YAAY,CAACxG,KAAA,CAAAwO,cAAc,CAACjI,WAAW,yB,KAIhDvF,mBAAA,CAOM,OAPNmP,YAOM,G,8BANJnP,mBAAA,CAAkD;IAA3CzB,KAAK,EAAC;EAAoB,GAAC,UAAQ,sBAC1CyB,mBAAA,CAII,KAJJoP,YAII,GAHFpP,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASjB,KAAA,CAAAwO,cAAc,CAAC6B,QAAQ,4BAA4BrQ,KAAA,CAAAwO,cAAc,CAAC6B,QAAQ;sBACjGrQ,KAAA,CAAAwO,cAAc,CAAC6B,QAAQ,oC,KAIhCrP,mBAAA,CAGM,OAHNsP,YAGM,G,8BAFJtP,mBAAA,CAAyD;IAAlDzB,KAAK,EAAC;EAAoB,GAAC,iBAAe,sBACjDyB,mBAAA,CAAoE,KAApEuP,YAAoE,EAAAlO,gBAAA,CAAjDrC,KAAA,CAAAwO,cAAc,CAACgC,eAAe,6B,GAEnDxP,mBAAA,CAGM,OAHNyP,YAGM,G,8BAFJzP,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAAqE,KAArE0P,YAAqE,EAAArO,gBAAA,CAAlD/B,QAAA,CAAAqQ,cAAc,CAAC3Q,KAAA,CAAAwO,cAAc,CAACxC,YAAY,kB,WAOvEjL,mBAAA,wBAA2B,EAC3BC,mBAAA,CAgCM,OAhCN4P,YAgCM,G,8BA/BJ5P,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAwE;IAApEzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,I,iBAAK,oBAAkB,E,wBAErEyB,mBAAA,CA2BM,OA3BN6P,YA2BM,GA1BJ7P,mBAAA,CAyBM,OAzBN8P,YAyBM,GAxBJ9P,mBAAA,CAWM,OAXN+P,YAWM,GAVJ/P,mBAAA,CAGM,OAHNgQ,YAGM,G,8BAFJhQ,mBAAA,CAAmD;IAA5CzB,KAAK,EAAC;EAAoB,GAAC,WAAS,sBAC3CyB,mBAAA,CAAoD,KAApDiQ,YAAoD,EAAA5O,gBAAA,CAAjCrC,KAAA,CAAAwO,cAAc,CAAC3D,WAAW,iB,GAE/C7J,mBAAA,CAKM,OALNkQ,YAKM,G,8BAJJlQ,mBAAA,CAAuD;IAAhDzB,KAAK,EAAC;EAAoB,GAAC,eAAa,sBAC/CyB,mBAAA,CAEI,KAFJmQ,YAEI,GADFnQ,mBAAA,CAAwF;IAApFkN,IAAI,YAAYlO,KAAA,CAAAwO,cAAc,CAACzD,YAAY;sBAAO/K,KAAA,CAAAwO,cAAc,CAACzD,YAAY,wBAAAqG,YAAA,E,OAIvFpQ,mBAAA,CAWM,OAXNqQ,YAWM,GAVJrQ,mBAAA,CAKM,OALNsQ,YAKM,G,8BAJJtQ,mBAAA,CAAsD;IAA/CzB,KAAK,EAAC;EAAoB,GAAC,cAAY,sBAC9CyB,mBAAA,CAEI,KAFJuQ,YAEI,GADFvQ,mBAAA,CAAuG;IAAnGkN,IAAI,SAASlO,KAAA,CAAAwO,cAAc,CAACgD,YAAY;sBAAOxR,KAAA,CAAAwO,cAAc,CAACgD,YAAY,0CAAAC,YAAA,E,KAGlFzQ,mBAAA,CAGM,OAHN0Q,YAGM,G,8BAFJ1Q,mBAAA,CAAiD;IAA1CzB,KAAK,EAAC;EAAoB,GAAC,SAAO,sBACzCyB,mBAAA,CAAyE,KAAzE2Q,YAAyE,EAAAtP,gBAAA,CAAtDrC,KAAA,CAAAwO,cAAc,CAACoD,cAAc,mC,WAO1D7Q,mBAAA,+BAAkC,EACvBf,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,I,cAA1ClS,mBAAA,CAyEM,OAzENmS,YAyEM,G,8BAxEJ9Q,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAA6F;IAAzFzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAA0C;IAAvCzB,KAAK,EAAC;EAA4B,I,iBAAK,+BAA6B,E,wBAE1FyB,mBAAA,CAoEM,OApEN+Q,YAoEM,GAnEJhR,mBAAA,gCAAmC,EACxBf,KAAA,CAAAwO,cAAc,CAAC7H,aAAa,6B,cAAvChH,mBAAA,CAuBM,OAAAqS,YAAA,GAtBJhR,mBAAA,CAqBM,OArBNiR,YAqBM,GApBJjR,mBAAA,CASM,OATNkR,YASM,GARJlR,mBAAA,CAGM,OAHNmR,YAGM,G,8BAFJnR,mBAAA,CAA0D;IAAnDzB,KAAK,EAAC;EAAoB,GAAC,kBAAgB,sBAClDyB,mBAAA,CAA6F,KAA7FoR,YAA6F,EAAA/P,gBAAA,CAA1ErC,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACQ,gBAAgB,oC,GAErErR,mBAAA,CAGM,OAHNsR,YAGM,G,8BAFJtR,mBAAA,CAAsD;IAA/CzB,KAAK,EAAC;EAAoB,GAAC,cAAY,sBAC9CyB,mBAAA,CAAyF,KAAzFuR,YAAyF,EAAAlQ,gBAAA,CAAtErC,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACW,YAAY,oC,KAGnExR,mBAAA,CASM,OATNyR,YASM,GARJzR,mBAAA,CAGM,OAHN0R,YAGM,G,8BAFJ1R,mBAAA,CAAoD;IAA7CzB,KAAK,EAAC;EAAoB,GAAC,YAAU,sBAC5CyB,mBAAA,CAAuF,KAAvF2R,YAAuF,EAAAtQ,gBAAA,CAApErC,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACe,UAAU,oC,GAE/D5R,mBAAA,CAGM,OAHN6R,YAGM,G,8BAFJ7R,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAA2G,KAA3G8R,YAA2G,EAAAzQ,gBAAA,CAAxF/B,QAAA,CAAAqL,cAAc,CAAC3L,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACkB,cAAc,qC,WAOxE/S,KAAA,CAAAwO,cAAc,CAAC7H,aAAa,iB,cAA5ChH,mBAAA,CAuCMqC,SAAA;IAAAC,GAAA;EAAA,IAxCNlB,mBAAA,oBAAuB,EACvBC,mBAAA,CAuCM,cAtCJA,mBAAA,CAqCM,OArCNgS,YAqCM,GApCJhS,mBAAA,CAiBM,OAjBNiS,YAiBM,GAhBJjS,mBAAA,CAGM,OAHNkS,YAGM,G,8BAFJlS,mBAAA,CAAoD;IAA7CzB,KAAK,EAAC;EAAoB,GAAC,YAAU,sBAC5CyB,mBAAA,CAAmG,KAAnGmS,YAAmG,EAAA9Q,gBAAA,CAAhF/B,QAAA,CAAAyL,UAAU,CAAC/L,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACuB,UAAU,qC,GAE1EpS,mBAAA,CAGM,OAHNqS,YAGM,G,8BAFJrS,mBAAA,CAAqD;IAA9CzB,KAAK,EAAC;EAAoB,GAAC,aAAW,sBAC7CyB,mBAAA,CAAwF,KAAxFsS,YAAwF,EAAAjR,gBAAA,CAArErC,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAAC0B,WAAW,oC,GAEhEvS,mBAAA,CAGM,OAHNwS,YAGM,G,8BAFJxS,mBAAA,CAAsD;IAA/CzB,KAAK,EAAC;EAAoB,GAAC,cAAY,sBAC9CyB,mBAAA,CAAyF,KAAzFyS,YAAyF,EAAApR,gBAAA,CAAtErC,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACW,YAAY,oC,GAEjExR,mBAAA,CAGM,OAHN0S,YAGM,G,8BAFJ1S,mBAAA,CAAqD;IAA9CzB,KAAK,EAAC;EAAoB,GAAC,aAAW,sBAC7CyB,mBAAA,CAAwF,KAAxF2S,YAAwF,EAAAtR,gBAAA,CAArErC,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAAC+B,WAAW,oC,KAGlE5S,mBAAA,CAiBM,OAjBN6S,YAiBM,GAhBJ7S,mBAAA,CAGM,OAHN8S,YAGM,G,8BAFJ9S,mBAAA,CAAoD;IAA7CzB,KAAK,EAAC;EAAoB,GAAC,YAAU,sBAC5CyB,mBAAA,CAAuF,KAAvF+S,YAAuF,EAAA1R,gBAAA,CAApErC,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACe,UAAU,oC,GAE/D5R,mBAAA,CAGM,OAHNgT,YAGM,G,8BAFJhT,mBAAA,CAAuD;IAAhDzB,KAAK,EAAC;EAAoB,GAAC,eAAa,sBAC/CyB,mBAAA,CAA0G,KAA1GiT,YAA0G,EAAA5R,gBAAA,CAAvF/B,QAAA,CAAAqL,cAAc,CAAC3L,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACqC,aAAa,qC,GAEjFlT,mBAAA,CAGM,OAHNmT,YAGM,G,8BAFJnT,mBAAA,CAAoD;IAA7CzB,KAAK,EAAC;EAAoB,GAAC,YAAU,sBAC5CyB,mBAAA,CAAwG,KAAxGoT,YAAwG,EAAA/R,gBAAA,CAArF/B,QAAA,CAAAqL,cAAc,CAAC3L,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAACwC,UAAU,sC,GAE9ErT,mBAAA,CAGM,OAHNsT,YAGM,G,8BAFJtT,mBAAA,CAA0D;IAAnDzB,KAAK,EAAC;EAAoB,GAAC,kBAAgB,sBAClDyB,mBAAA,CAAoG,KAApGuT,YAAoG,EAAAlS,gBAAA,CAAjF/B,QAAA,CAAAqL,cAAc,CAAC3L,KAAA,CAAAwO,cAAc,CAACqD,gBAAgB,CAAC2C,gBAAgB,4B,0IAShGzT,mBAAA,sCAAyC,EACzCC,mBAAA,CAgIM,OAhINyT,YAgIM,GA/HJ1T,mBAAA,uBAA0B,EAC1BC,mBAAA,CA8EM,OA9EN0T,YA8EM,G,8BA7EJ1T,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAwE;IAApEzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,I,iBAAK,mBAAiB,E,wBAErEyB,mBAAA,CAyEM,OAzEN2T,YAyEM,GAxEJ3T,mBAAA,CAQM,OARN4T,YAQM,G,8BAPJ5T,mBAAA,CAAuD;IAAhDzB,KAAK,EAAC;EAAoB,GAAC,eAAa,sB,gBAC/CyB,mBAAA,CAKS;IALDzB,KAAK,EAAC,aAAa;iEAAUS,KAAA,CAAA6U,gBAAgB,CAACC,SAAS,GAAAvS,MAAA;oCAC7DvB,mBAAA,CAA2C;IAAnCmF,KAAK,EAAC;EAAE,GAAC,mBAAiB,uB,kBAClCxG,mBAAA,CAESqC,SAAA,QAAAoE,WAAA,CAFgBpG,KAAA,CAAAqG,aAAa,EAAvBH,MAAM;yBAArBvG,mBAAA,CAES;MAFgCsC,GAAG,EAAEiE,MAAM,CAACI,EAAE;MAAGH,KAAK,EAAED,MAAM,CAACI;wBACnEhG,QAAA,CAAAkG,YAAY,CAACN,MAAM,CAACK,WAAW,yBAAAwO,YAAA;2EAHD/U,KAAA,CAAA6U,gBAAgB,CAACC,SAAS,E,KAOjE9T,mBAAA,CAQM,OARNgU,YAQM,G,8BAPJhU,mBAAA,CAAsD;IAA/CzB,KAAK,EAAC;EAAoB,GAAC,cAAY,sB,gBAC9CyB,mBAAA,CAKY;IAJVzB,KAAK,EAAC,cAAc;IACpB0V,IAAI,EAAC,GAAG;iEACCjV,KAAA,CAAA6U,gBAAgB,CAACK,MAAM,GAAA3S,MAAA;IAChCsD,WAAW,EAAC;iDADH7F,KAAA,CAAA6U,gBAAgB,CAACK,MAAM,E,KAIpClU,mBAAA,CA6BM,OA7BNmU,YA6BM,GA5BJnU,mBAAA,CAOS;IANPzB,KAAK,EAAC,iBAAiB;IACtB6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA8U,4BAAA,IAAA9U,QAAA,CAAA8U,4BAAA,IAAA9T,IAAA,CAA4B;IACnCkC,QAAQ,GAAGxD,KAAA,CAAA6U,gBAAgB,CAACC;oCAE7B9T,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,4B,iBAAK,iBAElC,E,iCACAyB,mBAAA,CASS;IARPzB,KAAK,EAAA0B,eAAA,EAAC,iBAAiB;MAAA,wBAIWX,QAAA,CAAA+J,UAAU,CAACrK,KAAA,CAAAwO,cAAc;IAAA;IAH1DpN,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA+U,uBAAA,IAAA/U,QAAA,CAAA+U,uBAAA,IAAA/T,IAAA,CAAuB;IAC9BkC,QAAQ,GAAGlD,QAAA,CAAA+J,UAAU,CAACrK,KAAA,CAAAwO,cAAc;IACpCnL,KAAK,EAAE/C,QAAA,CAAA+J,UAAU,CAACrK,KAAA,CAAAwO,cAAc,6BAA6BlO,QAAA,CAAAgV,oBAAoB,CAACtV,KAAA,CAAAwO,cAAc;oCAGjGxN,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,iBAEnC,E,yCACAyB,mBAAA,CASS;IARPzB,KAAK,EAAA0B,eAAA,EAAC,gBAAgB;MAAA,uBAIWX,QAAA,CAAAiK,SAAS,CAACvK,KAAA,CAAAwO,cAAc;IAAA;IAHxDpN,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEvC,KAAA,CAAAuV,cAAc,IAAIvV,KAAA,CAAAuV,cAAc;IACvC/R,QAAQ,GAAGlD,QAAA,CAAAiK,SAAS,CAACvK,KAAA,CAAAwO,cAAc;IACnCnL,KAAK,EAAE/C,QAAA,CAAAiK,SAAS,CAACvK,KAAA,CAAAwO,cAAc,4BAA4BlO,QAAA,CAAAgV,oBAAoB,CAACtV,KAAA,CAAAwO,cAAc;oCAG/FxN,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,6B,iBAAK,GACjC,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAuV,cAAc,+C,0CAIrBxU,mBAAA,oBAAuB,EACZf,KAAA,CAAAuV,cAAc,I,cAAzB5V,mBAAA,CAqBM,OArBN6V,YAqBM,GApBJxU,mBAAA,CASM,OATNyU,YASM,G,8BARJzU,mBAAA,CAAwE;IAAjEzB,KAAK,EAAC;EAAgC,GAAC,oBAAkB,sB,gBAChEyB,mBAAA,CAMY;IALVzB,KAAK,EAAC,cAAc;IACpB0V,IAAI,EAAC,GAAG;iEACCjV,KAAA,CAAA0V,UAAU,CAACR,MAAM,GAAA3S,MAAA;IAC1BsD,WAAW,EAAC,gDAAgD;IAC5D8P,QAAQ,EAAR;iDAFS3V,KAAA,CAAA0V,UAAU,CAACR,MAAM,E,KAK9BlU,mBAAA,CASM,OATN4U,YASM,GARJ5U,mBAAA,CAOS;IANPzB,KAAK,EAAC,gBAAgB;IACrB6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAuV,sBAAA,IAAAvV,QAAA,CAAAuV,sBAAA,IAAAvU,IAAA,CAAsB;IAC7BkC,QAAQ,GAAGxD,KAAA,CAAA0V,UAAU,CAACR,MAAM,IAAIlV,KAAA,CAAA0V,UAAU,CAACR,MAAM,CAACY,IAAI;oCAEvD9U,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,qBAEnC,E,8EAMRwB,mBAAA,yBAA4B,EAC5BC,mBAAA,CA4CM,OA5CN+U,YA4CM,G,8BA3CJ/U,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAgF;IAA5EzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,I,iBAAK,qBAAmB,E,wBAE7EyB,mBAAA,CAuCM,OAvCNgV,YAuCM,GAtCJhV,mBAAA,CAGM,OAHNiV,YAGM,G,8BAFJjV,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAA0E,KAA1EkV,YAA0E,EAAA7T,gBAAA,CAAvDrC,KAAA,CAAAwO,cAAc,CAAC2H,cAAc,oC,GAElDnV,mBAAA,CAOM,OAPNoV,YAOM,G,8BANJpV,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAII,KAJJqV,YAII,GAHFrV,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASjB,KAAA,CAAAwO,cAAc,CAAC8H,cAAc,6BAA6BtW,KAAA,CAAAwO,cAAc,CAAC8H,cAAc;sBAC9GtW,KAAA,CAAAwO,cAAc,CAAC8H,cAAc,oC,KAItCtV,mBAAA,CAyBM,OAzBNuV,YAyBM,GAxBJvV,mBAAA,CAKM,OALNwV,YAKM,GAJJxV,mBAAA,CAGM,OAHNyV,YAGM,G,8BAFJzV,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAA0B,GAAC,UAAQ,sBAChDyB,mBAAA,CAAiE,KAAjE0V,YAAiE,EAAArU,gBAAA,CAA9C/B,QAAA,CAAAqL,cAAc,CAAC3L,KAAA,CAAAwO,cAAc,CAACmI,QAAQ,kB,KAG7D3V,mBAAA,CAKM,OALN4V,YAKM,GAJJ5V,mBAAA,CAGM,OAHN6V,YAGM,G,8BAFJ7V,mBAAA,CAA+D;IAAxDzB,KAAK,EAAC;EAA0B,GAAC,iBAAe,sBACvDyB,mBAAA,CAAwE,KAAxE8V,YAAwE,EAAAzU,gBAAA,CAArD/B,QAAA,CAAAqL,cAAc,CAAC3L,KAAA,CAAAwO,cAAc,CAACuI,eAAe,kB,KAGpE/V,mBAAA,CAKM,OALNgW,YAKM,GAJJhW,mBAAA,CAGM,OAHNiW,YAGM,G,8BAFJjW,mBAAA,CAA8D;IAAvDzB,KAAK,EAAC;EAA0B,GAAC,gBAAc,sBACtDyB,mBAAA,CAAuE,KAAvEkW,YAAuE,EAAA7U,gBAAA,CAApD/B,QAAA,CAAAqL,cAAc,CAAC3L,KAAA,CAAAwO,cAAc,CAAC2I,cAAc,kB,KAGnEnW,mBAAA,CAKM,OALNoW,YAKM,GAJJpW,mBAAA,CAGM,OAHNqW,YAGM,G,8BAFJrW,mBAAA,CAA4D;IAArDzB,KAAK,EAAC;EAA0B,GAAC,cAAY,sBACpDyB,mBAAA,CAAuF,KAAvFsW,YAAuF,EAAAjV,gBAAA,CAA/C/B,QAAA,CAAAqL,cAAc,CAAC3L,KAAA,CAAAwO,cAAc,CAAC5C,SAAS,kB,eAS7F7K,mBAAA,6BAAgC,EAChCC,mBAAA,CAyCM,OAzCNuW,YAyCM,G,8BAxCJvW,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAuE;IAAnEzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAmC;IAAhCzB,KAAK,EAAC;EAAqB,I,iBAAK,gBAAc,E,wBAEpEyB,mBAAA,CAoCM,OApCNwW,YAoCM,GAnCOxX,KAAA,CAAAwO,cAAc,CAACiJ,cAAc,IAAIzX,KAAA,CAAAwO,cAAc,CAACiJ,cAAc,CAACrQ,MAAM,Q,cAAhFzH,mBAAA,CA8BM,OA9BN+X,YA8BM,I,kBA7BJ/X,mBAAA,CA4BMqC,SAAA,QAAAoE,WAAA,CA3BuBpG,KAAA,CAAAwO,cAAc,CAACiJ,cAAc,GAAhDE,OAAO,EAAEC,KAAK;yBADxBjY,mBAAA,CA4BM;MA1BHsC,GAAG,EAAE0V,OAAO,CAACrR,EAAE;MAChB/G,KAAK,EAAA0B,eAAA,EAAC,eAAe;QAAA,sBACW2W,KAAK,KAAK5X,KAAA,CAAAwO,cAAc,CAACiJ,cAAc,CAACrQ,MAAM;MAAA;QAE9EpG,mBAAA,CAEM;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,iBAAiB,QAAeX,QAAA,CAAAiL,cAAc,CAACoM,OAAO,CAACE,eAAe;2CAC/E7W,mBAAA,CAA6B;MAA1BzB,KAAK,EAAC;IAAe,2B,qBAE1ByB,mBAAA,CAkBM,OAlBN8W,YAkBM,GAjBJ9W,mBAAA,CAKM,OALN+W,YAKM,GAJJ/W,mBAAA,CAEO;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,QAAeX,QAAA,CAAAiL,cAAc,CAACoM,OAAO,CAACE,eAAe;wBACnEvX,QAAA,CAAAkG,YAAY,CAACmR,OAAO,CAACE,eAAe,0BAEzC7W,mBAAA,CAA+E,SAA/EgX,YAA+E,EAAA3V,gBAAA,CAA7C/B,QAAA,CAAAqQ,cAAc,CAACgH,OAAO,CAACM,UAAU,kB,GAErEjX,mBAAA,CAUM,OAVNkX,YAUM,GATJlX,mBAAA,CAEI,KAFJmX,YAEI,G,8BADFnX,mBAAA,CAA4B,gBAApB,aAAW,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAGsV,OAAO,CAACS,eAAe,iB,GAEhDT,OAAO,CAACU,eAAe,I,cAAhC1Y,mBAAA,CAEI,KAFJ2Y,YAEI,G,8BADFtX,mBAAA,CAAsB,gBAAd,OAAK,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAG/B,QAAA,CAAAkG,YAAY,CAACmR,OAAO,CAACU,eAAe,kB,wCAEvDV,OAAO,CAACY,aAAa,I,cAA9B5Y,mBAAA,CAEI,KAFJ6Y,YAEI,G,8BADFxX,mBAAA,CAAwB,gBAAhB,SAAO,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAGsV,OAAO,CAACY,aAAa,iB;qDAM3D5Y,mBAAA,CAGM,OAHN8Y,YAGM,EAAApX,MAAA,UAAAA,MAAA,SAFJL,mBAAA,CAAyC;IAAtCzB,KAAK,EAAC;EAA2B,4BACpCyB,mBAAA,CAAkC,WAA/B,6BAA2B,oB,WAKtCA,mBAAA,CASM,OATN0X,YASM,GARJ1X,mBAAA,CAGS;IAHDsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,mBAAmB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEvC,KAAA,CAAAuO,kBAAkB;oCACxEvN,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,SAEnC,E,IACAyB,mBAAA,CAGS;IAHDsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAqY,qBAAA,IAAArY,QAAA,CAAAqY,qBAAA,IAAArX,IAAA,CAAqB;oCACzEN,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,4B,iBAAK,WAEtC,E,iDAMRwB,mBAAA,wBAA2B,EAChBf,KAAA,CAAA4Y,eAAe,IAAI5Y,KAAA,CAAA6Y,wBAAwB,I,cAAtDlZ,mBAAA,CA6DM,OA7DNmZ,YA6DM,GA5DJ9X,mBAAA,CA2DM,OA3DN+X,YA2DM,GA1DJ/X,mBAAA,CAyDM,OAzDNgY,YAyDM,GAxDJhY,mBAAA,CAMM,OANNiY,YAMM,G,8BALJjY,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAa,IACrByB,mBAAA,CAAoD;IAAjDzB,KAAK,EAAC;EAAsC,I,iBAAK,kBAEtD,E,sBACAyB,mBAAA,CAAgF;IAAxEsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA4Y,qBAAA,IAAA5Y,QAAA,CAAA4Y,qBAAA,IAAA5X,IAAA,CAAqB;QAEvEN,mBAAA,CAkCM,OAlCNmY,YAkCM,G,8BAjCJnY,mBAAA,CAGM;IAHDzB,KAAK,EAAC;EAAqB,IAC9ByB,mBAAA,CAAgD;IAA7CzB,KAAK,EAAC;EAAkC,I,iBAAK,kGAElD,E,sBAEAyB,mBAAA,CAOM,OAPNoY,YAOM,G,8BANJpY,mBAAA,CAAiC,gBAAzB,kBAAgB,sBACxBA,mBAAA,CAIK,MAJLqY,YAIK,GAHHrY,mBAAA,CAAuF,a,8BAAnFA,mBAAA,CAAgC,gBAAxB,iBAAe,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAGrC,KAAA,CAAA6Y,wBAAwB,CAAC3O,cAAc,iB,GAC/ElJ,mBAAA,CAAqF,a,8BAAjFA,mBAAA,CAA+B,gBAAvB,gBAAc,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAGrC,KAAA,CAAA6Y,wBAAwB,CAAClS,aAAa,iB,GAC7E3F,mBAAA,CAA4E,a,8BAAxEA,mBAAA,CAAwB,gBAAhB,SAAO,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAGrC,KAAA,CAAA6Y,wBAAwB,CAAChO,WAAW,iB,OAIxE7J,mBAAA,CAkBM,OAlBNsY,YAkBM,G,8BAjBJtY,mBAAA,CAEQ;IAFDwH,GAAG,EAAC,iBAAiB;IAACjJ,KAAK,EAAC;MACjCyB,mBAAA,CAAoE,iB,iBAA5D,mBAAiB,GAAAA,mBAAA,CAAkC;IAA5BzB,KAAK,EAAC;EAAa,GAAC,GAAC,E,wCAEtDyB,mBAAA,CAOY;IANVsF,EAAE,EAAC,iBAAiB;iEACXtG,KAAA,CAAAuZ,eAAe,CAACrE,MAAM,GAAA3S,MAAA;IAC/BhD,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAuZ,eAAe,CAACC;IAAK;IAF7CvE,IAAI,EAAC,GAAG;IACRpP,WAAW,EAAC;0CAHH7F,KAAA,CAAAuZ,eAAe,CAACrE,MAAM,E,GAMtBlV,KAAA,CAAAuZ,eAAe,CAACC,KAAK,I,cAAhC7Z,mBAAA,CAEM,OAFN8Z,YAEM,EAAApX,gBAAA,CADDrC,KAAA,CAAAuZ,eAAe,CAACC,KAAK,oB,iEAE1BxY,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAW,GAAC,iFAEvB,qB,KAGJyB,mBAAA,CAaM,OAbN0Y,YAaM,GAZJ1Y,mBAAA,CAGS;IAHDsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,mBAAmB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA4Y,qBAAA,IAAA5Y,QAAA,CAAA4Y,qBAAA,IAAA5X,IAAA,CAAqB;IAAGkC,QAAQ,EAAExD,KAAA,CAAAuZ,eAAe,CAACzX;oCACxGd,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,UAEnC,E,iCACAyB,mBAAA,CAOS;IAPDsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,gBAAgB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAqZ,kBAAA,IAAArZ,QAAA,CAAAqZ,kBAAA,IAAArY,IAAA,CAAkB;IAAGkC,QAAQ,EAAExD,KAAA,CAAAuZ,eAAe,CAACzX,OAAO,KAAK9B,KAAA,CAAAuZ,eAAe,CAACrE,MAAM,CAACY,IAAI;oCACzI9U,mBAAA,CAAwC;IAArCzB,KAAK,EAAC;EAA0B,6BACvBS,KAAA,CAAAuZ,eAAe,CAACzX,OAAO,I,cAAnCnC,mBAAA,CAGO,QAAAia,YAAA,EAAAvY,MAAA,UAAAA,MAAA,SAFLL,mBAAA,CAA2C;IAAxCzB,KAAK,EAAC;EAA6B,4B,iBAAK,gBAE7C,E,qBACAI,mBAAA,CAAkC,QAAAka,YAAA,EAArB,gBAAc,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}