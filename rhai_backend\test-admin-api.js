const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
let adminToken = null;

async function testAdminLogin() {
  try {
    console.log('🔐 Testing admin login...');
    
    const response = await axios.post(`${BASE_URL}/admin/auth/login`, {
      username: 'admin12345',
      password: '12345QWERTqwert'
    });
    
    if (response.data.success && response.data.data.token) {
      adminToken = response.data.data.token;
      console.log('✅ Admin login successful');
      console.log('🎫 Token received:', adminToken.substring(0, 50) + '...');
      console.log('👤 Admin data:', response.data.data.admin);
      return true;
    } else {
      console.log('❌ Admin login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Admin login error:', error.response?.data || error.message);
    return false;
  }
}

async function testUserStats() {
  try {
    console.log('\n📊 Testing user stats endpoint...');
    
    const response = await axios.get(`${BASE_URL}/users/stats`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ User stats successful:', response.data);
    return true;
  } catch (error) {
    console.log('❌ User stats error:', error.response?.status, error.response?.data || error.message);
    return false;
  }
}

async function testGetUsers() {
  try {
    console.log('\n👥 Testing get users endpoint...');
    
    const response = await axios.get(`${BASE_URL}/users?page=1&limit=50`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Get users successful:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Get users error:', error.response?.status, error.response?.data || error.message);
    return false;
  }
}

async function testTokenDecoding() {
  try {
    console.log('\n🔍 Testing token decoding...');
    
    if (!adminToken) {
      console.log('❌ No token available');
      return false;
    }
    
    // Decode token payload (without verification)
    const tokenParts = adminToken.split('.');
    if (tokenParts.length !== 3) {
      console.log('❌ Invalid token format');
      return false;
    }
    
    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
    console.log('🎫 Token payload:', payload);
    
    const currentTime = Math.floor(Date.now() / 1000);
    console.log('⏰ Current time:', currentTime);
    console.log('⏰ Token expires:', payload.exp);
    console.log('⏰ Time until expiry:', payload.exp - currentTime, 'seconds');
    
    if (payload.exp < currentTime) {
      console.log('❌ Token is expired');
      return false;
    }
    
    console.log('✅ Token is valid');
    return true;
  } catch (error) {
    console.log('❌ Token decoding error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting admin API tests...\n');
  
  // Test admin login
  const loginSuccess = await testAdminLogin();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without valid login');
    return;
  }
  
  // Test token decoding
  await testTokenDecoding();
  
  // Test protected endpoints
  await testUserStats();
  await testGetUsers();
  
  console.log('\n✅ All tests completed');
}

runTests().catch(console.error);
