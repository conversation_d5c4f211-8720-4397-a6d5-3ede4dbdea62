{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nexport default {\n  name: 'HelpSupport',\n  data() {\n    return {\n      documentFAQs: [{\n        id: 'doc-1',\n        question: 'How do I request a Barangay Clearance?',\n        answer: 'To request a Barangay Clearance:<br>1. Go to \"Available Services\" in the sidebar<br>2. Select \"Barangay Clearance\"<br>3. Fill out the required information<br>4. Submit your request<br>5. Pay the ₱50 fee<br>6. Your clearance will be processed the same day',\n        expanded: false\n      }, {\n        id: 'doc-2',\n        question: 'What documents do I need to provide?',\n        answer: 'For most document requests, you need:<br>• Valid government-issued ID<br>• Proof of residency in Barangay Bula<br>• Completed application form<br>• Payment of applicable fees<br><br>Specific requirements may vary by document type.',\n        expanded: false\n      }, {\n        id: 'doc-3',\n        question: 'How long does document processing take?',\n        answer: 'Processing times vary by document type:<br>• Barangay Clearance: Same day<br>• Cedula: Same day<br>• Certificate of Residency: Same day<br>• Certificate of Indigency: 1-2 business days<br>• Business Permit: 2-3 business days',\n        expanded: false\n      }, {\n        id: 'doc-4',\n        question: 'Can I track my document request status?',\n        answer: 'Yes! You can track your request status by:<br>1. Going to \"My Requests\" in the sidebar<br>2. Viewing the status of each request<br>3. Receiving notifications when status changes<br><br>Status options include: Pending, Processing, Ready for Pickup, Completed',\n        expanded: false\n      }, {\n        id: 'doc-5',\n        question: 'What if my request is rejected?',\n        answer: 'If your request is rejected:<br>1. You will receive a notification with the reason<br>2. Review the rejection reason carefully<br>3. Correct any issues or provide missing documents<br>4. Submit a new request<br><br>Common rejection reasons include incomplete information or missing requirements.',\n        expanded: false\n      }],\n      accountFAQs: [{\n        id: 'acc-1',\n        question: 'How do I create an account?',\n        answer: 'To create an account:<br>1. Click \"Register\" on the login page<br>2. Fill out your personal information<br>3. Provide a valid email address<br>4. Create a secure password<br>5. Verify your email address<br>6. Wait for account approval from barangay staff',\n        expanded: false\n      }, {\n        id: 'acc-2',\n        question: 'I forgot my password. How do I reset it?',\n        answer: 'To reset your password:<br>1. Click \"Forgot Password\" on the login page<br>2. Enter your email address<br>3. Check your email for reset instructions<br>4. Click the reset link in the email<br>5. Create a new password<br><br>If you don\\'t receive the email, check your spam folder.',\n        expanded: false\n      }, {\n        id: 'acc-3',\n        question: 'How do I update my profile information?',\n        answer: 'To update your profile:<br>1. Go to \"My Profile\" in the sidebar<br>2. Click \"Edit Profile\"<br>3. Update your information<br>4. Click \"Save Changes\"<br><br>Note: Some changes may require verification by barangay staff.',\n        expanded: false\n      }, {\n        id: 'acc-4',\n        question: 'Why is my account pending approval?',\n        answer: 'New accounts require approval to ensure:<br>• You are a legitimate resident of Barangay Bula<br>• Your information is accurate<br>• System security is maintained<br><br>Approval typically takes 1-2 business days. You will receive an email notification once approved.',\n        expanded: false\n      }],\n      paymentFAQs: [{\n        id: 'pay-1',\n        question: 'What payment methods are accepted?',\n        answer: 'We accept the following payment methods:<br>• Cash payment at the barangay office<br>• GCash<br>• PayMaya<br>• Bank transfer<br>• Credit/Debit cards (via PayMongo)<br><br>Online payments are processed securely through our payment partners.',\n        expanded: false\n      }, {\n        id: 'pay-2',\n        question: 'When do I need to pay for my document request?',\n        answer: 'Payment is required:<br>• Before document processing begins<br>• After submitting your request<br>• Within 24 hours of request submission<br><br>Unpaid requests will be automatically cancelled after 24 hours.',\n        expanded: false\n      }, {\n        id: 'pay-3',\n        question: 'Can I get a refund if I cancel my request?',\n        answer: 'Refund policy:<br>• Full refund if cancelled within 1 hour of payment<br>• No refund if processing has already started<br>• Partial refund may be available in special circumstances<br><br>Contact our office for refund requests.',\n        expanded: false\n      }, {\n        id: 'pay-4',\n        question: 'Is my payment information secure?',\n        answer: 'Yes, your payment information is secure:<br>• We use SSL encryption for all transactions<br>• Payment processing is handled by certified partners<br>• We do not store your credit card information<br>• All transactions are monitored for fraud<br><br>Your financial security is our priority.',\n        expanded: false\n      }],\n      userGuides: [{\n        id: 'guide-1',\n        title: 'Getting Started',\n        description: 'Learn how to create an account and navigate the system',\n        icon: 'fas fa-play-circle'\n      }, {\n        id: 'guide-2',\n        title: 'Requesting Documents',\n        description: 'Step-by-step guide to requesting barangay documents',\n        icon: 'fas fa-file-alt'\n      }, {\n        id: 'guide-3',\n        title: 'Making Payments',\n        description: 'How to pay for your document requests online',\n        icon: 'fas fa-credit-card'\n      }, {\n        id: 'guide-4',\n        title: 'Tracking Requests',\n        description: 'Monitor the status of your document requests',\n        icon: 'fas fa-search'\n      }, {\n        id: 'guide-5',\n        title: 'Managing Your Profile',\n        description: 'Update your personal information and settings',\n        icon: 'fas fa-user-cog'\n      }, {\n        id: 'guide-6',\n        title: 'Troubleshooting',\n        description: 'Common issues and how to resolve them',\n        icon: 'fas fa-tools'\n      }]\n    };\n  },\n  methods: {\n    toggleFAQ(faqId) {\n      // Find the FAQ in all categories\n      const allFAQs = [...this.documentFAQs, ...this.accountFAQs, ...this.paymentFAQs];\n      const faq = allFAQs.find(f => f.id === faqId);\n      if (faq) {\n        faq.expanded = !faq.expanded;\n      }\n    },\n    scrollToSection(sectionId) {\n      const element = document.getElementById(sectionId);\n      if (element) {\n        element.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n      }\n    },\n    openGuide(guideId) {\n      // This would typically open a detailed guide or navigate to a guide page\n      console.log('Opening guide:', guideId);\n\n      // For now, show an alert with guide information\n      const guide = this.userGuides.find(g => g.id === guideId);\n      if (guide) {\n        alert(`Opening guide: ${guide.title}\\n\\n${guide.description}\\n\\nDetailed guides will be available in a future update.`);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "documentFAQs", "id", "question", "answer", "expanded", "accountFAQs", "paymentFAQs", "userGuides", "title", "description", "icon", "methods", "toggleFAQ", "faqId", "allFAQs", "faq", "find", "f", "scrollToSection", "sectionId", "element", "document", "getElementById", "scrollIntoView", "behavior", "block", "openGuide", "guideId", "console", "log", "guide", "g", "alert"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/components/client/js/helpSupport.js"], "sourcesContent": ["export default {\n  name: 'HelpSup<PERSON>',\n  data() {\n    return {\n      documentFAQs: [\n        {\n          id: 'doc-1',\n          question: 'How do I request a Barangay Clearance?',\n          answer: 'To request a Barangay Clearance:<br>1. Go to \"Available Services\" in the sidebar<br>2. Select \"Barangay Clearance\"<br>3. Fill out the required information<br>4. Submit your request<br>5. Pay the ₱50 fee<br>6. Your clearance will be processed the same day',\n          expanded: false\n        },\n        {\n          id: 'doc-2',\n          question: 'What documents do I need to provide?',\n          answer: 'For most document requests, you need:<br>• Valid government-issued ID<br>• Proof of residency in Barangay Bula<br>• Completed application form<br>• Payment of applicable fees<br><br>Specific requirements may vary by document type.',\n          expanded: false\n        },\n        {\n          id: 'doc-3',\n          question: 'How long does document processing take?',\n          answer: 'Processing times vary by document type:<br>• Barangay Clearance: Same day<br>• Cedula: Same day<br>• Certificate of Residency: Same day<br>• Certificate of Indigency: 1-2 business days<br>• Business Permit: 2-3 business days',\n          expanded: false\n        },\n        {\n          id: 'doc-4',\n          question: 'Can I track my document request status?',\n          answer: 'Yes! You can track your request status by:<br>1. Going to \"My Requests\" in the sidebar<br>2. Viewing the status of each request<br>3. Receiving notifications when status changes<br><br>Status options include: Pending, Processing, Ready for Pickup, Completed',\n          expanded: false\n        },\n        {\n          id: 'doc-5',\n          question: 'What if my request is rejected?',\n          answer: 'If your request is rejected:<br>1. You will receive a notification with the reason<br>2. Review the rejection reason carefully<br>3. Correct any issues or provide missing documents<br>4. Submit a new request<br><br>Common rejection reasons include incomplete information or missing requirements.',\n          expanded: false\n        }\n      ],\n      accountFAQs: [\n        {\n          id: 'acc-1',\n          question: 'How do I create an account?',\n          answer: 'To create an account:<br>1. Click \"Register\" on the login page<br>2. Fill out your personal information<br>3. Provide a valid email address<br>4. Create a secure password<br>5. Verify your email address<br>6. Wait for account approval from barangay staff',\n          expanded: false\n        },\n        {\n          id: 'acc-2',\n          question: 'I forgot my password. How do I reset it?',\n          answer: 'To reset your password:<br>1. Click \"Forgot Password\" on the login page<br>2. Enter your email address<br>3. Check your email for reset instructions<br>4. Click the reset link in the email<br>5. Create a new password<br><br>If you don\\'t receive the email, check your spam folder.',\n          expanded: false\n        },\n        {\n          id: 'acc-3',\n          question: 'How do I update my profile information?',\n          answer: 'To update your profile:<br>1. Go to \"My Profile\" in the sidebar<br>2. Click \"Edit Profile\"<br>3. Update your information<br>4. Click \"Save Changes\"<br><br>Note: Some changes may require verification by barangay staff.',\n          expanded: false\n        },\n        {\n          id: 'acc-4',\n          question: 'Why is my account pending approval?',\n          answer: 'New accounts require approval to ensure:<br>• You are a legitimate resident of Barangay Bula<br>• Your information is accurate<br>• System security is maintained<br><br>Approval typically takes 1-2 business days. You will receive an email notification once approved.',\n          expanded: false\n        }\n      ],\n      paymentFAQs: [\n        {\n          id: 'pay-1',\n          question: 'What payment methods are accepted?',\n          answer: 'We accept the following payment methods:<br>• Cash payment at the barangay office<br>• GCash<br>• PayMaya<br>• Bank transfer<br>• Credit/Debit cards (via PayMongo)<br><br>Online payments are processed securely through our payment partners.',\n          expanded: false\n        },\n        {\n          id: 'pay-2',\n          question: 'When do I need to pay for my document request?',\n          answer: 'Payment is required:<br>• Before document processing begins<br>• After submitting your request<br>• Within 24 hours of request submission<br><br>Unpaid requests will be automatically cancelled after 24 hours.',\n          expanded: false\n        },\n        {\n          id: 'pay-3',\n          question: 'Can I get a refund if I cancel my request?',\n          answer: 'Refund policy:<br>• Full refund if cancelled within 1 hour of payment<br>• No refund if processing has already started<br>• Partial refund may be available in special circumstances<br><br>Contact our office for refund requests.',\n          expanded: false\n        },\n        {\n          id: 'pay-4',\n          question: 'Is my payment information secure?',\n          answer: 'Yes, your payment information is secure:<br>• We use SSL encryption for all transactions<br>• Payment processing is handled by certified partners<br>• We do not store your credit card information<br>• All transactions are monitored for fraud<br><br>Your financial security is our priority.',\n          expanded: false\n        }\n      ],\n      userGuides: [\n        {\n          id: 'guide-1',\n          title: 'Getting Started',\n          description: 'Learn how to create an account and navigate the system',\n          icon: 'fas fa-play-circle'\n        },\n        {\n          id: 'guide-2',\n          title: 'Requesting Documents',\n          description: 'Step-by-step guide to requesting barangay documents',\n          icon: 'fas fa-file-alt'\n        },\n        {\n          id: 'guide-3',\n          title: 'Making Payments',\n          description: 'How to pay for your document requests online',\n          icon: 'fas fa-credit-card'\n        },\n        {\n          id: 'guide-4',\n          title: 'Tracking Requests',\n          description: 'Monitor the status of your document requests',\n          icon: 'fas fa-search'\n        },\n        {\n          id: 'guide-5',\n          title: 'Managing Your Profile',\n          description: 'Update your personal information and settings',\n          icon: 'fas fa-user-cog'\n        },\n        {\n          id: 'guide-6',\n          title: 'Troubleshooting',\n          description: 'Common issues and how to resolve them',\n          icon: 'fas fa-tools'\n        }\n      ]\n    };\n  },\n  methods: {\n    toggleFAQ(faqId) {\n      // Find the FAQ in all categories\n      const allFAQs = [...this.documentFAQs, ...this.accountFAQs, ...this.paymentFAQs];\n      const faq = allFAQs.find(f => f.id === faqId);\n      if (faq) {\n        faq.expanded = !faq.expanded;\n      }\n    },\n    scrollToSection(sectionId) {\n      const element = document.getElementById(sectionId);\n      if (element) {\n        element.scrollIntoView({ \n          behavior: 'smooth',\n          block: 'start'\n        });\n      }\n    },\n    openGuide(guideId) {\n      // This would typically open a detailed guide or navigate to a guide page\n      console.log('Opening guide:', guideId);\n      \n      // For now, show an alert with guide information\n      const guide = this.userGuides.find(g => g.id === guideId);\n      if (guide) {\n        alert(`Opening guide: ${guide.title}\\n\\n${guide.description}\\n\\nDetailed guides will be available in a future update.`);\n      }\n    }\n  }\n};\n"], "mappings": ";;AAAA,eAAe;EACbA,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,CACZ;QACEC,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,wCAAwC;QAClDC,MAAM,EAAE,gQAAgQ;QACxQC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,sCAAsC;QAChDC,MAAM,EAAE,wOAAwO;QAChPC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,yCAAyC;QACnDC,MAAM,EAAE,kOAAkO;QAC1OC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,yCAAyC;QACnDC,MAAM,EAAE,mQAAmQ;QAC3QC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,iCAAiC;QAC3CC,MAAM,EAAE,ySAAyS;QACjTC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,WAAW,EAAE,CACX;QACEJ,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,6BAA6B;QACvCC,MAAM,EAAE,gQAAgQ;QACxQC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,0CAA0C;QACpDC,MAAM,EAAE,0RAA0R;QAClSC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,yCAAyC;QACnDC,MAAM,EAAE,2NAA2N;QACnOC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,qCAAqC;QAC/CC,MAAM,EAAE,4QAA4Q;QACpRC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDE,WAAW,EAAE,CACX;QACEL,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,oCAAoC;QAC9CC,MAAM,EAAE,iPAAiP;QACzPC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,gDAAgD;QAC1DC,MAAM,EAAE,kNAAkN;QAC1NC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,4CAA4C;QACtDC,MAAM,EAAE,qOAAqO;QAC7OC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEH,EAAE,EAAE,OAAO;QACXC,QAAQ,EAAE,mCAAmC;QAC7CC,MAAM,EAAE,mSAAmS;QAC3SC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDG,UAAU,EAAE,CACV;QACEN,EAAE,EAAE,SAAS;QACbO,KAAK,EAAE,iBAAiB;QACxBC,WAAW,EAAE,wDAAwD;QACrEC,IAAI,EAAE;MACR,CAAC,EACD;QACET,EAAE,EAAE,SAAS;QACbO,KAAK,EAAE,sBAAsB;QAC7BC,WAAW,EAAE,qDAAqD;QAClEC,IAAI,EAAE;MACR,CAAC,EACD;QACET,EAAE,EAAE,SAAS;QACbO,KAAK,EAAE,iBAAiB;QACxBC,WAAW,EAAE,8CAA8C;QAC3DC,IAAI,EAAE;MACR,CAAC,EACD;QACET,EAAE,EAAE,SAAS;QACbO,KAAK,EAAE,mBAAmB;QAC1BC,WAAW,EAAE,8CAA8C;QAC3DC,IAAI,EAAE;MACR,CAAC,EACD;QACET,EAAE,EAAE,SAAS;QACbO,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE,+CAA+C;QAC5DC,IAAI,EAAE;MACR,CAAC,EACD;QACET,EAAE,EAAE,SAAS;QACbO,KAAK,EAAE,iBAAiB;QACxBC,WAAW,EAAE,uCAAuC;QACpDC,IAAI,EAAE;MACR,CAAC;IAEL,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,SAASA,CAACC,KAAK,EAAE;MACf;MACA,MAAMC,OAAO,GAAG,CAAC,GAAG,IAAI,CAACd,YAAY,EAAE,GAAG,IAAI,CAACK,WAAW,EAAE,GAAG,IAAI,CAACC,WAAW,CAAC;MAChF,MAAMS,GAAG,GAAGD,OAAO,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKY,KAAK,CAAC;MAC7C,IAAIE,GAAG,EAAE;QACPA,GAAG,CAACX,QAAQ,GAAG,CAACW,GAAG,CAACX,QAAQ;MAC9B;IACF,CAAC;IACDc,eAAeA,CAACC,SAAS,EAAE;MACzB,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC;MAClD,IAAIC,OAAO,EAAE;QACXA,OAAO,CAACG,cAAc,CAAC;UACrBC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF,CAAC;IACDC,SAASA,CAACC,OAAO,EAAE;MACjB;MACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,OAAO,CAAC;;MAEtC;MACA,MAAMG,KAAK,GAAG,IAAI,CAACvB,UAAU,CAACS,IAAI,CAACe,CAAC,IAAIA,CAAC,CAAC9B,EAAE,KAAK0B,OAAO,CAAC;MACzD,IAAIG,KAAK,EAAE;QACTE,KAAK,CAAC,kBAAkBF,KAAK,CAACtB,KAAK,OAAOsB,KAAK,CAACrB,WAAW,2DAA2D,CAAC;MACzH;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}