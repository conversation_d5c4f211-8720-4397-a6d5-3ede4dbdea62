{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || 'http://localhost:3000/api',\n  timeout: 15000,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  },\n  withCredentials: true // Enable credentials for CORS\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const adminToken = localStorage.getItem('adminToken');\n  const clientToken = localStorage.getItem('clientToken');\n\n  // Admin routes that require admin token\n  const adminRoutes = ['/admin/', '/users/', '/users?', '/notifications/admin', '/document-requests/admin'];\n\n  // Check if this is an admin route\n  const isAdminRoute = adminRoutes.some(route => {\n    if (!config.url) return false;\n\n    // Check for exact match or starts with pattern\n    if (route.endsWith('/') || route.includes('?')) {\n      return config.url.includes(route) || config.url.startsWith(route.replace('?', ''));\n    }\n    return config.url.includes(route);\n  });\n\n  // Also check for exact /users path\n  if (config.url === '/users' || config.url?.startsWith('/users?') || config.url?.startsWith('/users/')) {\n    if (adminToken) {\n      config.headers.Authorization = `Bearer ${adminToken}`;\n    }\n  } else if (isAdminRoute && adminToken) {\n    config.headers.Authorization = `Bearer ${adminToken}`;\n  } else if (clientToken) {\n    config.headers.Authorization = `Bearer ${clientToken}`;\n  }\n\n  // Debug logging\n  console.log('API Request:', {\n    url: config.url,\n    isAdminRoute: isAdminRoute || config.url === '/users' || config.url?.startsWith('/users'),\n    hasAdminToken: !!adminToken,\n    hasClientToken: !!clientToken,\n    authHeader: config.headers.Authorization ? 'Bearer ***' : 'None'\n  });\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  // Log error for debugging\n  console.error('API Error:', {\n    url: error.config?.url,\n    method: error.config?.method,\n    status: error.response?.status,\n    message: error.response?.data?.message || error.message,\n    data: error.response?.data\n  });\n  if (error.response?.status === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('clientToken');\n    localStorage.removeItem('clientData');\n    localStorage.removeItem('adminToken');\n    localStorage.removeItem('adminData');\n    // Don't redirect here, let components handle it\n  }\n\n  // Add more helpful error messages\n  if (!error.response) {\n    error.message = 'Network error - please check if the backend server is running on port 3000';\n  } else if (error.response.status === 500) {\n    error.message = 'Server error - please check the backend logs for details';\n  }\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "VUE_APP_API_URL", "timeout", "headers", "withCredentials", "interceptors", "request", "use", "config", "adminToken", "localStorage", "getItem", "clientToken", "adminRoutes", "isAdminRoute", "some", "route", "url", "endsWith", "includes", "startsWith", "replace", "Authorization", "console", "log", "hasAdminToken", "hasClientToken", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "Promise", "reject", "response", "method", "status", "message", "data", "removeItem"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || 'http://localhost:3000/api',\n  timeout: 15000,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n  withCredentials: true, // Enable credentials for CORS\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const adminToken = localStorage.getItem('adminToken');\n    const clientToken = localStorage.getItem('clientToken');\n\n    // Admin routes that require admin token\n    const adminRoutes = [\n      '/admin/',\n      '/users/',\n      '/users?',\n      '/notifications/admin',\n      '/document-requests/admin'\n    ];\n\n    // Check if this is an admin route\n    const isAdminRoute = adminRoutes.some(route => {\n      if (!config.url) return false;\n\n      // Check for exact match or starts with pattern\n      if (route.endsWith('/') || route.includes('?')) {\n        return config.url.includes(route) || config.url.startsWith(route.replace('?', ''));\n      }\n      return config.url.includes(route);\n    });\n\n    // Also check for exact /users path\n    if (config.url === '/users' || config.url?.startsWith('/users?') || config.url?.startsWith('/users/')) {\n      if (adminToken) {\n        config.headers.Authorization = `Bearer ${adminToken}`;\n      }\n    } else if (isAdminRoute && adminToken) {\n      config.headers.Authorization = `Bearer ${adminToken}`;\n    } else if (clientToken) {\n      config.headers.Authorization = `Bearer ${clientToken}`;\n    }\n\n    // Debug logging\n    console.log('API Request:', {\n      url: config.url,\n      isAdminRoute: isAdminRoute || config.url === '/users' || config.url?.startsWith('/users'),\n      hasAdminToken: !!adminToken,\n      hasClientToken: !!clientToken,\n      authHeader: config.headers.Authorization ? 'Bearer ***' : 'None'\n    });\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // Log error for debugging\n    console.error('API Error:', {\n      url: error.config?.url,\n      method: error.config?.method,\n      status: error.response?.status,\n      message: error.response?.data?.message || error.message,\n      data: error.response?.data\n    });\n\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('clientToken');\n      localStorage.removeItem('clientData');\n      localStorage.removeItem('adminToken');\n      localStorage.removeItem('adminData');\n      // Don't redirect here, let components handle it\n    }\n\n    // Add more helpful error messages\n    if (!error.response) {\n      error.message = 'Network error - please check if the backend server is running on port 3000';\n    } else if (error.response.status === 500) {\n      error.message = 'Server error - please check the backend logs for details';\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA2B;EACnEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE;EACZ,CAAC;EACDC,eAAe,EAAE,IAAI,CAAE;AACzB,CAAC,CAAC;;AAEF;AACAR,GAAG,CAACS,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EACrD,MAAMC,WAAW,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;;EAEvD;EACA,MAAME,WAAW,GAAG,CAClB,SAAS,EACT,SAAS,EACT,SAAS,EACT,sBAAsB,EACtB,0BAA0B,CAC3B;;EAED;EACA,MAAMC,YAAY,GAAGD,WAAW,CAACE,IAAI,CAACC,KAAK,IAAI;IAC7C,IAAI,CAACR,MAAM,CAACS,GAAG,EAAE,OAAO,KAAK;;IAE7B;IACA,IAAID,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,IAAIF,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC9C,OAAOX,MAAM,CAACS,GAAG,CAACE,QAAQ,CAACH,KAAK,CAAC,IAAIR,MAAM,CAACS,GAAG,CAACG,UAAU,CAACJ,KAAK,CAACK,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACpF;IACA,OAAOb,MAAM,CAACS,GAAG,CAACE,QAAQ,CAACH,KAAK,CAAC;EACnC,CAAC,CAAC;;EAEF;EACA,IAAIR,MAAM,CAACS,GAAG,KAAK,QAAQ,IAAIT,MAAM,CAACS,GAAG,EAAEG,UAAU,CAAC,SAAS,CAAC,IAAIZ,MAAM,CAACS,GAAG,EAAEG,UAAU,CAAC,SAAS,CAAC,EAAE;IACrG,IAAIX,UAAU,EAAE;MACdD,MAAM,CAACL,OAAO,CAACmB,aAAa,GAAG,UAAUb,UAAU,EAAE;IACvD;EACF,CAAC,MAAM,IAAIK,YAAY,IAAIL,UAAU,EAAE;IACrCD,MAAM,CAACL,OAAO,CAACmB,aAAa,GAAG,UAAUb,UAAU,EAAE;EACvD,CAAC,MAAM,IAAIG,WAAW,EAAE;IACtBJ,MAAM,CAACL,OAAO,CAACmB,aAAa,GAAG,UAAUV,WAAW,EAAE;EACxD;;EAEA;EACAW,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;IAC1BP,GAAG,EAAET,MAAM,CAACS,GAAG;IACfH,YAAY,EAAEA,YAAY,IAAIN,MAAM,CAACS,GAAG,KAAK,QAAQ,IAAIT,MAAM,CAACS,GAAG,EAAEG,UAAU,CAAC,QAAQ,CAAC;IACzFK,aAAa,EAAE,CAAC,CAAChB,UAAU;IAC3BiB,cAAc,EAAE,CAAC,CAACd,WAAW;IAC7Be,UAAU,EAAEnB,MAAM,CAACL,OAAO,CAACmB,aAAa,GAAG,YAAY,GAAG;EAC5D,CAAC,CAAC;EAEF,OAAOd,MAAM;AACf,CAAC,EACAoB,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhC,GAAG,CAACS,YAAY,CAAC0B,QAAQ,CAACxB,GAAG,CAC1BwB,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACAL,OAAO,CAACK,KAAK,CAAC,YAAY,EAAE;IAC1BX,GAAG,EAAEW,KAAK,CAACpB,MAAM,EAAES,GAAG;IACtBe,MAAM,EAAEJ,KAAK,CAACpB,MAAM,EAAEwB,MAAM;IAC5BC,MAAM,EAAEL,KAAK,CAACG,QAAQ,EAAEE,MAAM;IAC9BC,OAAO,EAAEN,KAAK,CAACG,QAAQ,EAAEI,IAAI,EAAED,OAAO,IAAIN,KAAK,CAACM,OAAO;IACvDC,IAAI,EAAEP,KAAK,CAACG,QAAQ,EAAEI;EACxB,CAAC,CAAC;EAEF,IAAIP,KAAK,CAACG,QAAQ,EAAEE,MAAM,KAAK,GAAG,EAAE;IAClC;IACAvB,YAAY,CAAC0B,UAAU,CAAC,aAAa,CAAC;IACtC1B,YAAY,CAAC0B,UAAU,CAAC,YAAY,CAAC;IACrC1B,YAAY,CAAC0B,UAAU,CAAC,YAAY,CAAC;IACrC1B,YAAY,CAAC0B,UAAU,CAAC,WAAW,CAAC;IACpC;EACF;;EAEA;EACA,IAAI,CAACR,KAAK,CAACG,QAAQ,EAAE;IACnBH,KAAK,CAACM,OAAO,GAAG,4EAA4E;EAC9F,CAAC,MAAM,IAAIN,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;IACxCL,KAAK,CAACM,OAAO,GAAG,0DAA0D;EAC5E;EAEA,OAAOL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAehC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}