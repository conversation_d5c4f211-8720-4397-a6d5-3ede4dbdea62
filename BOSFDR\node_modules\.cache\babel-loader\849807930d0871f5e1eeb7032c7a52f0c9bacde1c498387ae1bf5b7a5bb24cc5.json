{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, vShow as _vShow, withDirectives as _withDirectives, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"help-support-page\"\n};\nconst _hoisted_2 = {\n  class: \"quick-actions\"\n};\nconst _hoisted_3 = {\n  id: \"faq\",\n  class: \"content-section\"\n};\nconst _hoisted_4 = {\n  class: \"faq-categories\"\n};\nconst _hoisted_5 = {\n  class: \"faq-category\"\n};\nconst _hoisted_6 = {\n  class: \"faq-items\"\n};\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = {\n  class: \"faq-question\"\n};\nconst _hoisted_9 = {\n  class: \"faq-answer\"\n};\nconst _hoisted_10 = [\"innerHTML\"];\nconst _hoisted_11 = {\n  class: \"faq-category\"\n};\nconst _hoisted_12 = {\n  class: \"faq-items\"\n};\nconst _hoisted_13 = [\"onClick\"];\nconst _hoisted_14 = {\n  class: \"faq-question\"\n};\nconst _hoisted_15 = {\n  class: \"faq-answer\"\n};\nconst _hoisted_16 = [\"innerHTML\"];\nconst _hoisted_17 = {\n  class: \"faq-category\"\n};\nconst _hoisted_18 = {\n  class: \"faq-items\"\n};\nconst _hoisted_19 = [\"onClick\"];\nconst _hoisted_20 = {\n  class: \"faq-question\"\n};\nconst _hoisted_21 = {\n  class: \"faq-answer\"\n};\nconst _hoisted_22 = [\"innerHTML\"];\nconst _hoisted_23 = {\n  id: \"guides\",\n  class: \"content-section\"\n};\nconst _hoisted_24 = {\n  class: \"guides-grid\"\n};\nconst _hoisted_25 = {\n  class: \"guide-icon\"\n};\nconst _hoisted_26 = {\n  class: \"guide-content\"\n};\nconst _hoisted_27 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Page Header \"), _cache[14] || (_cache[14] = _createStaticVNode(\"<div class=\\\"page-header\\\" data-v-58bc3dfa><div class=\\\"header-content\\\" data-v-58bc3dfa><div class=\\\"header-text\\\" data-v-58bc3dfa><h1 class=\\\"page-title\\\" data-v-58bc3dfa><i class=\\\"fas fa-headset\\\" data-v-58bc3dfa></i> Help &amp; Support </h1><p class=\\\"page-description\\\" data-v-58bc3dfa> Get assistance with your document requests and learn how to use our services </p></div></div></div>\", 1)), _createCommentVNode(\" Quick Actions \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.scrollToSection('faq'))\n  }, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n    class: \"action-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\"\n  })], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"action-content\"\n  }, [_createElementVNode(\"h3\", null, \"Frequently Asked Questions\"), _createElementVNode(\"p\", null, \"Find answers to common questions\")], -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[1] || (_cache[1] = $event => _ctx.scrollToSection('contact'))\n  }, _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n    class: \"action-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-phone\"\n  })], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"action-content\"\n  }, [_createElementVNode(\"h3\", null, \"Contact Us\"), _createElementVNode(\"p\", null, \"Get in touch with our support team\")], -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[2] || (_cache[2] = $event => _ctx.scrollToSection('guides'))\n  }, _cache[6] || (_cache[6] = [_createElementVNode(\"div\", {\n    class: \"action-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-book\"\n  })], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"action-content\"\n  }, [_createElementVNode(\"h3\", null, \"User Guides\"), _createElementVNode(\"p\", null, \"Step-by-step instructions\")], -1 /* HOISTED */)])), _createElementVNode(\"div\", {\n    class: \"action-card\",\n    onClick: _cache[3] || (_cache[3] = $event => _ctx.scrollToSection('office-hours'))\n  }, _cache[7] || (_cache[7] = [_createElementVNode(\"div\", {\n    class: \"action-icon\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clock\"\n  })], -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"action-content\"\n  }, [_createElementVNode(\"h3\", null, \"Office Hours\"), _createElementVNode(\"p\", null, \"When we're available to help\")], -1 /* HOISTED */)]))]), _createCommentVNode(\" FAQ Section \"), _createElementVNode(\"div\", _hoisted_3, [_cache[11] || (_cache[11] = _createElementVNode(\"h2\", {\n    class: \"section-title\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-question-circle\"\n  }), _createTextVNode(\" Frequently Asked Questions \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[8] || (_cache[8] = _createElementVNode(\"h3\", {\n    class: \"category-title\"\n  }, \"Document Requests\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.documentFAQs, faq => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"faq-item\",\n      key: faq.id,\n      onClick: $event => _ctx.toggleFAQ(faq.id)\n    }, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"span\", null, _toDisplayString(faq.question), 1 /* TEXT */), _createElementVNode(\"i\", {\n      class: _normalizeClass([\"fas fa-chevron-down\", {\n        'rotated': faq.expanded\n      }])\n    }, null, 2 /* CLASS */)]), _withDirectives(_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"p\", {\n      innerHTML: faq.answer\n    }, null, 8 /* PROPS */, _hoisted_10)], 512 /* NEED_PATCH */), [[_vShow, faq.expanded]])], 8 /* PROPS */, _hoisted_7);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_11, [_cache[9] || (_cache[9] = _createElementVNode(\"h3\", {\n    class: \"category-title\"\n  }, \"Account & Profile\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.accountFAQs, faq => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"faq-item\",\n      key: faq.id,\n      onClick: $event => _ctx.toggleFAQ(faq.id)\n    }, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"span\", null, _toDisplayString(faq.question), 1 /* TEXT */), _createElementVNode(\"i\", {\n      class: _normalizeClass([\"fas fa-chevron-down\", {\n        'rotated': faq.expanded\n      }])\n    }, null, 2 /* CLASS */)]), _withDirectives(_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"p\", {\n      innerHTML: faq.answer\n    }, null, 8 /* PROPS */, _hoisted_16)], 512 /* NEED_PATCH */), [[_vShow, faq.expanded]])], 8 /* PROPS */, _hoisted_13);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_17, [_cache[10] || (_cache[10] = _createElementVNode(\"h3\", {\n    class: \"category-title\"\n  }, \"Payments & Fees\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.paymentFAQs, faq => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"faq-item\",\n      key: faq.id,\n      onClick: $event => _ctx.toggleFAQ(faq.id)\n    }, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"span\", null, _toDisplayString(faq.question), 1 /* TEXT */), _createElementVNode(\"i\", {\n      class: _normalizeClass([\"fas fa-chevron-down\", {\n        'rotated': faq.expanded\n      }])\n    }, null, 2 /* CLASS */)]), _withDirectives(_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"p\", {\n      innerHTML: faq.answer\n    }, null, 8 /* PROPS */, _hoisted_22)], 512 /* NEED_PATCH */), [[_vShow, faq.expanded]])], 8 /* PROPS */, _hoisted_19);\n  }), 128 /* KEYED_FRAGMENT */))])])])]), _createCommentVNode(\" User Guides Section \"), _createElementVNode(\"div\", _hoisted_23, [_cache[13] || (_cache[13] = _createElementVNode(\"h2\", {\n    class: \"section-title\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-book\"\n  }), _createTextVNode(\" User Guides \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_24, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.userGuides, guide => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"guide-card\",\n      key: guide.id\n    }, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"i\", {\n      class: _normalizeClass(guide.icon)\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"h3\", null, _toDisplayString(guide.title), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString(guide.description), 1 /* TEXT */), _createElementVNode(\"button\", {\n      class: \"guide-btn\",\n      onClick: $event => _ctx.openGuide(guide.id)\n    }, [...(_cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n      class: \"fas fa-arrow-right\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\" View Guide \")]))], 8 /* PROPS */, _hoisted_27)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" Contact Information Section \"), _cache[15] || (_cache[15] = _createStaticVNode(\"<div id=\\\"contact\\\" class=\\\"content-section\\\" data-v-58bc3dfa><h2 class=\\\"section-title\\\" data-v-58bc3dfa><i class=\\\"fas fa-phone\\\" data-v-58bc3dfa></i> Contact Information </h2><div class=\\\"contact-grid\\\" data-v-58bc3dfa><div class=\\\"contact-card\\\" data-v-58bc3dfa><div class=\\\"contact-icon\\\" data-v-58bc3dfa><i class=\\\"fas fa-map-marker-alt\\\" data-v-58bc3dfa></i></div><div class=\\\"contact-info\\\" data-v-58bc3dfa><h3 data-v-58bc3dfa>Barangay Office Address</h3><p data-v-58bc3dfa>Barangay Bula<br data-v-58bc3dfa>Camarines Sur, Philippines<br data-v-58bc3dfa>Postal Code: 4422</p></div></div><div class=\\\"contact-card\\\" data-v-58bc3dfa><div class=\\\"contact-icon\\\" data-v-58bc3dfa><i class=\\\"fas fa-phone\\\" data-v-58bc3dfa></i></div><div class=\\\"contact-info\\\" data-v-58bc3dfa><h3 data-v-58bc3dfa>Phone Numbers</h3><p data-v-58bc3dfa>Main Office: (*************<br data-v-58bc3dfa>Emergency: (*************<br data-v-58bc3dfa>Mobile: +63 ************</p></div></div><div class=\\\"contact-card\\\" data-v-58bc3dfa><div class=\\\"contact-icon\\\" data-v-58bc3dfa><i class=\\\"fas fa-envelope\\\" data-v-58bc3dfa></i></div><div class=\\\"contact-info\\\" data-v-58bc3dfa><h3 data-v-58bc3dfa>Email Addresses</h3><p data-v-58bc3dfa>General Inquiries: <EMAIL><br data-v-58bc3dfa>Document Requests: <EMAIL><br data-v-58bc3dfa>Technical Support: <EMAIL></p></div></div><div class=\\\"contact-card\\\" data-v-58bc3dfa><div class=\\\"contact-icon\\\" data-v-58bc3dfa><i class=\\\"fab fa-facebook\\\" data-v-58bc3dfa></i></div><div class=\\\"contact-info\\\" data-v-58bc3dfa><h3 data-v-58bc3dfa>Social Media</h3><p data-v-58bc3dfa>Facebook: @BarangayBulaOfficial<br data-v-58bc3dfa>Follow us for updates and announcements</p></div></div></div></div>\", 1)), _createCommentVNode(\" Office Hours Section \"), _cache[16] || (_cache[16] = _createStaticVNode(\"<div id=\\\"office-hours\\\" class=\\\"content-section\\\" data-v-58bc3dfa><h2 class=\\\"section-title\\\" data-v-58bc3dfa><i class=\\\"fas fa-clock\\\" data-v-58bc3dfa></i> Office Hours &amp; Service Schedule </h2><div class=\\\"hours-grid\\\" data-v-58bc3dfa><div class=\\\"hours-card\\\" data-v-58bc3dfa><h3 data-v-58bc3dfa>Regular Office Hours</h3><div class=\\\"schedule\\\" data-v-58bc3dfa><div class=\\\"schedule-item\\\" data-v-58bc3dfa><span class=\\\"day\\\" data-v-58bc3dfa>Monday - Friday</span><span class=\\\"time\\\" data-v-58bc3dfa>8:00 AM - 5:00 PM</span></div><div class=\\\"schedule-item\\\" data-v-58bc3dfa><span class=\\\"day\\\" data-v-58bc3dfa>Saturday</span><span class=\\\"time\\\" data-v-58bc3dfa>8:00 AM - 12:00 PM</span></div><div class=\\\"schedule-item\\\" data-v-58bc3dfa><span class=\\\"day\\\" data-v-58bc3dfa>Sunday</span><span class=\\\"time\\\" data-v-58bc3dfa>Closed</span></div></div></div><div class=\\\"hours-card\\\" data-v-58bc3dfa><h3 data-v-58bc3dfa>Document Processing</h3><div class=\\\"schedule\\\" data-v-58bc3dfa><div class=\\\"schedule-item\\\" data-v-58bc3dfa><span class=\\\"day\\\" data-v-58bc3dfa>Barangay Clearance</span><span class=\\\"time\\\" data-v-58bc3dfa>Same day processing</span></div><div class=\\\"schedule-item\\\" data-v-58bc3dfa><span class=\\\"day\\\" data-v-58bc3dfa>Cedula</span><span class=\\\"time\\\" data-v-58bc3dfa>Same day processing</span></div><div class=\\\"schedule-item\\\" data-v-58bc3dfa><span class=\\\"day\\\" data-v-58bc3dfa>Other Documents</span><span class=\\\"time\\\" data-v-58bc3dfa>1-3 business days</span></div></div></div><div class=\\\"hours-card\\\" data-v-58bc3dfa><h3 data-v-58bc3dfa>Emergency Services</h3><div class=\\\"schedule\\\" data-v-58bc3dfa><div class=\\\"schedule-item\\\" data-v-58bc3dfa><span class=\\\"day\\\" data-v-58bc3dfa>24/7 Hotline</span><span class=\\\"time\\\" data-v-58bc3dfa>(*************</span></div><div class=\\\"schedule-item\\\" data-v-58bc3dfa><span class=\\\"day\\\" data-v-58bc3dfa>Emergency Response</span><span class=\\\"time\\\" data-v-58bc3dfa>Available 24/7</span></div></div></div></div></div>\", 1)), _createCommentVNode(\" Document Fees Section \"), _cache[17] || (_cache[17] = _createStaticVNode(\"<div class=\\\"content-section\\\" data-v-58bc3dfa><h2 class=\\\"section-title\\\" data-v-58bc3dfa><i class=\\\"fas fa-peso-sign\\\" data-v-58bc3dfa></i> Document Fees </h2><div class=\\\"fees-table\\\" data-v-58bc3dfa><table data-v-58bc3dfa><thead data-v-58bc3dfa><tr data-v-58bc3dfa><th data-v-58bc3dfa>Document Type</th><th data-v-58bc3dfa>Fee</th><th data-v-58bc3dfa>Processing Time</th></tr></thead><tbody data-v-58bc3dfa><tr data-v-58bc3dfa><td data-v-58bc3dfa>Barangay Clearance</td><td data-v-58bc3dfa>₱50.00</td><td data-v-58bc3dfa>Same day</td></tr><tr data-v-58bc3dfa><td data-v-58bc3dfa>Cedula (Community Tax Certificate)</td><td data-v-58bc3dfa>₱30.00</td><td data-v-58bc3dfa>Same day</td></tr></tbody></table></div></div>\", 1)), _createCommentVNode(\" Emergency Contact \"), _cache[18] || (_cache[18] = _createStaticVNode(\"<div class=\\\"emergency-section\\\" data-v-58bc3dfa><div class=\\\"emergency-card\\\" data-v-58bc3dfa><div class=\\\"emergency-icon\\\" data-v-58bc3dfa><i class=\\\"fas fa-exclamation-triangle\\\" data-v-58bc3dfa></i></div><div class=\\\"emergency-content\\\" data-v-58bc3dfa><h3 data-v-58bc3dfa>Emergency Contact</h3><p data-v-58bc3dfa>For urgent matters or emergencies, please call our 24/7 hotline:</p><div class=\\\"emergency-number\\\" data-v-58bc3dfa><i class=\\\"fas fa-phone\\\" data-v-58bc3dfa></i><span data-v-58bc3dfa>(*************</span></div></div></div></div>\", 1))]);\n}", "map": {"version": 3, "names": ["class", "id", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "onClick", "_cache", "$event", "_ctx", "scrollToSection", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_Fragment", "_renderList", "documentFAQs", "faq", "key", "toggleFAQ", "_hoisted_8", "_toDisplayString", "question", "_normalizeClass", "expanded", "_hoisted_9", "innerHTML", "answer", "_hoisted_11", "_hoisted_12", "accountFAQs", "_hoisted_14", "_hoisted_15", "_hoisted_17", "_hoisted_18", "paymentFAQs", "_hoisted_20", "_hoisted_21", "_hoisted_23", "_hoisted_24", "userGuides", "guide", "_hoisted_25", "icon", "_hoisted_26", "title", "description", "openGuide"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\HelpSupport.vue"], "sourcesContent": ["<template>\n  <div class=\"help-support-page\">\n    <!-- <PERSON> Header -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"header-text\">\n          <h1 class=\"page-title\">\n            <i class=\"fas fa-headset\"></i>\n            Help & Support\n          </h1>\n          <p class=\"page-description\">\n            Get assistance with your document requests and learn how to use our services\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"quick-actions\">\n      <div class=\"action-card\" @click=\"scrollToSection('faq')\">\n        <div class=\"action-icon\">\n          <i class=\"fas fa-question-circle\"></i>\n        </div>\n        <div class=\"action-content\">\n          <h3>Frequently Asked Questions</h3>\n          <p>Find answers to common questions</p>\n        </div>\n      </div>\n\n      <div class=\"action-card\" @click=\"scrollToSection('contact')\">\n        <div class=\"action-icon\">\n          <i class=\"fas fa-phone\"></i>\n        </div>\n        <div class=\"action-content\">\n          <h3>Contact Us</h3>\n          <p>Get in touch with our support team</p>\n        </div>\n      </div>\n\n      <div class=\"action-card\" @click=\"scrollToSection('guides')\">\n        <div class=\"action-icon\">\n          <i class=\"fas fa-book\"></i>\n        </div>\n        <div class=\"action-content\">\n          <h3>User Guides</h3>\n          <p>Step-by-step instructions</p>\n        </div>\n      </div>\n\n      <div class=\"action-card\" @click=\"scrollToSection('office-hours')\">\n        <div class=\"action-icon\">\n          <i class=\"fas fa-clock\"></i>\n        </div>\n        <div class=\"action-content\">\n          <h3>Office Hours</h3>\n          <p>When we're available to help</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- FAQ Section -->\n    <div id=\"faq\" class=\"content-section\">\n      <h2 class=\"section-title\">\n        <i class=\"fas fa-question-circle\"></i>\n        Frequently Asked Questions\n      </h2>\n      \n      <div class=\"faq-categories\">\n        <div class=\"faq-category\">\n          <h3 class=\"category-title\">Document Requests</h3>\n          <div class=\"faq-items\">\n            <div class=\"faq-item\" v-for=\"faq in documentFAQs\" :key=\"faq.id\" @click=\"toggleFAQ(faq.id)\">\n              <div class=\"faq-question\">\n                <span>{{ faq.question }}</span>\n                <i class=\"fas fa-chevron-down\" :class=\"{ 'rotated': faq.expanded }\"></i>\n              </div>\n              <div class=\"faq-answer\" v-show=\"faq.expanded\">\n                <p v-html=\"faq.answer\"></p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"faq-category\">\n          <h3 class=\"category-title\">Account & Profile</h3>\n          <div class=\"faq-items\">\n            <div class=\"faq-item\" v-for=\"faq in accountFAQs\" :key=\"faq.id\" @click=\"toggleFAQ(faq.id)\">\n              <div class=\"faq-question\">\n                <span>{{ faq.question }}</span>\n                <i class=\"fas fa-chevron-down\" :class=\"{ 'rotated': faq.expanded }\"></i>\n              </div>\n              <div class=\"faq-answer\" v-show=\"faq.expanded\">\n                <p v-html=\"faq.answer\"></p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"faq-category\">\n          <h3 class=\"category-title\">Payments & Fees</h3>\n          <div class=\"faq-items\">\n            <div class=\"faq-item\" v-for=\"faq in paymentFAQs\" :key=\"faq.id\" @click=\"toggleFAQ(faq.id)\">\n              <div class=\"faq-question\">\n                <span>{{ faq.question }}</span>\n                <i class=\"fas fa-chevron-down\" :class=\"{ 'rotated': faq.expanded }\"></i>\n              </div>\n              <div class=\"faq-answer\" v-show=\"faq.expanded\">\n                <p v-html=\"faq.answer\"></p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- User Guides Section -->\n    <div id=\"guides\" class=\"content-section\">\n      <h2 class=\"section-title\">\n        <i class=\"fas fa-book\"></i>\n        User Guides\n      </h2>\n      \n      <div class=\"guides-grid\">\n        <div class=\"guide-card\" v-for=\"guide in userGuides\" :key=\"guide.id\">\n          <div class=\"guide-icon\">\n            <i :class=\"guide.icon\"></i>\n          </div>\n          <div class=\"guide-content\">\n            <h3>{{ guide.title }}</h3>\n            <p>{{ guide.description }}</p>\n            <button class=\"guide-btn\" @click=\"openGuide(guide.id)\">\n              <i class=\"fas fa-arrow-right\"></i>\n              View Guide\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Contact Information Section -->\n    <div id=\"contact\" class=\"content-section\">\n      <h2 class=\"section-title\">\n        <i class=\"fas fa-phone\"></i>\n        Contact Information\n      </h2>\n      \n      <div class=\"contact-grid\">\n        <div class=\"contact-card\">\n          <div class=\"contact-icon\">\n            <i class=\"fas fa-map-marker-alt\"></i>\n          </div>\n          <div class=\"contact-info\">\n            <h3>Barangay Office Address</h3>\n            <p>Barangay Bula<br>Camarines Sur, Philippines<br>Postal Code: 4422</p>\n          </div>\n        </div>\n\n        <div class=\"contact-card\">\n          <div class=\"contact-icon\">\n            <i class=\"fas fa-phone\"></i>\n          </div>\n          <div class=\"contact-info\">\n            <h3>Phone Numbers</h3>\n            <p>Main Office: (*************<br>Emergency: (*************<br>Mobile: +63 ************</p>\n          </div>\n        </div>\n\n        <div class=\"contact-card\">\n          <div class=\"contact-icon\">\n            <i class=\"fas fa-envelope\"></i>\n          </div>\n          <div class=\"contact-info\">\n            <h3>Email Addresses</h3>\n            <p>General Inquiries: <EMAIL><br>Document Requests: <EMAIL><br>Technical Support: <EMAIL></p>\n          </div>\n        </div>\n\n        <div class=\"contact-card\">\n          <div class=\"contact-icon\">\n            <i class=\"fab fa-facebook\"></i>\n          </div>\n          <div class=\"contact-info\">\n            <h3>Social Media</h3>\n            <p>Facebook: @BarangayBulaOfficial<br>Follow us for updates and announcements</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Office Hours Section -->\n    <div id=\"office-hours\" class=\"content-section\">\n      <h2 class=\"section-title\">\n        <i class=\"fas fa-clock\"></i>\n        Office Hours & Service Schedule\n      </h2>\n      \n      <div class=\"hours-grid\">\n        <div class=\"hours-card\">\n          <h3>Regular Office Hours</h3>\n          <div class=\"schedule\">\n            <div class=\"schedule-item\">\n              <span class=\"day\">Monday - Friday</span>\n              <span class=\"time\">8:00 AM - 5:00 PM</span>\n            </div>\n            <div class=\"schedule-item\">\n              <span class=\"day\">Saturday</span>\n              <span class=\"time\">8:00 AM - 12:00 PM</span>\n            </div>\n            <div class=\"schedule-item\">\n              <span class=\"day\">Sunday</span>\n              <span class=\"time\">Closed</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"hours-card\">\n          <h3>Document Processing</h3>\n          <div class=\"schedule\">\n            <div class=\"schedule-item\">\n              <span class=\"day\">Barangay Clearance</span>\n              <span class=\"time\">Same day processing</span>\n            </div>\n            <div class=\"schedule-item\">\n              <span class=\"day\">Cedula</span>\n              <span class=\"time\">Same day processing</span>\n            </div>\n            <div class=\"schedule-item\">\n              <span class=\"day\">Other Documents</span>\n              <span class=\"time\">1-3 business days</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"hours-card\">\n          <h3>Emergency Services</h3>\n          <div class=\"schedule\">\n            <div class=\"schedule-item\">\n              <span class=\"day\">24/7 Hotline</span>\n              <span class=\"time\">(*************</span>\n            </div>\n            <div class=\"schedule-item\">\n              <span class=\"day\">Emergency Response</span>\n              <span class=\"time\">Available 24/7</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Document Fees Section -->\n    <div class=\"content-section\">\n      <h2 class=\"section-title\">\n        <i class=\"fas fa-peso-sign\"></i>\n        Document Fees\n      </h2>\n      \n      <div class=\"fees-table\">\n        <table>\n          <thead>\n            <tr>\n              <th>Document Type</th>\n              <th>Fee</th>\n              <th>Processing Time</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr>\n              <td>Barangay Clearance</td>\n              <td>₱50.00</td>\n              <td>Same day</td>\n            </tr>\n            <tr>\n              <td>Cedula (Community Tax Certificate)</td>\n              <td>₱30.00</td>\n              <td>Same day</td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Emergency Contact -->\n    <div class=\"emergency-section\">\n      <div class=\"emergency-card\">\n        <div class=\"emergency-icon\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n        </div>\n        <div class=\"emergency-content\">\n          <h3>Emergency Contact</h3>\n          <p>For urgent matters or emergencies, please call our 24/7 hotline:</p>\n          <div class=\"emergency-number\">\n            <i class=\"fas fa-phone\"></i>\n            <span>(*************</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script src=\"./js/helpSupport.js\"></script>\n<style scoped src=\"./css/helpSupport.css\"></style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAiBvBA,KAAK,EAAC;AAAe;;EA2CrBC,EAAE,EAAC,KAAK;EAACD,KAAK,EAAC;;;EAMbA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;;EAEbA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAY;;;EAOxBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;;EAEbA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAY;;;EAOxBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;;EAEbA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAY;;;EAU5BC,EAAE,EAAC,QAAQ;EAACD,KAAK,EAAC;;;EAMhBA,KAAK,EAAC;AAAa;;EAEfA,KAAK,EAAC;AAAY;;EAGlBA,KAAK,EAAC;AAAe;;;uBA9HlCE,mBAAA,CAwSM,OAxSNC,UAwSM,GAvSJC,mBAAA,iBAAoB,E,gcAepBA,mBAAA,mBAAsB,EACtBC,mBAAA,CAwCM,OAxCNC,UAwCM,GAvCJD,mBAAA,CAQM;IARDL,KAAK,EAAC,aAAa;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,eAAe;gCAC9CN,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAsC;IAAnCL,KAAK,EAAC;EAAwB,G,qBAEnCK,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAgB,IACzBK,mBAAA,CAAmC,YAA/B,4BAA0B,GAC9BA,mBAAA,CAAuC,WAApC,kCAAgC,E,wBAIvCA,mBAAA,CAQM;IARDL,KAAK,EAAC,aAAa;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,eAAe;gCAC9CN,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,G,qBAEzBK,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAgB,IACzBK,mBAAA,CAAmB,YAAf,YAAU,GACdA,mBAAA,CAAyC,WAAtC,oCAAkC,E,wBAIzCA,mBAAA,CAQM;IARDL,KAAK,EAAC,aAAa;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,eAAe;gCAC9CN,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAA2B;IAAxBL,KAAK,EAAC;EAAa,G,qBAExBK,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAgB,IACzBK,mBAAA,CAAoB,YAAhB,aAAW,GACfA,mBAAA,CAAgC,WAA7B,2BAAyB,E,wBAIhCA,mBAAA,CAQM;IARDL,KAAK,EAAC,aAAa;IAAEO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,eAAe;gCAC9CN,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,G,qBAEzBK,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAgB,IACzBK,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAmC,WAAhC,8BAA4B,E,0BAKrCD,mBAAA,iBAAoB,EACpBC,mBAAA,CAoDM,OApDNO,UAoDM,G,4BAnDJP,mBAAA,CAGK;IAHDL,KAAK,EAAC;EAAe,IACvBK,mBAAA,CAAsC;IAAnCL,KAAK,EAAC;EAAwB,I,iBAAK,8BAExC,E,sBAEAK,mBAAA,CA6CM,OA7CNQ,UA6CM,GA5CJR,mBAAA,CAaM,OAbNS,UAaM,G,0BAZJT,mBAAA,CAAiD;IAA7CL,KAAK,EAAC;EAAgB,GAAC,mBAAiB,sBAC5CK,mBAAA,CAUM,OAVNU,UAUM,I,kBATJb,mBAAA,CAQMc,SAAA,QAAAC,WAAA,CAR8BP,IAAA,CAAAQ,YAAY,EAAnBC,GAAG;yBAAhCjB,mBAAA,CAQM;MARDF,KAAK,EAAC,UAAU;MAA8BoB,GAAG,EAAED,GAAG,CAAClB,EAAE;MAAGM,OAAK,EAAAE,MAAA,IAAEC,IAAA,CAAAW,SAAS,CAACF,GAAG,CAAClB,EAAE;QACtFI,mBAAA,CAGM,OAHNiB,UAGM,GAFJjB,mBAAA,CAA+B,cAAAkB,gBAAA,CAAtBJ,GAAG,CAACK,QAAQ,kBACrBnB,mBAAA,CAAwE;MAArEL,KAAK,EAAAyB,eAAA,EAAC,qBAAqB;QAAA,WAAsBN,GAAG,CAACO;MAAQ;+CAElErB,mBAAA,CAEM,OAFNsB,UAEM,GADJtB,mBAAA,CAA2B;MAAxBuB,SAAmB,EAAXT,GAAG,CAACU;4EADeV,GAAG,CAACO,QAAQ,E;sCAOlDrB,mBAAA,CAaM,OAbNyB,WAaM,G,0BAZJzB,mBAAA,CAAiD;IAA7CL,KAAK,EAAC;EAAgB,GAAC,mBAAiB,sBAC5CK,mBAAA,CAUM,OAVN0B,WAUM,I,kBATJ7B,mBAAA,CAQMc,SAAA,QAAAC,WAAA,CAR8BP,IAAA,CAAAsB,WAAW,EAAlBb,GAAG;yBAAhCjB,mBAAA,CAQM;MARDF,KAAK,EAAC,UAAU;MAA6BoB,GAAG,EAAED,GAAG,CAAClB,EAAE;MAAGM,OAAK,EAAAE,MAAA,IAAEC,IAAA,CAAAW,SAAS,CAACF,GAAG,CAAClB,EAAE;QACrFI,mBAAA,CAGM,OAHN4B,WAGM,GAFJ5B,mBAAA,CAA+B,cAAAkB,gBAAA,CAAtBJ,GAAG,CAACK,QAAQ,kBACrBnB,mBAAA,CAAwE;MAArEL,KAAK,EAAAyB,eAAA,EAAC,qBAAqB;QAAA,WAAsBN,GAAG,CAACO;MAAQ;+CAElErB,mBAAA,CAEM,OAFN6B,WAEM,GADJ7B,mBAAA,CAA2B;MAAxBuB,SAAmB,EAAXT,GAAG,CAACU;4EADeV,GAAG,CAACO,QAAQ,E;sCAOlDrB,mBAAA,CAaM,OAbN8B,WAaM,G,4BAZJ9B,mBAAA,CAA+C;IAA3CL,KAAK,EAAC;EAAgB,GAAC,iBAAe,sBAC1CK,mBAAA,CAUM,OAVN+B,WAUM,I,kBATJlC,mBAAA,CAQMc,SAAA,QAAAC,WAAA,CAR8BP,IAAA,CAAA2B,WAAW,EAAlBlB,GAAG;yBAAhCjB,mBAAA,CAQM;MARDF,KAAK,EAAC,UAAU;MAA6BoB,GAAG,EAAED,GAAG,CAAClB,EAAE;MAAGM,OAAK,EAAAE,MAAA,IAAEC,IAAA,CAAAW,SAAS,CAACF,GAAG,CAAClB,EAAE;QACrFI,mBAAA,CAGM,OAHNiC,WAGM,GAFJjC,mBAAA,CAA+B,cAAAkB,gBAAA,CAAtBJ,GAAG,CAACK,QAAQ,kBACrBnB,mBAAA,CAAwE;MAArEL,KAAK,EAAAyB,eAAA,EAAC,qBAAqB;QAAA,WAAsBN,GAAG,CAACO;MAAQ;+CAElErB,mBAAA,CAEM,OAFNkC,WAEM,GADJlC,mBAAA,CAA2B;MAAxBuB,SAAmB,EAAXT,GAAG,CAACU;4EADeV,GAAG,CAACO,QAAQ,E;0CAStDtB,mBAAA,yBAA4B,EAC5BC,mBAAA,CAqBM,OArBNmC,WAqBM,G,4BApBJnC,mBAAA,CAGK;IAHDL,KAAK,EAAC;EAAe,IACvBK,mBAAA,CAA2B;IAAxBL,KAAK,EAAC;EAAa,I,iBAAK,eAE7B,E,sBAEAK,mBAAA,CAcM,OAdNoC,WAcM,I,kBAbJvC,mBAAA,CAYMc,SAAA,QAAAC,WAAA,CAZkCP,IAAA,CAAAgC,UAAU,EAAnBC,KAAK;yBAApCzC,mBAAA,CAYM;MAZDF,KAAK,EAAC,YAAY;MAA8BoB,GAAG,EAAEuB,KAAK,CAAC1C;QAC9DI,mBAAA,CAEM,OAFNuC,WAEM,GADJvC,mBAAA,CAA2B;MAAvBL,KAAK,EAAAyB,eAAA,CAAEkB,KAAK,CAACE,IAAI;+BAEvBxC,mBAAA,CAOM,OAPNyC,WAOM,GANJzC,mBAAA,CAA0B,YAAAkB,gBAAA,CAAnBoB,KAAK,CAACI,KAAK,kBAClB1C,mBAAA,CAA8B,WAAAkB,gBAAA,CAAxBoB,KAAK,CAACK,WAAW,kBACvB3C,mBAAA,CAGS;MAHDL,KAAK,EAAC,WAAW;MAAEO,OAAK,EAAAE,MAAA,IAAEC,IAAA,CAAAuC,SAAS,CAACN,KAAK,CAAC1C,EAAE;yCAClDI,mBAAA,CAAkC;MAA/BL,KAAK,EAAC;IAAoB,4B,iBAAK,cAEpC,E;sCAMRI,mBAAA,iCAAoC,E,+xDAkDpCA,mBAAA,0BAA6B,E,igEA4D7BA,mBAAA,2BAA8B,E,uwBAgC9BA,mBAAA,uBAA0B,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}