{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\nimport { Modal } from 'bootstrap';\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      },\n      // Modal data\n      viewUserData: null,\n      addUserLoading: false,\n      editUserLoading: false,\n      // Form validation errors\n      formErrors: {},\n      editFormErrors: {},\n      // Add user form\n      addUserForm: {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null\n      },\n      // Edit user form\n      editUserForm: {\n        id: null,\n        username: '',\n        email: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        status: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null,\n        // Password reset fields\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      },\n      // Available options\n      genderOptions: [{\n        value: 'male',\n        label: 'Male'\n      }, {\n        value: 'female',\n        label: 'Female'\n      }],\n      statusOptions: [{\n        value: 'active',\n        label: 'Active'\n      }, {\n        value: 'inactive',\n        label: 'Inactive'\n      }, {\n        value: 'suspended',\n        label: 'Suspended'\n      }, {\n        value: 'pending_verification',\n        label: 'Pending Verification'\n      }]\n    };\n  },\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Make bootstrap available globally for this component\n    this.$bootstrap = {\n      Modal\n    };\n\n    // Debug authentication\n    const adminToken = localStorage.getItem('adminToken');\n    const adminData = localStorage.getItem('adminData');\n    console.log('🔍 Admin Authentication Debug:');\n    console.log('  Admin Token:', adminToken ? 'EXISTS' : 'NOT FOUND');\n    console.log('  Admin Data:', adminData ? 'EXISTS' : 'NOT FOUND');\n    if (adminData) {\n      try {\n        const data = JSON.parse(adminData);\n        console.log('  Username:', data.username);\n        console.log('  Role:', data.role);\n        console.log('  Status:', data.status);\n      } catch (e) {\n        console.error('  Error parsing admin data:', e);\n      }\n    }\n    if (!adminToken) {\n      this.showToast('error', 'You are not logged in as admin. Please login first.');\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUserStats();\n    await this.loadUsers();\n  },\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n    // Load user statistics\n    async loadUserStats() {\n      try {\n        const response = await userManagementService.getUserStats();\n        if (response.success) {\n          this.userStats = response.data;\n        } else {\n          this.calculateStats();\n        }\n      } catch (error) {\n        console.error('Failed to load user statistics:', error);\n        this.calculateStats();\n      }\n    },\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50,\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true : this.filterStatus === 'inactive' ? false : undefined\n        };\n        const response = await userManagementService.getUsers(params);\n        if (response.success) {\n          this.users = response.data.users.map(user => userManagementService.formatUserData(user));\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.showToast('error', error.message || 'Failed to load users');\n        this.users = [];\n        this.filteredUsers = [];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user => user.full_name.toLowerCase().includes(query) || user.email.toLowerCase().includes(query) || user.username.toLowerCase().includes(query));\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n    // User actions\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n          const statusText = newStatus === 'active' ? 'activated' : 'suspended';\n          this.showToast('success', `User ${user.full_name} has been ${statusText}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to update user status. Please try again.';\n        this.showToast('error', errorMessage);\n      }\n    },\n    async deleteUser(user) {\n      const confirmMessage = `Are you sure you want to delete user \"${user.full_name}\"?\\n\\nThis will:\\n- Deactivate the user account\\n- Prevent future logins\\n- Preserve data for audit purposes\\n\\nThis action can be reversed by reactivating the user.`;\n      if (!confirm(confirmMessage)) {\n        return;\n      }\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n        if (response.success) {\n          this.showToast('success', `User ${user.full_name} has been deleted successfully.`);\n\n          // Reload data to reflect changes\n          await this.loadUsers();\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to delete user. Please try again.';\n        this.showToast('error', errorMessage);\n      }\n    },\n    // Helper methods\n    showToast(type, message) {\n      if (this.$toast?.[type]) {\n        this.$toast[type](message);\n      } else {\n        console.log(`${type.toUpperCase()}: ${message}`);\n        if (type === 'error') alert(`Error: ${message}`);else if (type === 'success') alert(`Success: ${message}`);\n      }\n    },\n    closeModal(modalId) {\n      try {\n        const modal = Modal.getInstance(document.getElementById(modalId));\n        if (modal) modal.hide();\n      } catch (error) {\n        console.error('Error closing modal:', error);\n      }\n    },\n    handleFormError(error, errorField) {\n      console.error('Form error:', error);\n\n      // Handle server validation errors\n      if (error.response?.data?.details) {\n        const serverErrors = {};\n        error.response.data.details.forEach(detail => {\n          if (detail.path) serverErrors[detail.path] = detail.msg;\n        });\n        this[errorField] = {\n          ...this[errorField],\n          ...serverErrors\n        };\n      }\n      const errorMessage = error.response?.data?.message || error.message || 'Operation failed';\n      this.showToast('error', errorMessage);\n    },\n    // Modal methods\n    showAddUserModal() {\n      this.resetAddUserForm();\n      try {\n        const modalElement = document.getElementById('addUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing add user modal:', error);\n        this.$toast?.error?.('Failed to open add user modal');\n      }\n    },\n    resetAddUserForm() {\n      Object.assign(this.addUserForm, {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        phone_number: '',\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null\n      });\n      this.formErrors = {};\n    },\n    clearRoleSpecificFields() {\n      const form = this.addUserForm;\n      if (form.role === 'admin') {\n        // Clear client fields\n        Object.assign(form, {\n          birth_date: '',\n          gender: '',\n          civil_status_id: 1,\n          nationality: 'Filipino',\n          house_number: '',\n          street: '',\n          subdivision: '',\n          barangay: '',\n          city_municipality: '',\n          province: '',\n          postal_code: '',\n          years_of_residency: null,\n          months_of_residency: null\n        });\n      } else if (form.role === 'client') {\n        // Clear admin fields\n        Object.assign(form, {\n          position: '',\n          department: '',\n          employee_id: '',\n          hire_date: ''\n        });\n      }\n    },\n    validateAddUserForm() {\n      const errors = {};\n      const form = this.addUserForm;\n\n      // Basic validation\n      if (!form.username?.length || form.username.length < 3) errors.username = 'Username must be at least 3 characters long';\n      if (!form.password?.length || form.password.length < 6) errors.password = 'Password must be at least 6 characters long';\n      if (!form.first_name?.trim() || form.first_name.trim().length < 2) errors.first_name = 'First name must be at least 2 characters long';\n      if (!form.last_name?.trim() || form.last_name.trim().length < 2) errors.last_name = 'Last name must be at least 2 characters long';\n      if (!form.role) errors.role = 'Please select a user type';\n      if (!form.phone_number?.trim() || form.phone_number.trim().length < 10) errors.phone_number = 'Please provide a valid phone number';\n      if (form.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(form.email)) errors.email = 'Please provide a valid email address';\n\n      // Client-specific validation\n      if (form.role === 'client') {\n        if (!form.birth_date) errors.birth_date = 'Birth date is required for clients';\n        if (!form.gender) errors.gender = 'Gender is required for clients';\n        if (!form.barangay?.trim()) errors.barangay = 'Barangay is required for clients';\n        if (!form.city_municipality?.trim()) errors.city_municipality = 'City/Municipality is required for clients';\n        if (!form.province?.trim()) errors.province = 'Province is required for clients';\n      }\n      this.formErrors = errors;\n      return Object.keys(errors).length === 0;\n    },\n    validateEditUserForm() {\n      const errors = {};\n      const form = this.editUserForm;\n\n      // Basic validation\n      if (!form.username?.length || form.username.length < 3) errors.username = 'Username must be at least 3 characters long';\n      if (!form.first_name?.trim() || form.first_name.trim().length < 2) errors.first_name = 'First name must be at least 2 characters long';\n      if (!form.last_name?.trim() || form.last_name.trim().length < 2) errors.last_name = 'Last name must be at least 2 characters long';\n      if (!form.phone_number?.trim() || form.phone_number.trim().length < 10) errors.phone_number = 'Please provide a valid phone number';\n      if (form.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(form.email)) errors.email = 'Please provide a valid email address';\n\n      // Password reset validation\n      if (form.resetPassword) {\n        if (!form.newPassword?.length || form.newPassword.length < 6) errors.newPassword = 'New password must be at least 6 characters long';\n        if (form.newPassword !== form.confirmPassword) errors.confirmPassword = 'Passwords do not match';\n      }\n      this.editFormErrors = errors;\n      return Object.keys(errors).length === 0;\n    },\n    getFullAddress(user) {\n      const parts = [];\n      if (user.house_number) parts.push(user.house_number);\n      if (user.street) parts.push(user.street);\n      if (user.subdivision) parts.push(user.subdivision);\n      if (user.barangay) parts.push(user.barangay);\n      if (user.city_municipality) parts.push(user.city_municipality);\n      if (user.province) parts.push(user.province);\n      return parts.join(', ') || 'No address provided';\n    },\n    async submitAddUser() {\n      if (!this.validateAddUserForm()) {\n        this.showToast('error', 'Please fix the validation errors before submitting.');\n        return;\n      }\n      this.addUserLoading = true;\n      try {\n        const userData = {\n          ...this.addUserForm\n        };\n\n        // Convert numeric fields\n        ['years_of_residency', 'months_of_residency', 'civil_status_id'].forEach(field => {\n          if (userData[field]) userData[field] = parseInt(userData[field]);\n        });\n        const response = await userManagementService.createUser(userData);\n        if (response.success) {\n          this.showToast('success', 'User created successfully');\n          this.closeModal('addUserModal');\n          this.resetAddUserForm();\n          await Promise.all([this.loadUsers(), this.loadUserStats()]);\n        } else {\n          throw new Error(response.message || 'Failed to create user');\n        }\n      } catch (error) {\n        this.handleFormError(error, 'formErrors');\n      } finally {\n        this.addUserLoading = false;\n      }\n    },\n    editUser(user) {\n      this.editUserForm = {\n        id: user.id,\n        username: user.username,\n        email: user.email || '',\n        first_name: user.first_name || '',\n        middle_name: user.middle_name || '',\n        last_name: user.last_name || '',\n        suffix: user.suffix || '',\n        role: user.type,\n        status: user.status,\n        phone_number: user.phone_number || '',\n        position: user.position || '',\n        department: user.department || '',\n        employee_id: user.employee_id || '',\n        hire_date: user.hire_date ? user.hire_date.split('T')[0] : '',\n        birth_date: user.birth_date ? user.birth_date.split('T')[0] : '',\n        gender: user.gender || '',\n        civil_status_id: user.civil_status_id || 1,\n        nationality: user.nationality || 'Filipino',\n        house_number: user.house_number || '',\n        street: user.street || '',\n        subdivision: user.subdivision || '',\n        barangay: user.barangay || '',\n        city_municipality: user.city_municipality || '',\n        province: user.province || '',\n        postal_code: user.postal_code || '',\n        years_of_residency: user.years_of_residency || null,\n        months_of_residency: user.months_of_residency || null,\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      };\n      this.editFormErrors = {};\n      try {\n        const modal = new Modal(document.getElementById('editUserModal'));\n        modal.show();\n      } catch (error) {\n        this.showToast('error', 'Failed to open edit user modal');\n      }\n    },\n    async submitEditUser() {\n      if (!this.validateEditUserForm()) {\n        this.showToast('error', 'Please fix the validation errors before submitting.');\n        return;\n      }\n      this.editUserLoading = true;\n      try {\n        const form = this.editUserForm;\n        const updateData = {\n          username: form.username,\n          email: form.email,\n          first_name: form.first_name,\n          middle_name: form.middle_name,\n          last_name: form.last_name,\n          suffix: form.suffix,\n          status: form.status,\n          phone_number: form.phone_number\n        };\n\n        // Add role-specific fields\n        if (form.role === 'admin') {\n          Object.assign(updateData, {\n            position: form.position,\n            department: form.department,\n            employee_id: form.employee_id,\n            hire_date: form.hire_date\n          });\n        }\n\n        // Add password if resetting\n        if (form.resetPassword && form.newPassword) {\n          updateData.password = form.newPassword;\n        }\n        const response = await userManagementService.updateUser(form.id, updateData);\n        if (response.success) {\n          this.showToast('success', 'User updated successfully');\n          this.closeModal('editUserModal');\n          await Promise.all([this.loadUsers(), this.loadUserStats()]);\n        } else {\n          throw new Error(response.message || 'Failed to update user');\n        }\n      } catch (error) {\n        this.handleFormError(error, 'editFormErrors');\n      } finally {\n        this.editUserLoading = false;\n      }\n    },\n    async viewUser(user) {\n      try {\n        const response = await userManagementService.getUser(user.id);\n        if (response.success) {\n          this.viewUserData = userManagementService.formatUserData(response.data);\n          const modal = new Modal(document.getElementById('viewUserModal'));\n          modal.show();\n        } else {\n          throw new Error(response.message || 'Failed to load user details');\n        }\n      } catch (error) {\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to load user details';\n        this.showToast('error', errorMessage);\n      }\n    }\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};", "map": {"version": 3, "names": ["Ad<PERSON><PERSON><PERSON><PERSON>", "AdminSidebar", "adminAuthService", "userManagementService", "Modal", "name", "components", "data", "sidebarCollapsed", "showUserDropdown", "isMobile", "adminData", "users", "filteredUsers", "searchQuery", "filterStatus", "filterType", "currentPage", "itemsPerPage", "loading", "userStats", "total", "active", "pending", "admins", "viewUserData", "addUserLoading", "editUserLoading", "formErrors", "editFormErrors", "addUserForm", "username", "email", "password", "first_name", "middle_name", "last_name", "suffix", "role", "phone_number", "position", "department", "employee_id", "hire_date", "birth_date", "gender", "civil_status_id", "nationality", "house_number", "street", "subdivision", "barangay", "city_municipality", "province", "postal_code", "years_of_residency", "months_of_residency", "editUserForm", "id", "status", "resetPassword", "newPassword", "confirmPassword", "genderOptions", "value", "label", "statusOptions", "computed", "activeMenu", "path", "$route", "includes", "paginatedUsers", "start", "end", "slice", "totalPages", "Math", "ceil", "length", "visiblePages", "pages", "max", "min", "i", "push", "mounted", "isLoggedIn", "$router", "initializeUI", "$bootstrap", "adminToken", "localStorage", "getItem", "console", "log", "JSON", "parse", "e", "error", "showToast", "loadAdminProfile", "loadUserStats", "loadUsers", "beforeUnmount", "handleResize", "window", "removeEventListener", "methods", "innerWidth", "saved", "was<PERSON><PERSON><PERSON>", "addEventListener", "handleSidebarToggle", "setItem", "stringify", "handleMenuChange", "menu", "routes", "handleUserDropdownToggle", "handleMenuAction", "action", "closeMobileSidebar", "handleLogout", "logout", "response", "getProfile", "success", "getAdminData", "getUserStats", "calculateStats", "params", "page", "limit", "search", "undefined", "is_active", "getUsers", "map", "user", "formatUserData", "Error", "message", "filter", "u", "type", "searchUsers", "filterUsers", "filtered", "query", "toLowerCase", "full_name", "changePage", "getInitials", "fullName", "split", "char<PERSON>t", "join", "toUpperCase", "getStatusBadgeClass", "classes", "formatStatus", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "toggleUserStatus", "newStatus", "reason", "updateUserStatus", "statusText", "errorMessage", "deleteUser", "confirmMessage", "confirm", "$toast", "alert", "closeModal", "modalId", "modal", "getInstance", "document", "getElementById", "hide", "handleFormError", "<PERSON><PERSON>ield", "details", "serverErrors", "for<PERSON>ach", "detail", "msg", "showAddUserModal", "resetAddUserForm", "modalElement", "show", "Object", "assign", "clearRoleSpecificFields", "form", "validateAddUserForm", "errors", "trim", "test", "keys", "validateEditUserForm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parts", "submitAddUser", "userData", "field", "parseInt", "createUser", "Promise", "all", "editUser", "submitEditUser", "updateData", "updateUser", "viewUser", "getUser"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminUsers.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-users\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"container-fluid p-4\">\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-outline-success btn-sm\" @click=\"loadUsers\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                  <button class=\"btn btn-success btn-sm\" @click=\"showAddUserModal\">\n                    <i class=\"fas fa-user-plus me-1\"></i>\n                    Add User\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- User Statistics -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-primary shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">\n                        Total Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.total || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-users fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-success shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">\n                        Active Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.active || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-check fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-warning shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">\n                        Pending Verification\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.pending || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-clock fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-info shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">\n                        Admin Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.admins || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-shield fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Users Table -->\n          <div class=\"row\">\n            <div class=\"col-12\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex justify-content-between align-items-center\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-users me-2\"></i>\n                    User List\n                  </h6>\n                  <div class=\"d-flex gap-2\">\n                    <select class=\"form-select form-select-sm\" v-model=\"filterStatus\" @change=\"filterUsers\">\n                      <option value=\"\">All Status</option>\n                      <option value=\"active\">Active</option>\n                      <option value=\"inactive\">Inactive</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"suspended\">Suspended</option>\n                    </select>\n                    <select class=\"form-select form-select-sm\" v-model=\"filterType\" @change=\"filterUsers\">\n                      <option value=\"\">All Types</option>\n                      <option value=\"client\">Clients</option>\n                      <option value=\"admin\">Admins</option>\n                    </select>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <!-- Search Bar -->\n                  <div class=\"row mb-3\">\n                    <div class=\"col-md-6\">\n                      <div class=\"input-group\">\n                        <span class=\"input-group-text\">\n                          <i class=\"fas fa-search\"></i>\n                        </span>\n                        <input\n                          type=\"text\"\n                          class=\"form-control\"\n                          placeholder=\"Search users by name, email, or username...\"\n                          v-model=\"searchQuery\"\n                          @input=\"searchUsers\"\n                        >\n                      </div>\n                    </div>\n                    <div class=\"col-md-6 text-end\">\n                      <span class=\"text-muted\">\n                        Showing {{ filteredUsers.length }} of {{ users.length }} users\n                      </span>\n                    </div>\n                  </div>\n\n                  <!-- Loading State -->\n                  <div v-if=\"loading\" class=\"text-center py-4\">\n                    <div class=\"spinner-border text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Loading...</span>\n                    </div>\n                    <p class=\"text-muted mt-2\">Loading users...</p>\n                  </div>\n\n                  <!-- Empty State -->\n                  <div v-else-if=\"filteredUsers.length === 0\" class=\"text-center py-5\">\n                    <i class=\"fas fa-users fa-3x text-gray-300 mb-3\"></i>\n                    <h5 class=\"text-gray-600\">No users found</h5>\n                    <p class=\"text-muted\">\n                      {{ searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.' }}\n                    </p>\n                  </div>\n\n                  <!-- Users Table -->\n                  <div v-else class=\"table-responsive\">\n                    <table class=\"table table-hover\">\n                      <thead class=\"table-light\">\n                        <tr>\n                          <th>User</th>\n                          <th>Email</th>\n                          <th>Type</th>\n                          <th>Status</th>\n                          <th>Registered</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        <tr v-for=\"user in paginatedUsers\" :key=\"user.id\">\n                          <td>\n                            <div class=\"d-flex align-items-center\">\n                              <div class=\"user-avatar me-3\">\n                                <img v-if=\"user.profile_picture\" :src=\"user.profile_picture\" :alt=\"user.full_name\" class=\"rounded-circle\">\n                                <div v-else class=\"avatar-placeholder rounded-circle\">\n                                  {{ getInitials(user.full_name) }}\n                                </div>\n                              </div>\n                              <div>\n                                <div class=\"fw-bold\">{{ user.full_name }}</div>\n                                <div class=\"text-muted small\">@{{ user.username }}</div>\n                              </div>\n                            </div>\n                          </td>\n                          <td>{{ user.email }}</td>\n                          <td>\n                            <span class=\"badge\" :class=\"user.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                              {{ user.type === 'admin' ? 'Admin' : 'Client' }}\n                            </span>\n                          </td>\n                          <td>\n                            <span class=\"badge\" :class=\"getStatusBadgeClass(user.status)\">\n                              {{ formatStatus(user.status) }}\n                            </span>\n                          </td>\n                          <td>{{ formatDate(user.created_at) }}</td>\n                          <td>\n                            <div class=\"btn-group btn-group-sm\">\n                              <button class=\"btn btn-outline-primary\" @click=\"viewUser(user)\" title=\"View Details\">\n                                <i class=\"fas fa-eye\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-warning\" @click=\"editUser(user)\" title=\"Edit User\">\n                                <i class=\"fas fa-edit\"></i>\n                              </button>\n                              <button\n                                class=\"btn\"\n                                :class=\"user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'\"\n                                @click=\"toggleUserStatus(user)\"\n                                :title=\"user.status === 'active' ? 'Suspend User' : 'Activate User'\"\n                              >\n                                <i :class=\"user.status === 'active' ? 'fas fa-pause' : 'fas fa-play'\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-danger\" @click=\"deleteUser(user)\" title=\"Delete User\">\n                                <i class=\"fas fa-trash\"></i>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      </tbody>\n                    </table>\n                  </div>\n\n                  <!-- Pagination -->\n                  <div v-if=\"totalPages > 1\" class=\"d-flex justify-content-between align-items-center mt-3\">\n                    <div class=\"text-muted\">\n                      Page {{ currentPage }} of {{ totalPages }}\n                    </div>\n                    <nav>\n                      <ul class=\"pagination pagination-sm mb-0\">\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === 1 }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage - 1)\" :disabled=\"currentPage === 1\">\n                            Previous\n                          </button>\n                        </li>\n                        <li\n                          v-for=\"page in visiblePages\"\n                          :key=\"page\"\n                          class=\"page-item\"\n                          :class=\"{ active: page === currentPage }\"\n                        >\n                          <button class=\"page-link\" @click=\"changePage(page)\">{{ page }}</button>\n                        </li>\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === totalPages }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage + 1)\" :disabled=\"currentPage === totalPages\">\n                            Next\n                          </button>\n                        </li>\n                      </ul>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n\n    <!-- Add User Modal -->\n    <div class=\"modal fade\" id=\"addUserModal\" tabindex=\"-1\" aria-labelledby=\"addUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"addUserModalLabel\">\n              <i class=\"fas fa-user-plus me-2\"></i>\n              Add New User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitAddUser\">\n              <!-- Basic Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addUsername\"\n                    v-model=\"addUserForm.username\"\n                    :class=\"{ 'is-invalid': formErrors.username }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.username\" class=\"invalid-feedback\">\n                    {{ formErrors.username }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addPassword\" class=\"form-label\">Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"addPassword\"\n                    v-model=\"addUserForm.password\"\n                    :class=\"{ 'is-invalid': formErrors.password }\"\n                    required\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"formErrors.password\" class=\"invalid-feedback\">\n                    {{ formErrors.password }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Personal Information -->\n              <div class=\"row\">\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addFirstName\"\n                    v-model=\"addUserForm.first_name\"\n                    :class=\"{ 'is-invalid': formErrors.first_name }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.first_name\" class=\"invalid-feedback\">\n                    {{ formErrors.first_name }}\n                  </div>\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addMiddleName\" class=\"form-label\">Middle Name</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addMiddleName\"\n                    v-model=\"addUserForm.middle_name\"\n                  >\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addLastName\"\n                    v-model=\"addUserForm.last_name\"\n                    :class=\"{ 'is-invalid': formErrors.last_name }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.last_name\" class=\"invalid-feedback\">\n                    {{ formErrors.last_name }}\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addSuffix\" class=\"form-label\">Suffix</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addSuffix\"\n                    v-model=\"addUserForm.suffix\"\n                    placeholder=\"Jr, Sr, III, etc.\"\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addRole\" class=\"form-label\">User Type *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"addRole\"\n                    v-model=\"addUserForm.role\"\n                    :class=\"{ 'is-invalid': formErrors.role }\"\n                    required\n                    @change=\"clearRoleSpecificFields\"\n                  >\n                    <option value=\"\">Select User Type</option>\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                  <div v-if=\"formErrors.role\" class=\"invalid-feedback\">\n                    {{ formErrors.role }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Contact Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addEmail\" class=\"form-label\">Email</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"addEmail\"\n                    v-model=\"addUserForm.email\"\n                    :class=\"{ 'is-invalid': formErrors.email }\"\n                  >\n                  <div v-if=\"formErrors.email\" class=\"invalid-feedback\">\n                    {{ formErrors.email }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addPhone\" class=\"form-label\">Phone Number *</label>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"addPhone\"\n                    v-model=\"addUserForm.phone_number\"\n                    :class=\"{ 'is-invalid': formErrors.phone_number }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.phone_number\" class=\"invalid-feedback\">\n                    {{ formErrors.phone_number }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Admin-specific fields -->\n              <div v-if=\"addUserForm.role === 'admin'\">\n                <hr>\n                <h6 class=\"text-primary mb-3\">\n                  <i class=\"fas fa-user-shield me-2\"></i>\n                  Administrator Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addEmployeeId\" class=\"form-label\">Employee ID</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addEmployeeId\"\n                      v-model=\"addUserForm.employee_id\"\n                      placeholder=\"e.g., EMP001\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addPosition\" class=\"form-label\">Position</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addPosition\"\n                      v-model=\"addUserForm.position\"\n                      placeholder=\"e.g., Barangay Secretary\"\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addDepartment\" class=\"form-label\">Department</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addDepartment\"\n                      v-model=\"addUserForm.department\"\n                      placeholder=\"e.g., Administration\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addHireDate\" class=\"form-label\">Hire Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"addHireDate\"\n                      v-model=\"addUserForm.hire_date\"\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Client-specific fields -->\n              <div v-if=\"addUserForm.role === 'client'\">\n                <hr>\n                <h6 class=\"text-info mb-3\">\n                  <i class=\"fas fa-user me-2\"></i>\n                  Client Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addBirthDate\" class=\"form-label\">Birth Date *</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"addBirthDate\"\n                      v-model=\"addUserForm.birth_date\"\n                      :class=\"{ 'is-invalid': formErrors.birth_date }\"\n                      required\n                    >\n                    <div v-if=\"formErrors.birth_date\" class=\"invalid-feedback\">\n                      {{ formErrors.birth_date }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addGender\" class=\"form-label\">Gender *</label>\n                    <select\n                      class=\"form-select\"\n                      id=\"addGender\"\n                      v-model=\"addUserForm.gender\"\n                      :class=\"{ 'is-invalid': formErrors.gender }\"\n                      required\n                    >\n                      <option value=\"\">Select Gender</option>\n                      <option value=\"male\">Male</option>\n                      <option value=\"female\">Female</option>\n                    </select>\n                    <div v-if=\"formErrors.gender\" class=\"invalid-feedback\">\n                      {{ formErrors.gender }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addCivilStatus\" class=\"form-label\">Civil Status *</label>\n                    <select\n                      class=\"form-select\"\n                      id=\"addCivilStatus\"\n                      v-model=\"addUserForm.civil_status_id\"\n                      required\n                    >\n                      <option value=\"1\">Single</option>\n                      <option value=\"2\">Married</option>\n                      <option value=\"3\">Widowed</option>\n                      <option value=\"4\">Divorced</option>\n                      <option value=\"5\">Separated</option>\n                    </select>\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addNationality\" class=\"form-label\">Nationality</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addNationality\"\n                      v-model=\"addUserForm.nationality\"\n                      placeholder=\"Filipino\"\n                    >\n                  </div>\n                </div>\n\n                <!-- Address Information -->\n                <h6 class=\"text-secondary mb-3 mt-4\">\n                  <i class=\"fas fa-map-marker-alt me-2\"></i>\n                  Address Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addHouseNumber\" class=\"form-label\">House Number</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addHouseNumber\"\n                      v-model=\"addUserForm.house_number\"\n                      placeholder=\"e.g., 123\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addStreet\" class=\"form-label\">Street</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addStreet\"\n                      v-model=\"addUserForm.street\"\n                      placeholder=\"e.g., Main Street\"\n                    >\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addSubdivision\" class=\"form-label\">Subdivision</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addSubdivision\"\n                      v-model=\"addUserForm.subdivision\"\n                      placeholder=\"e.g., Greenfield Village\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addBarangay\" class=\"form-label\">Barangay *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addBarangay\"\n                      v-model=\"addUserForm.barangay\"\n                      :class=\"{ 'is-invalid': formErrors.barangay }\"\n                      required\n                      placeholder=\"e.g., Barangay Bula\"\n                    >\n                    <div v-if=\"formErrors.barangay\" class=\"invalid-feedback\">\n                      {{ formErrors.barangay }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addCity\" class=\"form-label\">City/Municipality *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addCity\"\n                      v-model=\"addUserForm.city_municipality\"\n                      :class=\"{ 'is-invalid': formErrors.city_municipality }\"\n                      required\n                      placeholder=\"e.g., Camarines Sur\"\n                    >\n                    <div v-if=\"formErrors.city_municipality\" class=\"invalid-feedback\">\n                      {{ formErrors.city_municipality }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addProvince\" class=\"form-label\">Province *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addProvince\"\n                      v-model=\"addUserForm.province\"\n                      :class=\"{ 'is-invalid': formErrors.province }\"\n                      required\n                      placeholder=\"e.g., Camarines Sur\"\n                    >\n                    <div v-if=\"formErrors.province\" class=\"invalid-feedback\">\n                      {{ formErrors.province }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addPostalCode\" class=\"form-label\">Postal Code</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addPostalCode\"\n                      v-model=\"addUserForm.postal_code\"\n                      placeholder=\"e.g., 4422\"\n                    >\n                  </div>\n                </div>\n\n                <!-- Residency Information -->\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addYearsResidency\" class=\"form-label\">Years of Residency</label>\n                    <input\n                      type=\"number\"\n                      class=\"form-control\"\n                      id=\"addYearsResidency\"\n                      v-model=\"addUserForm.years_of_residency\"\n                      min=\"0\"\n                      placeholder=\"e.g., 5\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addMonthsResidency\" class=\"form-label\">Additional Months</label>\n                    <input\n                      type=\"number\"\n                      class=\"form-control\"\n                      id=\"addMonthsResidency\"\n                      v-model=\"addUserForm.months_of_residency\"\n                      min=\"0\"\n                      max=\"11\"\n                      placeholder=\"e.g., 6\"\n                    >\n                  </div>\n                </div>\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitAddUser\" :disabled=\"addUserLoading\">\n              <span v-if=\"addUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-plus me-2\"></i>\n              {{ addUserLoading ? 'Creating...' : 'Create User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Edit User Modal -->\n    <div class=\"modal fade\" id=\"editUserModal\" tabindex=\"-1\" aria-labelledby=\"editUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"editUserModalLabel\">\n              <i class=\"fas fa-user-edit me-2\"></i>\n              Edit User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitEditUser\" v-if=\"editUserForm.id\">\n              <!-- Basic Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editUsername\"\n                    v-model=\"editUserForm.username\"\n                    :class=\"{ 'is-invalid': editFormErrors.username }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.username\" class=\"invalid-feedback\">\n                    {{ editFormErrors.username }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editStatus\" class=\"form-label\">Status *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"editStatus\"\n                    v-model=\"editUserForm.status\"\n                    :class=\"{ 'is-invalid': editFormErrors.status }\"\n                    required\n                  >\n                    <option value=\"active\">Active</option>\n                    <option value=\"inactive\">Inactive</option>\n                    <option value=\"suspended\">Suspended</option>\n                    <option value=\"pending_verification\">Pending Verification</option>\n                  </select>\n                  <div v-if=\"editFormErrors.status\" class=\"invalid-feedback\">\n                    {{ editFormErrors.status }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Personal Information -->\n              <div class=\"row\">\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editFirstName\"\n                    v-model=\"editUserForm.first_name\"\n                    :class=\"{ 'is-invalid': editFormErrors.first_name }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.first_name\" class=\"invalid-feedback\">\n                    {{ editFormErrors.first_name }}\n                  </div>\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editMiddleName\" class=\"form-label\">Middle Name</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editMiddleName\"\n                    v-model=\"editUserForm.middle_name\"\n                  >\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editLastName\"\n                    v-model=\"editUserForm.last_name\"\n                    :class=\"{ 'is-invalid': editFormErrors.last_name }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.last_name\" class=\"invalid-feedback\">\n                    {{ editFormErrors.last_name }}\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editSuffix\" class=\"form-label\">Suffix</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editSuffix\"\n                    v-model=\"editUserForm.suffix\"\n                    placeholder=\"Jr, Sr, III, etc.\"\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editRole\" class=\"form-label\">User Type *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"editRole\"\n                    v-model=\"editUserForm.role\"\n                    :class=\"{ 'is-invalid': editFormErrors.role }\"\n                    required\n                    disabled\n                  >\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                  <small class=\"text-muted\">User type cannot be changed after creation</small>\n                  <div v-if=\"editFormErrors.role\" class=\"invalid-feedback\">\n                    {{ editFormErrors.role }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Contact Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editEmail\" class=\"form-label\">Email</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"editEmail\"\n                    v-model=\"editUserForm.email\"\n                    :class=\"{ 'is-invalid': editFormErrors.email }\"\n                  >\n                  <div v-if=\"editFormErrors.email\" class=\"invalid-feedback\">\n                    {{ editFormErrors.email }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editPhone\" class=\"form-label\">Phone Number *</label>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"editPhone\"\n                    v-model=\"editUserForm.phone_number\"\n                    :class=\"{ 'is-invalid': editFormErrors.phone_number }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.phone_number\" class=\"invalid-feedback\">\n                    {{ editFormErrors.phone_number }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Admin-specific fields -->\n              <div v-if=\"editUserForm.role === 'admin'\">\n                <hr>\n                <h6 class=\"text-primary mb-3\">\n                  <i class=\"fas fa-user-shield me-2\"></i>\n                  Administrator Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editEmployeeId\" class=\"form-label\">Employee ID</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editEmployeeId\"\n                      v-model=\"editUserForm.employee_id\"\n                      placeholder=\"e.g., EMP001\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editPosition\" class=\"form-label\">Position</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editPosition\"\n                      v-model=\"editUserForm.position\"\n                      placeholder=\"e.g., Barangay Secretary\"\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editDepartment\" class=\"form-label\">Department</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editDepartment\"\n                      v-model=\"editUserForm.department\"\n                      placeholder=\"e.g., Administration\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editHireDate\" class=\"form-label\">Hire Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"editHireDate\"\n                      v-model=\"editUserForm.hire_date\"\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Client-specific fields (read-only for editing) -->\n              <div v-if=\"editUserForm.role === 'client'\">\n                <hr>\n                <h6 class=\"text-info mb-3\">\n                  <i class=\"fas fa-user me-2\"></i>\n                  Client Information\n                </h6>\n                <div class=\"alert alert-info\">\n                  <i class=\"fas fa-info-circle me-2\"></i>\n                  Client-specific information can only be updated by the client through their profile.\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label class=\"form-label\">Birth Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      v-model=\"editUserForm.birth_date\"\n                      readonly\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label class=\"form-label\">Gender</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      :value=\"editUserForm.gender ? editUserForm.gender.charAt(0).toUpperCase() + editUserForm.gender.slice(1) : ''\"\n                      readonly\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-12 mb-3\">\n                    <label class=\"form-label\">Address</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      :value=\"getFullAddress(editUserForm)\"\n                      readonly\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Password Reset Section -->\n              <hr>\n              <div class=\"row\">\n                <div class=\"col-12 mb-3\">\n                  <div class=\"form-check\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"checkbox\"\n                      id=\"resetPassword\"\n                      v-model=\"editUserForm.resetPassword\"\n                    >\n                    <label class=\"form-check-label\" for=\"resetPassword\">\n                      Reset user password\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <div v-if=\"editUserForm.resetPassword\" class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editNewPassword\" class=\"form-label\">New Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"editNewPassword\"\n                    v-model=\"editUserForm.newPassword\"\n                    :class=\"{ 'is-invalid': editFormErrors.newPassword }\"\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"editFormErrors.newPassword\" class=\"invalid-feedback\">\n                    {{ editFormErrors.newPassword }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editConfirmPassword\" class=\"form-label\">Confirm Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"editConfirmPassword\"\n                    v-model=\"editUserForm.confirmPassword\"\n                    :class=\"{ 'is-invalid': editFormErrors.confirmPassword }\"\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"editFormErrors.confirmPassword\" class=\"invalid-feedback\">\n                    {{ editFormErrors.confirmPassword }}\n                  </div>\n                </div>\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitEditUser\" :disabled=\"editUserLoading\">\n              <span v-if=\"editUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-save me-2\"></i>\n              {{ editUserLoading ? 'Updating...' : 'Update User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- View User Modal -->\n    <div class=\"modal fade\" id=\"viewUserModal\" tabindex=\"-1\" aria-labelledby=\"viewUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"viewUserModalLabel\">\n              <i class=\"fas fa-user me-2\"></i>\n              User Details\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\" v-if=\"viewUserData\">\n            <div class=\"row\">\n              <div class=\"col-md-4 text-center mb-4\">\n                <div class=\"user-avatar-large mx-auto mb-3\">\n                  <img v-if=\"viewUserData.profile_picture\" :src=\"viewUserData.profile_picture\" :alt=\"viewUserData.full_name\" class=\"rounded-circle\">\n                  <div v-else class=\"avatar-placeholder-large rounded-circle\">\n                    {{ getInitials(viewUserData.full_name) }}\n                  </div>\n                </div>\n                <h5>{{ viewUserData.full_name }}</h5>\n                <span class=\"badge\" :class=\"getStatusBadgeClass(viewUserData.status)\">\n                  {{ formatStatus(viewUserData.status) }}\n                </span>\n                <div class=\"mt-2\">\n                  <span class=\"badge\" :class=\"viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                    {{ viewUserData.type === 'admin' ? 'Administrator' : 'Client' }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"col-md-8\">\n                <!-- Basic Information -->\n                <h6 class=\"text-primary mb-3\">\n                  <i class=\"fas fa-user me-2\"></i>\n                  Basic Information\n                </h6>\n                <div class=\"row mb-2\">\n                  <div class=\"col-sm-4\"><strong>Username:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.username }}</div>\n                </div>\n                <div class=\"row mb-2\">\n                  <div class=\"col-sm-4\"><strong>Email:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.email || 'Not provided' }}</div>\n                </div>\n                <div class=\"row mb-2\">\n                  <div class=\"col-sm-4\"><strong>Phone:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.phone_number || 'Not provided' }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Status:</strong></div>\n                  <div class=\"col-sm-8\">\n                    <span class=\"badge\" :class=\"getStatusBadgeClass(viewUserData.status)\">\n                      {{ formatStatus(viewUserData.status) }}\n                    </span>\n                  </div>\n                </div>\n\n                <!-- Admin-specific Information -->\n                <div v-if=\"viewUserData.type === 'admin'\">\n                  <h6 class=\"text-primary mb-3\">\n                    <i class=\"fas fa-user-shield me-2\"></i>\n                    Administrator Details\n                  </h6>\n                  <div class=\"row mb-2\">\n                    <div class=\"col-sm-4\"><strong>Employee ID:</strong></div>\n                    <div class=\"col-sm-8\">{{ viewUserData.employee_id || 'Not assigned' }}</div>\n                  </div>\n                  <div class=\"row mb-2\">\n                    <div class=\"col-sm-4\"><strong>Position:</strong></div>\n                    <div class=\"col-sm-8\">{{ viewUserData.position || 'Not specified' }}</div>\n                  </div>\n                  <div class=\"row mb-2\">\n                    <div class=\"col-sm-4\"><strong>Department:</strong></div>\n                    <div class=\"col-sm-8\">{{ viewUserData.department || 'Not specified' }}</div>\n                  </div>\n                  <div class=\"row mb-3\">\n                    <div class=\"col-sm-4\"><strong>Hire Date:</strong></div>\n                    <div class=\"col-sm-8\">{{ viewUserData.hire_date ? formatDate(viewUserData.hire_date) : 'Not specified' }}</div>\n                  </div>\n                </div>\n\n                <!-- Client-specific Information -->\n                <div v-if=\"viewUserData.type === 'client'\">\n                  <h6 class=\"text-info mb-3\">\n                    <i class=\"fas fa-user me-2\"></i>\n                    Client Details\n                  </h6>\n                  <div class=\"row mb-2\">\n                    <div class=\"col-sm-4\"><strong>Birth Date:</strong></div>\n                    <div class=\"col-sm-8\">{{ viewUserData.birth_date ? formatDate(viewUserData.birth_date) : 'Not provided' }}</div>\n                  </div>\n                  <div class=\"row mb-2\">\n                    <div class=\"col-sm-4\"><strong>Gender:</strong></div>\n                    <div class=\"col-sm-8\">{{ viewUserData.gender ? viewUserData.gender.charAt(0).toUpperCase() + viewUserData.gender.slice(1) : 'Not specified' }}</div>\n                  </div>\n                  <div class=\"row mb-2\">\n                    <div class=\"col-sm-4\"><strong>Nationality:</strong></div>\n                    <div class=\"col-sm-8\">{{ viewUserData.nationality || 'Not specified' }}</div>\n                  </div>\n                  <div class=\"row mb-3\">\n                    <div class=\"col-sm-4\"><strong>Address:</strong></div>\n                    <div class=\"col-sm-8\">{{ getFullAddress(viewUserData) }}</div>\n                  </div>\n                  <div v-if=\"viewUserData.years_of_residency || viewUserData.months_of_residency\" class=\"row mb-3\">\n                    <div class=\"col-sm-4\"><strong>Residency:</strong></div>\n                    <div class=\"col-sm-8\">\n                      {{ viewUserData.years_of_residency || 0 }} years, {{ viewUserData.months_of_residency || 0 }} months\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Account Information -->\n                <h6 class=\"text-secondary mb-3\">\n                  <i class=\"fas fa-clock me-2\"></i>\n                  Account Information\n                </h6>\n                <div class=\"row mb-2\">\n                  <div class=\"col-sm-4\"><strong>Registered:</strong></div>\n                  <div class=\"col-sm-8\">{{ formatDate(viewUserData.created_at) }}</div>\n                </div>\n                <div class=\"row mb-2\">\n                  <div class=\"col-sm-4\"><strong>Last Updated:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.updated_at ? formatDate(viewUserData.updated_at) : 'Never' }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Last Login:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.last_login ? formatDate(viewUserData.last_login) : 'Never' }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Close</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"editUser(viewUserData)\" data-bs-dismiss=\"modal\">\n              <i class=\"fas fa-edit me-2\"></i>\n              Edit User\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\nimport { Modal } from 'bootstrap';\n\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      },\n\n      // Modal data\n      viewUserData: null,\n      addUserLoading: false,\n      editUserLoading: false,\n\n      // Form validation errors\n      formErrors: {},\n      editFormErrors: {},\n\n      // Add user form\n      addUserForm: {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null\n      },\n\n      // Edit user form\n      editUserForm: {\n        id: null,\n        username: '',\n        email: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        status: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null,\n        // Password reset fields\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      },\n\n      // Available options\n      genderOptions: [\n        { value: 'male', label: 'Male' },\n        { value: 'female', label: 'Female' }\n      ],\n\n      statusOptions: [\n        { value: 'active', label: 'Active' },\n        { value: 'inactive', label: 'Inactive' },\n        { value: 'suspended', label: 'Suspended' },\n        { value: 'pending_verification', label: 'Pending Verification' }\n      ]\n    };\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Make bootstrap available globally for this component\n    this.$bootstrap = { Modal };\n\n    // Debug authentication\n    const adminToken = localStorage.getItem('adminToken');\n    const adminData = localStorage.getItem('adminData');\n\n    console.log('🔍 Admin Authentication Debug:');\n    console.log('  Admin Token:', adminToken ? 'EXISTS' : 'NOT FOUND');\n    console.log('  Admin Data:', adminData ? 'EXISTS' : 'NOT FOUND');\n\n    if (adminData) {\n      try {\n        const data = JSON.parse(adminData);\n        console.log('  Username:', data.username);\n        console.log('  Role:', data.role);\n        console.log('  Status:', data.status);\n      } catch (e) {\n        console.error('  Error parsing admin data:', e);\n      }\n    }\n\n    if (!adminToken) {\n      this.showToast('error', 'You are not logged in as admin. Please login first.');\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUserStats();\n    await this.loadUsers();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load user statistics\n    async loadUserStats() {\n      try {\n        const response = await userManagementService.getUserStats();\n        if (response.success) {\n          this.userStats = response.data;\n        } else {\n          this.calculateStats();\n        }\n      } catch (error) {\n        console.error('Failed to load user statistics:', error);\n        this.calculateStats();\n      }\n    },\n\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50,\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true :\n                     this.filterStatus === 'inactive' ? false : undefined\n        };\n\n        const response = await userManagementService.getUsers(params);\n\n        if (response.success) {\n          this.users = response.data.users.map(user =>\n            userManagementService.formatUserData(user)\n          );\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.showToast('error', error.message || 'Failed to load users');\n        this.users = [];\n        this.filteredUsers = [];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user =>\n          user.full_name.toLowerCase().includes(query) ||\n          user.email.toLowerCase().includes(query) ||\n          user.username.toLowerCase().includes(query)\n        );\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    // User actions\n\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n\n          const statusText = newStatus === 'active' ? 'activated' : 'suspended';\n          this.showToast('success', `User ${user.full_name} has been ${statusText}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to update user status. Please try again.';\n        this.showToast('error', errorMessage);\n      }\n    },\n\n    async deleteUser(user) {\n      const confirmMessage = `Are you sure you want to delete user \"${user.full_name}\"?\\n\\nThis will:\\n- Deactivate the user account\\n- Prevent future logins\\n- Preserve data for audit purposes\\n\\nThis action can be reversed by reactivating the user.`;\n\n      if (!confirm(confirmMessage)) {\n        return;\n      }\n\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n\n        if (response.success) {\n          this.showToast('success', `User ${user.full_name} has been deleted successfully.`);\n\n          // Reload data to reflect changes\n          await this.loadUsers();\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to delete user. Please try again.';\n        this.showToast('error', errorMessage);\n      }\n    },\n\n    // Helper methods\n    showToast(type, message) {\n      if (this.$toast?.[type]) {\n        this.$toast[type](message);\n      } else {\n        console.log(`${type.toUpperCase()}: ${message}`);\n        if (type === 'error') alert(`Error: ${message}`);\n        else if (type === 'success') alert(`Success: ${message}`);\n      }\n    },\n\n    closeModal(modalId) {\n      try {\n        const modal = Modal.getInstance(document.getElementById(modalId));\n        if (modal) modal.hide();\n      } catch (error) {\n        console.error('Error closing modal:', error);\n      }\n    },\n\n    handleFormError(error, errorField) {\n      console.error('Form error:', error);\n\n      // Handle server validation errors\n      if (error.response?.data?.details) {\n        const serverErrors = {};\n        error.response.data.details.forEach(detail => {\n          if (detail.path) serverErrors[detail.path] = detail.msg;\n        });\n        this[errorField] = { ...this[errorField], ...serverErrors };\n      }\n\n      const errorMessage = error.response?.data?.message || error.message || 'Operation failed';\n      this.showToast('error', errorMessage);\n    },\n\n\n\n    // Modal methods\n    showAddUserModal() {\n      this.resetAddUserForm();\n      try {\n        const modalElement = document.getElementById('addUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing add user modal:', error);\n        this.$toast?.error?.('Failed to open add user modal');\n      }\n    },\n\n    resetAddUserForm() {\n      Object.assign(this.addUserForm, {\n        username: '', email: '', password: '', first_name: '', middle_name: '', last_name: '',\n        suffix: '', role: '', phone_number: '', position: '', department: '', employee_id: '',\n        hire_date: '', birth_date: '', gender: '', civil_status_id: 1, nationality: 'Filipino',\n        house_number: '', street: '', subdivision: '', barangay: '', city_municipality: '',\n        province: '', postal_code: '', years_of_residency: null, months_of_residency: null\n      });\n      this.formErrors = {};\n    },\n\n    clearRoleSpecificFields() {\n      const form = this.addUserForm;\n      if (form.role === 'admin') {\n        // Clear client fields\n        Object.assign(form, {\n          birth_date: '', gender: '', civil_status_id: 1, nationality: 'Filipino',\n          house_number: '', street: '', subdivision: '', barangay: '',\n          city_municipality: '', province: '', postal_code: '',\n          years_of_residency: null, months_of_residency: null\n        });\n      } else if (form.role === 'client') {\n        // Clear admin fields\n        Object.assign(form, {\n          position: '', department: '', employee_id: '', hire_date: ''\n        });\n      }\n    },\n\n    validateAddUserForm() {\n      const errors = {};\n      const form = this.addUserForm;\n\n      // Basic validation\n      if (!form.username?.length || form.username.length < 3) errors.username = 'Username must be at least 3 characters long';\n      if (!form.password?.length || form.password.length < 6) errors.password = 'Password must be at least 6 characters long';\n      if (!form.first_name?.trim() || form.first_name.trim().length < 2) errors.first_name = 'First name must be at least 2 characters long';\n      if (!form.last_name?.trim() || form.last_name.trim().length < 2) errors.last_name = 'Last name must be at least 2 characters long';\n      if (!form.role) errors.role = 'Please select a user type';\n      if (!form.phone_number?.trim() || form.phone_number.trim().length < 10) errors.phone_number = 'Please provide a valid phone number';\n      if (form.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(form.email)) errors.email = 'Please provide a valid email address';\n\n      // Client-specific validation\n      if (form.role === 'client') {\n        if (!form.birth_date) errors.birth_date = 'Birth date is required for clients';\n        if (!form.gender) errors.gender = 'Gender is required for clients';\n        if (!form.barangay?.trim()) errors.barangay = 'Barangay is required for clients';\n        if (!form.city_municipality?.trim()) errors.city_municipality = 'City/Municipality is required for clients';\n        if (!form.province?.trim()) errors.province = 'Province is required for clients';\n      }\n\n      this.formErrors = errors;\n      return Object.keys(errors).length === 0;\n    },\n\n    validateEditUserForm() {\n      const errors = {};\n      const form = this.editUserForm;\n\n      // Basic validation\n      if (!form.username?.length || form.username.length < 3) errors.username = 'Username must be at least 3 characters long';\n      if (!form.first_name?.trim() || form.first_name.trim().length < 2) errors.first_name = 'First name must be at least 2 characters long';\n      if (!form.last_name?.trim() || form.last_name.trim().length < 2) errors.last_name = 'Last name must be at least 2 characters long';\n      if (!form.phone_number?.trim() || form.phone_number.trim().length < 10) errors.phone_number = 'Please provide a valid phone number';\n      if (form.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(form.email)) errors.email = 'Please provide a valid email address';\n\n      // Password reset validation\n      if (form.resetPassword) {\n        if (!form.newPassword?.length || form.newPassword.length < 6) errors.newPassword = 'New password must be at least 6 characters long';\n        if (form.newPassword !== form.confirmPassword) errors.confirmPassword = 'Passwords do not match';\n      }\n\n      this.editFormErrors = errors;\n      return Object.keys(errors).length === 0;\n    },\n\n    getFullAddress(user) {\n      const parts = [];\n      if (user.house_number) parts.push(user.house_number);\n      if (user.street) parts.push(user.street);\n      if (user.subdivision) parts.push(user.subdivision);\n      if (user.barangay) parts.push(user.barangay);\n      if (user.city_municipality) parts.push(user.city_municipality);\n      if (user.province) parts.push(user.province);\n      return parts.join(', ') || 'No address provided';\n    },\n\n    async submitAddUser() {\n      if (!this.validateAddUserForm()) {\n        this.showToast('error', 'Please fix the validation errors before submitting.');\n        return;\n      }\n\n      this.addUserLoading = true;\n      try {\n        const userData = { ...this.addUserForm };\n\n        // Convert numeric fields\n        ['years_of_residency', 'months_of_residency', 'civil_status_id'].forEach(field => {\n          if (userData[field]) userData[field] = parseInt(userData[field]);\n        });\n\n        const response = await userManagementService.createUser(userData);\n\n        if (response.success) {\n          this.showToast('success', 'User created successfully');\n          this.closeModal('addUserModal');\n          this.resetAddUserForm();\n          await Promise.all([this.loadUsers(), this.loadUserStats()]);\n        } else {\n          throw new Error(response.message || 'Failed to create user');\n        }\n      } catch (error) {\n        this.handleFormError(error, 'formErrors');\n      } finally {\n        this.addUserLoading = false;\n      }\n    },\n\n    editUser(user) {\n      this.editUserForm = {\n        id: user.id,\n        username: user.username,\n        email: user.email || '',\n        first_name: user.first_name || '',\n        middle_name: user.middle_name || '',\n        last_name: user.last_name || '',\n        suffix: user.suffix || '',\n        role: user.type,\n        status: user.status,\n        phone_number: user.phone_number || '',\n        position: user.position || '',\n        department: user.department || '',\n        employee_id: user.employee_id || '',\n        hire_date: user.hire_date ? user.hire_date.split('T')[0] : '',\n        birth_date: user.birth_date ? user.birth_date.split('T')[0] : '',\n        gender: user.gender || '',\n        civil_status_id: user.civil_status_id || 1,\n        nationality: user.nationality || 'Filipino',\n        house_number: user.house_number || '',\n        street: user.street || '',\n        subdivision: user.subdivision || '',\n        barangay: user.barangay || '',\n        city_municipality: user.city_municipality || '',\n        province: user.province || '',\n        postal_code: user.postal_code || '',\n        years_of_residency: user.years_of_residency || null,\n        months_of_residency: user.months_of_residency || null,\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      };\n\n      this.editFormErrors = {};\n\n      try {\n        const modal = new Modal(document.getElementById('editUserModal'));\n        modal.show();\n      } catch (error) {\n        this.showToast('error', 'Failed to open edit user modal');\n      }\n    },\n\n    async submitEditUser() {\n      if (!this.validateEditUserForm()) {\n        this.showToast('error', 'Please fix the validation errors before submitting.');\n        return;\n      }\n\n      this.editUserLoading = true;\n      try {\n        const form = this.editUserForm;\n        const updateData = {\n          username: form.username,\n          email: form.email,\n          first_name: form.first_name,\n          middle_name: form.middle_name,\n          last_name: form.last_name,\n          suffix: form.suffix,\n          status: form.status,\n          phone_number: form.phone_number\n        };\n\n        // Add role-specific fields\n        if (form.role === 'admin') {\n          Object.assign(updateData, {\n            position: form.position,\n            department: form.department,\n            employee_id: form.employee_id,\n            hire_date: form.hire_date\n          });\n        }\n\n        // Add password if resetting\n        if (form.resetPassword && form.newPassword) {\n          updateData.password = form.newPassword;\n        }\n\n        const response = await userManagementService.updateUser(form.id, updateData);\n\n        if (response.success) {\n          this.showToast('success', 'User updated successfully');\n          this.closeModal('editUserModal');\n          await Promise.all([this.loadUsers(), this.loadUserStats()]);\n        } else {\n          throw new Error(response.message || 'Failed to update user');\n        }\n      } catch (error) {\n        this.handleFormError(error, 'editFormErrors');\n      } finally {\n        this.editUserLoading = false;\n      }\n    },\n\n    async viewUser(user) {\n      try {\n        const response = await userManagementService.getUser(user.id);\n        if (response.success) {\n          this.viewUserData = userManagementService.formatUserData(response.data);\n          const modal = new Modal(document.getElementById('viewUserModal'));\n          modal.show();\n        } else {\n          throw new Error(response.message || 'Failed to load user details');\n        }\n      } catch (error) {\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to load user details';\n        this.showToast('error', errorMessage);\n      }\n    },\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* User avatar styles */\n.user-avatar {\n  width: 40px;\n  height: 40px;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-placeholder {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  border-radius: 50%;\n}\n\n.user-avatar-large {\n  width: 80px;\n  height: 80px;\n}\n\n.avatar-placeholder-large {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 24px;\n  border-radius: 50%;\n}\n\n/* Modal styles */\n.modal-lg {\n  max-width: 800px;\n}\n\n/* Form styles */\n.form-label {\n  font-weight: 600;\n  color: #495057;\n}\n\n.form-control:focus,\n.form-select:focus {\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n/* Loading states */\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Table improvements */\n.table th {\n  border-top: none;\n  font-weight: 600;\n  color: #5a5c69;\n  font-size: 0.875rem;\n}\n\n.table td {\n  vertical-align: middle;\n  font-size: 0.875rem;\n}\n\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 123, 255, 0.05);\n}\n\n/* Button group improvements */\n.btn-group-sm .btn {\n  padding: 0.375rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n/* Search and filter improvements */\n.form-select-sm {\n  font-size: 0.875rem;\n  padding: 0.375rem 0.75rem;\n}\n\n/* Pagination improvements */\n.pagination-sm .page-link {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n}\n\n/* Badge improvements */\n.badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 0.5rem;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .table-responsive {\n    font-size: 0.8rem;\n  }\n\n  .btn-group-sm .btn {\n    padding: 0.25rem 0.375rem;\n    font-size: 0.7rem;\n  }\n\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n  }\n\n  .avatar-placeholder {\n    width: 32px;\n    height: 32px;\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    gap: 0.5rem !important;\n  }\n\n  .btn-group {\n    flex-direction: column;\n  }\n\n  .btn-group .btn {\n    border-radius: 0.375rem !important;\n    margin-bottom: 0.25rem;\n  }\n}\n</style>\n"], "mappings": ";;;;;AAmoCA,OAAOA,WAAU,MAAO,mBAAmB;AAC3C,OAAOC,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,gBAAe,MAAO,6BAA6B;AAC1D,OAAOC,qBAAoB,MAAO,kCAAkC;AACpE,SAASC,KAAI,QAAS,WAAW;AAEjC,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVN,WAAW;IACXC;EACF,CAAC;EAEDM,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACf;MACAC,KAAK,EAAE,EAAE;MACTC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,MAAM,EAAE;MACV,CAAC;MAED;MACAC,YAAY,EAAE,IAAI;MAClBC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,KAAK;MAEtB;MACAC,UAAU,EAAE,CAAC,CAAC;MACdC,cAAc,EAAE,CAAC,CAAC;MAElB;MACAC,WAAW,EAAE;QACXC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE,EAAE;QAChB;QACAC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACb;QACAC,UAAU,EAAE,EAAE;QACdC,MAAM,EAAE,EAAE;QACVC,eAAe,EAAE,CAAC;QAClBC,WAAW,EAAE,UAAU;QACvBC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,EAAE;QACVC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE;MACvB,CAAC;MAED;MACAC,YAAY,EAAE;QACZC,EAAE,EAAE,IAAI;QACR3B,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTE,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRqB,MAAM,EAAE,EAAE;QACVpB,YAAY,EAAE,EAAE;QAChB;QACAC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACb;QACAC,UAAU,EAAE,EAAE;QACdC,MAAM,EAAE,EAAE;QACVC,eAAe,EAAE,CAAC;QAClBC,WAAW,EAAE,UAAU;QACvBC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,EAAE;QACVC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE,IAAI;QACzB;QACAI,aAAa,EAAE,KAAK;QACpBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC;MAED;MACAC,aAAa,EAAE,CACb;QAAEC,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAO,CAAC,EAChC;QAAED,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAS,EACpC;MAEDC,aAAa,EAAE,CACb;QAAEF,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAS,CAAC,EACpC;QAAED,KAAK,EAAE,UAAU;QAAEC,KAAK,EAAE;MAAW,CAAC,EACxC;QAAED,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAY,CAAC,EAC1C;QAAED,KAAK,EAAE,sBAAsB;QAAEC,KAAK,EAAE;MAAuB;IAEnE,CAAC;EACH,CAAC;EAEDE,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;MAC7B,IAAIA,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,OAAO;MACjD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,sBAAsB,CAAC,EAAE,OAAO,UAAU;MAC5D,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,OAAO,WAAW;IACpB,CAAC;IAEDC,cAAcA,CAAA,EAAG;MACf,MAAMC,KAAI,GAAI,CAAC,IAAI,CAACxD,WAAU,GAAI,CAAC,IAAI,IAAI,CAACC,YAAY;MACxD,MAAMwD,GAAE,GAAID,KAAI,GAAI,IAAI,CAACvD,YAAY;MACrC,OAAO,IAAI,CAACL,aAAa,CAAC8D,KAAK,CAACF,KAAK,EAAEC,GAAG,CAAC;IAC7C,CAAC;IAEDE,UAAUA,CAAA,EAAG;MACX,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACjE,aAAa,CAACkE,MAAK,GAAI,IAAI,CAAC7D,YAAY,CAAC;IACjE,CAAC;IAED8D,YAAYA,CAAA,EAAG;MACb,MAAMC,KAAI,GAAI,EAAE;MAChB,MAAMR,KAAI,GAAII,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjE,WAAU,GAAI,CAAC,CAAC;MAC/C,MAAMyD,GAAE,GAAIG,IAAI,CAACM,GAAG,CAAC,IAAI,CAACP,UAAU,EAAE,IAAI,CAAC3D,WAAU,GAAI,CAAC,CAAC;MAE3D,KAAK,IAAImE,CAAA,GAAIX,KAAK,EAAEW,CAAA,IAAKV,GAAG,EAAEU,CAAC,EAAE,EAAE;QACjCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;MACf;MACA,OAAOH,KAAK;IACd;EACF,CAAC;EAED,MAAMK,OAAOA,CAAA,EAAG;IACd;IACA,IAAI,CAACpF,gBAAgB,CAACqF,UAAU,CAAC,CAAC,EAAE;MAClC,IAAI,CAACC,OAAO,CAACH,IAAI,CAAC,cAAc,CAAC;MACjC;IACF;;IAEA;IACA,IAAI,CAACI,YAAY,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACC,UAAS,GAAI;MAAEtF;IAAM,CAAC;;IAE3B;IACA,MAAMuF,UAAS,GAAIC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,MAAMlF,SAAQ,GAAIiF,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAEnDC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7CD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,UAAS,GAAI,QAAO,GAAI,WAAW,CAAC;IAClEG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEpF,SAAQ,GAAI,QAAO,GAAI,WAAW,CAAC;IAEhE,IAAIA,SAAS,EAAE;MACb,IAAI;QACF,MAAMJ,IAAG,GAAIyF,IAAI,CAACC,KAAK,CAACtF,SAAS,CAAC;QAClCmF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAExF,IAAI,CAACwB,QAAQ,CAAC;QACzC+D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAExF,IAAI,CAAC+B,IAAI,CAAC;QACjCwD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAExF,IAAI,CAACoD,MAAM,CAAC;MACvC,EAAE,OAAOuC,CAAC,EAAE;QACVJ,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAED,CAAC,CAAC;MACjD;IACF;IAEA,IAAI,CAACP,UAAU,EAAE;MACf,IAAI,CAACS,SAAS,CAAC,OAAO,EAAE,qDAAqD,CAAC;MAC9E,IAAI,CAACZ,OAAO,CAACH,IAAI,CAAC,cAAc,CAAC;MACjC;IACF;;IAEA;IACA,MAAM,IAAI,CAACgB,gBAAgB,CAAC,CAAC;IAC7B,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC;IAC1B,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC;EACxB,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,YAAY,CAAC;IACzD;EACF,CAAC;EAEDG,OAAO,EAAE;IACP;IACAnB,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC/E,QAAO,GAAIgG,MAAM,CAACG,UAAS,IAAK,GAAG;;MAExC;MACA,IAAI,CAAC,IAAI,CAACnG,QAAQ,EAAE;QAClB,MAAMoG,KAAI,GAAIlB,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAACrF,gBAAe,GAAIsG,KAAI,GAAId,IAAI,CAACC,KAAK,CAACa,KAAK,IAAI,KAAK;MAC3D,OAAO;QACL,IAAI,CAACtG,gBAAe,GAAI,IAAI,EAAE;MAChC;;MAEA;MACA,IAAI,CAACiG,YAAW,GAAI,MAAM;QACxB,MAAMM,SAAQ,GAAI,IAAI,CAACrG,QAAQ;QAC/B,IAAI,CAACA,QAAO,GAAIgG,MAAM,CAACG,UAAS,IAAK,GAAG;QAExC,IAAI,IAAI,CAACnG,QAAO,IAAK,CAACqG,SAAS,EAAE;UAC/B,IAAI,CAACvG,gBAAe,GAAI,IAAI,EAAE;QAChC,OAAO,IAAI,CAAC,IAAI,CAACE,QAAO,IAAKqG,SAAS,EAAE;UACtC;UACA,MAAMD,KAAI,GAAIlB,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;UAC3D,IAAI,CAACrF,gBAAe,GAAIsG,KAAI,GAAId,IAAI,CAACC,KAAK,CAACa,KAAK,IAAI,KAAK;QAC3D;MACF,CAAC;MACDJ,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACP,YAAY,CAAC;IACtD,CAAC;IAED;IACAQ,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACzG,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;MAC9CoF,YAAY,CAACsB,OAAO,CAAC,uBAAuB,EAAElB,IAAI,CAACmB,SAAS,CAAC,IAAI,CAAC3G,gBAAgB,CAAC,CAAC;IACtF,CAAC;IAED;IACA4G,gBAAgBA,CAACC,IAAI,EAAE;MACrB,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,cAAc;QACvB,UAAU,EAAE,iBAAiB;QAC7B,SAAS,EAAE,gBAAgB;QAC3B,UAAU,EAAE,iBAAiB;QAC7B,UAAU,EAAE,sBAAsB;QAClC,SAAS,EAAE;MACb,CAAC;;MAED;MACA,IAAI,IAAI,CAAC5G,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;MAEA,IAAI8G,MAAM,CAACD,IAAI,CAAC,EAAE;QAChB,IAAI,CAAC7B,OAAO,CAACH,IAAI,CAACiC,MAAM,CAACD,IAAI,CAAC,CAAC;MACjC;IACF,CAAC;IAED;IACAE,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAAC9G,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED;IACA+G,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAIA,MAAK,KAAM,SAAS,EAAE;QACxB,IAAI,CAACjC,OAAO,CAACH,IAAI,CAAC,gBAAgB,CAAC;MACrC,OAAO,IAAIoC,MAAK,KAAM,UAAU,EAAE;QAChC,IAAI,CAACjC,OAAO,CAACH,IAAI,CAAC,iBAAiB,CAAC;MACtC;MACA,IAAI,CAAC5E,gBAAe,GAAI,KAAK;IAC/B,CAAC;IAED;IACAiH,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAChH,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;IACF,CAAC;IAED;IACAmH,YAAYA,CAAA,EAAG;MACbzH,gBAAgB,CAAC0H,MAAM,CAAC,CAAC;MACzB,IAAI,CAACpC,OAAO,CAACH,IAAI,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;IACA,MAAMgB,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAMwB,QAAO,GAAI,MAAM3H,gBAAgB,CAAC4H,UAAU,CAAC,CAAC;QACpD,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACpH,SAAQ,GAAIkH,QAAQ,CAACtH,IAAI;QAChC;MACF,EAAE,OAAO4F,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACxF,SAAQ,GAAIT,gBAAgB,CAAC8H,YAAY,CAAC,CAAC;MAClD;IACF,CAAC;IAED;IACA,MAAM1B,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,MAAMuB,QAAO,GAAI,MAAM1H,qBAAqB,CAAC8H,YAAY,CAAC,CAAC;QAC3D,IAAIJ,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC3G,SAAQ,GAAIyG,QAAQ,CAACtH,IAAI;QAChC,OAAO;UACL,IAAI,CAAC2H,cAAc,CAAC,CAAC;QACvB;MACF,EAAE,OAAO/B,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAAC+B,cAAc,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACA,MAAM3B,SAASA,CAAA,EAAG;MAChB,IAAI,CAACpF,OAAM,GAAI,IAAI;MACnB,IAAI;QACF,MAAMgH,MAAK,GAAI;UACbC,IAAI,EAAE,IAAI,CAACnH,WAAW;UACtBoH,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,IAAI,CAACxH,WAAU,IAAKyH,SAAS;UACrCjG,IAAI,EAAE,IAAI,CAACtB,UAAS,IAAKuH,SAAS;UAClCC,SAAS,EAAE,IAAI,CAACzH,YAAW,KAAM,QAAO,GAAI,IAAG,GACpC,IAAI,CAACA,YAAW,KAAM,UAAS,GAAI,KAAI,GAAIwH;QACxD,CAAC;QAED,MAAMV,QAAO,GAAI,MAAM1H,qBAAqB,CAACsI,QAAQ,CAACN,MAAM,CAAC;QAE7D,IAAIN,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACnH,KAAI,GAAIiH,QAAQ,CAACtH,IAAI,CAACK,KAAK,CAAC8H,GAAG,CAACC,IAAG,IACtCxI,qBAAqB,CAACyI,cAAc,CAACD,IAAI,CAC3C,CAAC;UACD,IAAI,CAAC9H,aAAY,GAAI,CAAC,GAAG,IAAI,CAACD,KAAK,CAAC;UACpC,IAAI,CAACsH,cAAc,CAAC,CAAC;QACvB,OAAO;UACL,MAAM,IAAIW,KAAK,CAAChB,QAAQ,CAACiB,OAAM,IAAK,sBAAsB,CAAC;QAC7D;MACF,EAAE,OAAO3C,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACC,SAAS,CAAC,OAAO,EAAED,KAAK,CAAC2C,OAAM,IAAK,sBAAsB,CAAC;QAChE,IAAI,CAAClI,KAAI,GAAI,EAAE;QACf,IAAI,CAACC,aAAY,GAAI,EAAE;QACvB,IAAI,CAACqH,cAAc,CAAC,CAAC;MACvB,UAAU;QACR,IAAI,CAAC/G,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA+G,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC9G,SAAQ,GAAI;QACfC,KAAK,EAAE,IAAI,CAACT,KAAK,CAACmE,MAAM;QACxBzD,MAAM,EAAE,IAAI,CAACV,KAAK,CAACmI,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACrF,MAAK,KAAM,QAAQ,CAAC,CAACoB,MAAM;QAC5DxD,OAAO,EAAE,IAAI,CAACX,KAAK,CAACmI,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACrF,MAAK,KAAM,SAAS,CAAC,CAACoB,MAAM;QAC9DvD,MAAM,EAAE,IAAI,CAACZ,KAAK,CAACmI,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,IAAG,KAAM,OAAO,CAAC,CAAClE;MACrD,CAAC;IACH,CAAC;IAED;IACAmE,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACC,WAAW,CAAC,CAAC;IACpB,CAAC;IAED;IACAA,WAAWA,CAAA,EAAG;MACZ,IAAIC,QAAO,GAAI,CAAC,GAAG,IAAI,CAACxI,KAAK,CAAC;;MAE9B;MACA,IAAI,IAAI,CAACE,WAAW,EAAE;QACpB,MAAMuI,KAAI,GAAI,IAAI,CAACvI,WAAW,CAACwI,WAAW,CAAC,CAAC;QAC5CF,QAAO,GAAIA,QAAQ,CAACL,MAAM,CAACJ,IAAG,IAC5BA,IAAI,CAACY,SAAS,CAACD,WAAW,CAAC,CAAC,CAAC/E,QAAQ,CAAC8E,KAAK,KAC3CV,IAAI,CAAC3G,KAAK,CAACsH,WAAW,CAAC,CAAC,CAAC/E,QAAQ,CAAC8E,KAAK,KACvCV,IAAI,CAAC5G,QAAQ,CAACuH,WAAW,CAAC,CAAC,CAAC/E,QAAQ,CAAC8E,KAAK,CAC5C,CAAC;MACH;;MAEA;MACA,IAAI,IAAI,CAACtI,YAAY,EAAE;QACrBqI,QAAO,GAAIA,QAAQ,CAACL,MAAM,CAACJ,IAAG,IAAKA,IAAI,CAAChF,MAAK,KAAM,IAAI,CAAC5C,YAAY,CAAC;MACvE;;MAEA;MACA,IAAI,IAAI,CAACC,UAAU,EAAE;QACnBoI,QAAO,GAAIA,QAAQ,CAACL,MAAM,CAACJ,IAAG,IAAKA,IAAI,CAACM,IAAG,KAAM,IAAI,CAACjI,UAAU,CAAC;MACnE;MAEA,IAAI,CAACH,aAAY,GAAIuI,QAAQ;MAC7B,IAAI,CAACnI,WAAU,GAAI,CAAC,EAAE;IACxB,CAAC;IAED;IACAuI,UAAUA,CAACpB,IAAI,EAAE;MACf,IAAIA,IAAG,IAAK,KAAKA,IAAG,IAAK,IAAI,CAACxD,UAAU,EAAE;QACxC,IAAI,CAAC3D,WAAU,GAAImH,IAAI;MACzB;IACF,CAAC;IAED;IACAqB,WAAWA,CAACC,QAAQ,EAAE;MACpB,IAAI,CAACA,QAAQ,EAAE,OAAO,GAAG;MACzB,OAAOA,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACjB,GAAG,CAACrI,IAAG,IAAKA,IAAI,CAACuJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAACnF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED;IACAoF,mBAAmBA,CAACpG,MAAM,EAAE;MAC1B,MAAMqG,OAAM,GAAI;QACd,QAAQ,EAAE,YAAY;QACtB,UAAU,EAAE,cAAc;QAC1B,SAAS,EAAE,YAAY;QACvB,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,OAAO,CAACrG,MAAM,KAAK,cAAc;IAC1C,CAAC;IAED;IACAsG,YAAYA,CAACtG,MAAM,EAAE;MACnB,OAAOA,MAAM,CAACiG,MAAM,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,IAAInG,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;IACAuF,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC;IAED;;IAGA,MAAMC,gBAAgBA,CAAC/B,IAAI,EAAE;MAC3B,IAAI;QACF,MAAMgC,SAAQ,GAAIhC,IAAI,CAAChF,MAAK,KAAM,QAAO,GAAI,WAAU,GAAI,QAAQ;QACnE,MAAMiH,MAAK,GAAI,4BAA4B,IAAI,CAACjK,SAAS,EAAEuB,UAAS,IAAK,OAAO,EAAE;QAElF,MAAM2F,QAAO,GAAI,MAAM1H,qBAAqB,CAAC0K,gBAAgB,CAAClC,IAAI,CAACjF,EAAE,EAAEiH,SAAS,EAAEC,MAAM,CAAC;QAEzF,IAAI/C,QAAQ,CAACE,OAAO,EAAE;UACpB;UACAY,IAAI,CAAChF,MAAK,GAAIgH,SAAS;UACvB,IAAI,CAACzC,cAAc,CAAC,CAAC;UAErB,MAAM4C,UAAS,GAAIH,SAAQ,KAAM,QAAO,GAAI,WAAU,GAAI,WAAW;UACrE,IAAI,CAACvE,SAAS,CAAC,SAAS,EAAE,QAAQuC,IAAI,CAACY,SAAS,aAAauB,UAAU,GAAG,CAAC;QAC7E,OAAO;UACL,MAAM,IAAIjC,KAAK,CAAChB,QAAQ,CAACiB,OAAM,IAAK,8BAA8B,CAAC;QACrE;MACF,EAAE,OAAO3C,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,MAAM4E,YAAW,GAAI5E,KAAK,CAAC0B,QAAQ,EAAEtH,IAAI,EAAEuI,OAAM,IAAK3C,KAAK,CAAC2C,OAAM,IAAK,iDAAiD;QACxH,IAAI,CAAC1C,SAAS,CAAC,OAAO,EAAE2E,YAAY,CAAC;MACvC;IACF,CAAC;IAED,MAAMC,UAAUA,CAACrC,IAAI,EAAE;MACrB,MAAMsC,cAAa,GAAI,yCAAyCtC,IAAI,CAACY,SAAS,uKAAuK;MAErP,IAAI,CAAC2B,OAAO,CAACD,cAAc,CAAC,EAAE;QAC5B;MACF;MAEA,IAAI;QACF,MAAML,MAAK,GAAI,0BAA0B,IAAI,CAACjK,SAAS,EAAEuB,UAAS,IAAK,OAAO,EAAE;QAChF,MAAM2F,QAAO,GAAI,MAAM1H,qBAAqB,CAAC6K,UAAU,CAACrC,IAAI,CAACjF,EAAE,EAAEkH,MAAM,CAAC;QAExE,IAAI/C,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC3B,SAAS,CAAC,SAAS,EAAE,QAAQuC,IAAI,CAACY,SAAS,iCAAiC,CAAC;;UAElF;UACA,MAAM,IAAI,CAAChD,SAAS,CAAC,CAAC;UACtB,MAAM,IAAI,CAACD,aAAa,CAAC,CAAC;QAC5B,OAAO;UACL,MAAM,IAAIuC,KAAK,CAAChB,QAAQ,CAACiB,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAO3C,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAM4E,YAAW,GAAI5E,KAAK,CAAC0B,QAAQ,EAAEtH,IAAI,EAAEuI,OAAM,IAAK3C,KAAK,CAAC2C,OAAM,IAAK,0CAA0C;QACjH,IAAI,CAAC1C,SAAS,CAAC,OAAO,EAAE2E,YAAY,CAAC;MACvC;IACF,CAAC;IAED;IACA3E,SAASA,CAAC6C,IAAI,EAAEH,OAAO,EAAE;MACvB,IAAI,IAAI,CAACqC,MAAM,GAAGlC,IAAI,CAAC,EAAE;QACvB,IAAI,CAACkC,MAAM,CAAClC,IAAI,CAAC,CAACH,OAAO,CAAC;MAC5B,OAAO;QACLhD,OAAO,CAACC,GAAG,CAAC,GAAGkD,IAAI,CAACa,WAAW,CAAC,CAAC,KAAKhB,OAAO,EAAE,CAAC;QAChD,IAAIG,IAAG,KAAM,OAAO,EAAEmC,KAAK,CAAC,UAAUtC,OAAO,EAAE,CAAC,MAC3C,IAAIG,IAAG,KAAM,SAAS,EAAEmC,KAAK,CAAC,YAAYtC,OAAO,EAAE,CAAC;MAC3D;IACF,CAAC;IAEDuC,UAAUA,CAACC,OAAO,EAAE;MAClB,IAAI;QACF,MAAMC,KAAI,GAAInL,KAAK,CAACoL,WAAW,CAACC,QAAQ,CAACC,cAAc,CAACJ,OAAO,CAAC,CAAC;QACjE,IAAIC,KAAK,EAAEA,KAAK,CAACI,IAAI,CAAC,CAAC;MACzB,EAAE,OAAOxF,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF,CAAC;IAEDyF,eAAeA,CAACzF,KAAK,EAAE0F,UAAU,EAAE;MACjC/F,OAAO,CAACK,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;;MAEnC;MACA,IAAIA,KAAK,CAAC0B,QAAQ,EAAEtH,IAAI,EAAEuL,OAAO,EAAE;QACjC,MAAMC,YAAW,GAAI,CAAC,CAAC;QACvB5F,KAAK,CAAC0B,QAAQ,CAACtH,IAAI,CAACuL,OAAO,CAACE,OAAO,CAACC,MAAK,IAAK;UAC5C,IAAIA,MAAM,CAAC5H,IAAI,EAAE0H,YAAY,CAACE,MAAM,CAAC5H,IAAI,IAAI4H,MAAM,CAACC,GAAG;QACzD,CAAC,CAAC;QACF,IAAI,CAACL,UAAU,IAAI;UAAE,GAAG,IAAI,CAACA,UAAU,CAAC;UAAE,GAAGE;QAAa,CAAC;MAC7D;MAEA,MAAMhB,YAAW,GAAI5E,KAAK,CAAC0B,QAAQ,EAAEtH,IAAI,EAAEuI,OAAM,IAAK3C,KAAK,CAAC2C,OAAM,IAAK,kBAAkB;MACzF,IAAI,CAAC1C,SAAS,CAAC,OAAO,EAAE2E,YAAY,CAAC;IACvC,CAAC;IAID;IACAoB,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI;QACF,MAAMC,YAAW,GAAIZ,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;QAC5D,IAAIW,YAAY,EAAE;UAChB,MAAMd,KAAI,GAAI,IAAInL,KAAK,CAACiM,YAAY,CAAC;UACrCd,KAAK,CAACe,IAAI,CAAC,CAAC;QACd;MACF,EAAE,OAAOnG,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACgF,MAAM,EAAEhF,KAAK,GAAG,+BAA+B,CAAC;MACvD;IACF,CAAC;IAEDiG,gBAAgBA,CAAA,EAAG;MACjBG,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC1K,WAAW,EAAE;QAC9BC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,UAAU,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,SAAS,EAAE,EAAE;QACrFC,MAAM,EAAE,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEC,YAAY,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,UAAU,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QACrFC,SAAS,EAAE,EAAE;QAAEC,UAAU,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,eAAe,EAAE,CAAC;QAAEC,WAAW,EAAE,UAAU;QACtFC,YAAY,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,iBAAiB,EAAE,EAAE;QAClFC,QAAQ,EAAE,EAAE;QAAEC,WAAW,EAAE,EAAE;QAAEC,kBAAkB,EAAE,IAAI;QAAEC,mBAAmB,EAAE;MAChF,CAAC,CAAC;MACF,IAAI,CAAC5B,UAAS,GAAI,CAAC,CAAC;IACtB,CAAC;IAED6K,uBAAuBA,CAAA,EAAG;MACxB,MAAMC,IAAG,GAAI,IAAI,CAAC5K,WAAW;MAC7B,IAAI4K,IAAI,CAACpK,IAAG,KAAM,OAAO,EAAE;QACzB;QACAiK,MAAM,CAACC,MAAM,CAACE,IAAI,EAAE;UAClB9J,UAAU,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,eAAe,EAAE,CAAC;UAAEC,WAAW,EAAE,UAAU;UACvEC,YAAY,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAC3DC,iBAAiB,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UACpDC,kBAAkB,EAAE,IAAI;UAAEC,mBAAmB,EAAE;QACjD,CAAC,CAAC;MACJ,OAAO,IAAIkJ,IAAI,CAACpK,IAAG,KAAM,QAAQ,EAAE;QACjC;QACAiK,MAAM,CAACC,MAAM,CAACE,IAAI,EAAE;UAClBlK,QAAQ,EAAE,EAAE;UAAEC,UAAU,EAAE,EAAE;UAAEC,WAAW,EAAE,EAAE;UAAEC,SAAS,EAAE;QAC5D,CAAC,CAAC;MACJ;IACF,CAAC;IAEDgK,mBAAmBA,CAAA,EAAG;MACpB,MAAMC,MAAK,GAAI,CAAC,CAAC;MACjB,MAAMF,IAAG,GAAI,IAAI,CAAC5K,WAAW;;MAE7B;MACA,IAAI,CAAC4K,IAAI,CAAC3K,QAAQ,EAAEgD,MAAK,IAAK2H,IAAI,CAAC3K,QAAQ,CAACgD,MAAK,GAAI,CAAC,EAAE6H,MAAM,CAAC7K,QAAO,GAAI,6CAA6C;MACvH,IAAI,CAAC2K,IAAI,CAACzK,QAAQ,EAAE8C,MAAK,IAAK2H,IAAI,CAACzK,QAAQ,CAAC8C,MAAK,GAAI,CAAC,EAAE6H,MAAM,CAAC3K,QAAO,GAAI,6CAA6C;MACvH,IAAI,CAACyK,IAAI,CAACxK,UAAU,EAAE2K,IAAI,CAAC,KAAKH,IAAI,CAACxK,UAAU,CAAC2K,IAAI,CAAC,CAAC,CAAC9H,MAAK,GAAI,CAAC,EAAE6H,MAAM,CAAC1K,UAAS,GAAI,+CAA+C;MACtI,IAAI,CAACwK,IAAI,CAACtK,SAAS,EAAEyK,IAAI,CAAC,KAAKH,IAAI,CAACtK,SAAS,CAACyK,IAAI,CAAC,CAAC,CAAC9H,MAAK,GAAI,CAAC,EAAE6H,MAAM,CAACxK,SAAQ,GAAI,8CAA8C;MAClI,IAAI,CAACsK,IAAI,CAACpK,IAAI,EAAEsK,MAAM,CAACtK,IAAG,GAAI,2BAA2B;MACzD,IAAI,CAACoK,IAAI,CAACnK,YAAY,EAAEsK,IAAI,CAAC,KAAKH,IAAI,CAACnK,YAAY,CAACsK,IAAI,CAAC,CAAC,CAAC9H,MAAK,GAAI,EAAE,EAAE6H,MAAM,CAACrK,YAAW,GAAI,qCAAqC;MACnI,IAAImK,IAAI,CAAC1K,KAAI,IAAK,CAAC,4BAA4B,CAAC8K,IAAI,CAACJ,IAAI,CAAC1K,KAAK,CAAC,EAAE4K,MAAM,CAAC5K,KAAI,GAAI,sCAAsC;;MAEvH;MACA,IAAI0K,IAAI,CAACpK,IAAG,KAAM,QAAQ,EAAE;QAC1B,IAAI,CAACoK,IAAI,CAAC9J,UAAU,EAAEgK,MAAM,CAAChK,UAAS,GAAI,oCAAoC;QAC9E,IAAI,CAAC8J,IAAI,CAAC7J,MAAM,EAAE+J,MAAM,CAAC/J,MAAK,GAAI,gCAAgC;QAClE,IAAI,CAAC6J,IAAI,CAACvJ,QAAQ,EAAE0J,IAAI,CAAC,CAAC,EAAED,MAAM,CAACzJ,QAAO,GAAI,kCAAkC;QAChF,IAAI,CAACuJ,IAAI,CAACtJ,iBAAiB,EAAEyJ,IAAI,CAAC,CAAC,EAAED,MAAM,CAACxJ,iBAAgB,GAAI,2CAA2C;QAC3G,IAAI,CAACsJ,IAAI,CAACrJ,QAAQ,EAAEwJ,IAAI,CAAC,CAAC,EAAED,MAAM,CAACvJ,QAAO,GAAI,kCAAkC;MAClF;MAEA,IAAI,CAACzB,UAAS,GAAIgL,MAAM;MACxB,OAAOL,MAAM,CAACQ,IAAI,CAACH,MAAM,CAAC,CAAC7H,MAAK,KAAM,CAAC;IACzC,CAAC;IAEDiI,oBAAoBA,CAAA,EAAG;MACrB,MAAMJ,MAAK,GAAI,CAAC,CAAC;MACjB,MAAMF,IAAG,GAAI,IAAI,CAACjJ,YAAY;;MAE9B;MACA,IAAI,CAACiJ,IAAI,CAAC3K,QAAQ,EAAEgD,MAAK,IAAK2H,IAAI,CAAC3K,QAAQ,CAACgD,MAAK,GAAI,CAAC,EAAE6H,MAAM,CAAC7K,QAAO,GAAI,6CAA6C;MACvH,IAAI,CAAC2K,IAAI,CAACxK,UAAU,EAAE2K,IAAI,CAAC,KAAKH,IAAI,CAACxK,UAAU,CAAC2K,IAAI,CAAC,CAAC,CAAC9H,MAAK,GAAI,CAAC,EAAE6H,MAAM,CAAC1K,UAAS,GAAI,+CAA+C;MACtI,IAAI,CAACwK,IAAI,CAACtK,SAAS,EAAEyK,IAAI,CAAC,KAAKH,IAAI,CAACtK,SAAS,CAACyK,IAAI,CAAC,CAAC,CAAC9H,MAAK,GAAI,CAAC,EAAE6H,MAAM,CAACxK,SAAQ,GAAI,8CAA8C;MAClI,IAAI,CAACsK,IAAI,CAACnK,YAAY,EAAEsK,IAAI,CAAC,KAAKH,IAAI,CAACnK,YAAY,CAACsK,IAAI,CAAC,CAAC,CAAC9H,MAAK,GAAI,EAAE,EAAE6H,MAAM,CAACrK,YAAW,GAAI,qCAAqC;MACnI,IAAImK,IAAI,CAAC1K,KAAI,IAAK,CAAC,4BAA4B,CAAC8K,IAAI,CAACJ,IAAI,CAAC1K,KAAK,CAAC,EAAE4K,MAAM,CAAC5K,KAAI,GAAI,sCAAsC;;MAEvH;MACA,IAAI0K,IAAI,CAAC9I,aAAa,EAAE;QACtB,IAAI,CAAC8I,IAAI,CAAC7I,WAAW,EAAEkB,MAAK,IAAK2H,IAAI,CAAC7I,WAAW,CAACkB,MAAK,GAAI,CAAC,EAAE6H,MAAM,CAAC/I,WAAU,GAAI,iDAAiD;QACpI,IAAI6I,IAAI,CAAC7I,WAAU,KAAM6I,IAAI,CAAC5I,eAAe,EAAE8I,MAAM,CAAC9I,eAAc,GAAI,wBAAwB;MAClG;MAEA,IAAI,CAACjC,cAAa,GAAI+K,MAAM;MAC5B,OAAOL,MAAM,CAACQ,IAAI,CAACH,MAAM,CAAC,CAAC7H,MAAK,KAAM,CAAC;IACzC,CAAC;IAEDkI,cAAcA,CAACtE,IAAI,EAAE;MACnB,MAAMuE,KAAI,GAAI,EAAE;MAChB,IAAIvE,IAAI,CAAC3F,YAAY,EAAEkK,KAAK,CAAC7H,IAAI,CAACsD,IAAI,CAAC3F,YAAY,CAAC;MACpD,IAAI2F,IAAI,CAAC1F,MAAM,EAAEiK,KAAK,CAAC7H,IAAI,CAACsD,IAAI,CAAC1F,MAAM,CAAC;MACxC,IAAI0F,IAAI,CAACzF,WAAW,EAAEgK,KAAK,CAAC7H,IAAI,CAACsD,IAAI,CAACzF,WAAW,CAAC;MAClD,IAAIyF,IAAI,CAACxF,QAAQ,EAAE+J,KAAK,CAAC7H,IAAI,CAACsD,IAAI,CAACxF,QAAQ,CAAC;MAC5C,IAAIwF,IAAI,CAACvF,iBAAiB,EAAE8J,KAAK,CAAC7H,IAAI,CAACsD,IAAI,CAACvF,iBAAiB,CAAC;MAC9D,IAAIuF,IAAI,CAACtF,QAAQ,EAAE6J,KAAK,CAAC7H,IAAI,CAACsD,IAAI,CAACtF,QAAQ,CAAC;MAC5C,OAAO6J,KAAK,CAACrD,IAAI,CAAC,IAAI,KAAK,qBAAqB;IAClD,CAAC;IAED,MAAMsD,aAAaA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAACR,mBAAmB,CAAC,CAAC,EAAE;QAC/B,IAAI,CAACvG,SAAS,CAAC,OAAO,EAAE,qDAAqD,CAAC;QAC9E;MACF;MAEA,IAAI,CAAC1E,cAAa,GAAI,IAAI;MAC1B,IAAI;QACF,MAAM0L,QAAO,GAAI;UAAE,GAAG,IAAI,CAACtL;QAAY,CAAC;;QAExC;QACA,CAAC,oBAAoB,EAAE,qBAAqB,EAAE,iBAAiB,CAAC,CAACkK,OAAO,CAACqB,KAAI,IAAK;UAChF,IAAID,QAAQ,CAACC,KAAK,CAAC,EAAED,QAAQ,CAACC,KAAK,IAAIC,QAAQ,CAACF,QAAQ,CAACC,KAAK,CAAC,CAAC;QAClE,CAAC,CAAC;QAEF,MAAMxF,QAAO,GAAI,MAAM1H,qBAAqB,CAACoN,UAAU,CAACH,QAAQ,CAAC;QAEjE,IAAIvF,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC3B,SAAS,CAAC,SAAS,EAAE,2BAA2B,CAAC;UACtD,IAAI,CAACiF,UAAU,CAAC,cAAc,CAAC;UAC/B,IAAI,CAACe,gBAAgB,CAAC,CAAC;UACvB,MAAMoB,OAAO,CAACC,GAAG,CAAC,CAAC,IAAI,CAAClH,SAAS,CAAC,CAAC,EAAE,IAAI,CAACD,aAAa,CAAC,CAAC,CAAC,CAAC;QAC7D,OAAO;UACL,MAAM,IAAIuC,KAAK,CAAChB,QAAQ,CAACiB,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAO3C,KAAK,EAAE;QACd,IAAI,CAACyF,eAAe,CAACzF,KAAK,EAAE,YAAY,CAAC;MAC3C,UAAU;QACR,IAAI,CAACzE,cAAa,GAAI,KAAK;MAC7B;IACF,CAAC;IAEDgM,QAAQA,CAAC/E,IAAI,EAAE;MACb,IAAI,CAAClF,YAAW,GAAI;QAClBC,EAAE,EAAEiF,IAAI,CAACjF,EAAE;QACX3B,QAAQ,EAAE4G,IAAI,CAAC5G,QAAQ;QACvBC,KAAK,EAAE2G,IAAI,CAAC3G,KAAI,IAAK,EAAE;QACvBE,UAAU,EAAEyG,IAAI,CAACzG,UAAS,IAAK,EAAE;QACjCC,WAAW,EAAEwG,IAAI,CAACxG,WAAU,IAAK,EAAE;QACnCC,SAAS,EAAEuG,IAAI,CAACvG,SAAQ,IAAK,EAAE;QAC/BC,MAAM,EAAEsG,IAAI,CAACtG,MAAK,IAAK,EAAE;QACzBC,IAAI,EAAEqG,IAAI,CAACM,IAAI;QACftF,MAAM,EAAEgF,IAAI,CAAChF,MAAM;QACnBpB,YAAY,EAAEoG,IAAI,CAACpG,YAAW,IAAK,EAAE;QACrCC,QAAQ,EAAEmG,IAAI,CAACnG,QAAO,IAAK,EAAE;QAC7BC,UAAU,EAAEkG,IAAI,CAAClG,UAAS,IAAK,EAAE;QACjCC,WAAW,EAAEiG,IAAI,CAACjG,WAAU,IAAK,EAAE;QACnCC,SAAS,EAAEgG,IAAI,CAAChG,SAAQ,GAAIgG,IAAI,CAAChG,SAAS,CAACgH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;QAC7D/G,UAAU,EAAE+F,IAAI,CAAC/F,UAAS,GAAI+F,IAAI,CAAC/F,UAAU,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;QAChE9G,MAAM,EAAE8F,IAAI,CAAC9F,MAAK,IAAK,EAAE;QACzBC,eAAe,EAAE6F,IAAI,CAAC7F,eAAc,IAAK,CAAC;QAC1CC,WAAW,EAAE4F,IAAI,CAAC5F,WAAU,IAAK,UAAU;QAC3CC,YAAY,EAAE2F,IAAI,CAAC3F,YAAW,IAAK,EAAE;QACrCC,MAAM,EAAE0F,IAAI,CAAC1F,MAAK,IAAK,EAAE;QACzBC,WAAW,EAAEyF,IAAI,CAACzF,WAAU,IAAK,EAAE;QACnCC,QAAQ,EAAEwF,IAAI,CAACxF,QAAO,IAAK,EAAE;QAC7BC,iBAAiB,EAAEuF,IAAI,CAACvF,iBAAgB,IAAK,EAAE;QAC/CC,QAAQ,EAAEsF,IAAI,CAACtF,QAAO,IAAK,EAAE;QAC7BC,WAAW,EAAEqF,IAAI,CAACrF,WAAU,IAAK,EAAE;QACnCC,kBAAkB,EAAEoF,IAAI,CAACpF,kBAAiB,IAAK,IAAI;QACnDC,mBAAmB,EAAEmF,IAAI,CAACnF,mBAAkB,IAAK,IAAI;QACrDI,aAAa,EAAE,KAAK;QACpBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC;MAED,IAAI,CAACjC,cAAa,GAAI,CAAC,CAAC;MAExB,IAAI;QACF,MAAM0J,KAAI,GAAI,IAAInL,KAAK,CAACqL,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC,CAAC;QACjEH,KAAK,CAACe,IAAI,CAAC,CAAC;MACd,EAAE,OAAOnG,KAAK,EAAE;QACd,IAAI,CAACC,SAAS,CAAC,OAAO,EAAE,gCAAgC,CAAC;MAC3D;IACF,CAAC;IAED,MAAMuH,cAAcA,CAAA,EAAG;MACrB,IAAI,CAAC,IAAI,CAACX,oBAAoB,CAAC,CAAC,EAAE;QAChC,IAAI,CAAC5G,SAAS,CAAC,OAAO,EAAE,qDAAqD,CAAC;QAC9E;MACF;MAEA,IAAI,CAACzE,eAAc,GAAI,IAAI;MAC3B,IAAI;QACF,MAAM+K,IAAG,GAAI,IAAI,CAACjJ,YAAY;QAC9B,MAAMmK,UAAS,GAAI;UACjB7L,QAAQ,EAAE2K,IAAI,CAAC3K,QAAQ;UACvBC,KAAK,EAAE0K,IAAI,CAAC1K,KAAK;UACjBE,UAAU,EAAEwK,IAAI,CAACxK,UAAU;UAC3BC,WAAW,EAAEuK,IAAI,CAACvK,WAAW;UAC7BC,SAAS,EAAEsK,IAAI,CAACtK,SAAS;UACzBC,MAAM,EAAEqK,IAAI,CAACrK,MAAM;UACnBsB,MAAM,EAAE+I,IAAI,CAAC/I,MAAM;UACnBpB,YAAY,EAAEmK,IAAI,CAACnK;QACrB,CAAC;;QAED;QACA,IAAImK,IAAI,CAACpK,IAAG,KAAM,OAAO,EAAE;UACzBiK,MAAM,CAACC,MAAM,CAACoB,UAAU,EAAE;YACxBpL,QAAQ,EAAEkK,IAAI,CAAClK,QAAQ;YACvBC,UAAU,EAAEiK,IAAI,CAACjK,UAAU;YAC3BC,WAAW,EAAEgK,IAAI,CAAChK,WAAW;YAC7BC,SAAS,EAAE+J,IAAI,CAAC/J;UAClB,CAAC,CAAC;QACJ;;QAEA;QACA,IAAI+J,IAAI,CAAC9I,aAAY,IAAK8I,IAAI,CAAC7I,WAAW,EAAE;UAC1C+J,UAAU,CAAC3L,QAAO,GAAIyK,IAAI,CAAC7I,WAAW;QACxC;QAEA,MAAMgE,QAAO,GAAI,MAAM1H,qBAAqB,CAAC0N,UAAU,CAACnB,IAAI,CAAChJ,EAAE,EAAEkK,UAAU,CAAC;QAE5E,IAAI/F,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC3B,SAAS,CAAC,SAAS,EAAE,2BAA2B,CAAC;UACtD,IAAI,CAACiF,UAAU,CAAC,eAAe,CAAC;UAChC,MAAMmC,OAAO,CAACC,GAAG,CAAC,CAAC,IAAI,CAAClH,SAAS,CAAC,CAAC,EAAE,IAAI,CAACD,aAAa,CAAC,CAAC,CAAC,CAAC;QAC7D,OAAO;UACL,MAAM,IAAIuC,KAAK,CAAChB,QAAQ,CAACiB,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAO3C,KAAK,EAAE;QACd,IAAI,CAACyF,eAAe,CAACzF,KAAK,EAAE,gBAAgB,CAAC;MAC/C,UAAU;QACR,IAAI,CAACxE,eAAc,GAAI,KAAK;MAC9B;IACF,CAAC;IAED,MAAMmM,QAAQA,CAACnF,IAAI,EAAE;MACnB,IAAI;QACF,MAAMd,QAAO,GAAI,MAAM1H,qBAAqB,CAAC4N,OAAO,CAACpF,IAAI,CAACjF,EAAE,CAAC;QAC7D,IAAImE,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACtG,YAAW,GAAItB,qBAAqB,CAACyI,cAAc,CAACf,QAAQ,CAACtH,IAAI,CAAC;UACvE,MAAMgL,KAAI,GAAI,IAAInL,KAAK,CAACqL,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC,CAAC;UACjEH,KAAK,CAACe,IAAI,CAAC,CAAC;QACd,OAAO;UACL,MAAM,IAAIzD,KAAK,CAAChB,QAAQ,CAACiB,OAAM,IAAK,6BAA6B,CAAC;QACpE;MACF,EAAE,OAAO3C,KAAK,EAAE;QACd,MAAM4E,YAAW,GAAI5E,KAAK,CAAC0B,QAAQ,EAAEtH,IAAI,EAAEuI,OAAM,IAAK3C,KAAK,CAAC2C,OAAM,IAAK,6BAA6B;QACpG,IAAI,CAAC1C,SAAS,CAAC,OAAO,EAAE2E,YAAY,CAAC;MACvC;IACF;;IAEA;IACA;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}