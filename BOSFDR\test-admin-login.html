<!DOCTYPE html>
<html>
<head>
    <title>Test Admin Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        input { padding: 8px; margin: 5px; width: 200px; }
        button { padding: 10px 20px; margin: 5px; }
        .results { margin-top: 20px; padding: 10px; background: #f5f5f5; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Admin Login Test</h1>
    
    <div class="form-group">
        <input type="text" id="username" placeholder="Username" value="admin12345">
    </div>
    <div class="form-group">
        <input type="password" id="password" placeholder="Password" value="12345QWERTqwert">
    </div>
    <div class="form-group">
        <button onclick="testLogin()">Test Login</button>
        <button onclick="testUsersAPI()">Test Users API</button>
        <button onclick="clearStorage()">Clear Storage</button>
    </div>
    
    <div id="results" class="results"></div>

    <script>
        function log(message, type = 'info') {
            const div = document.getElementById('results');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            div.innerHTML += `<p class="${className}">${message}</p>`;
            console.log(message);
        }

        function clearStorage() {
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminData');
            localStorage.removeItem('clientToken');
            localStorage.removeItem('clientData');
            log('Storage cleared', 'success');
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            log('Testing admin login...');
            
            try {
                const response = await fetch('http://localhost:3000/api/admin/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log('✅ Login successful!', 'success');
                    log('Token: ' + data.data.token.substring(0, 50) + '...');
                    log('Admin: ' + JSON.stringify(data.data.admin, null, 2));
                    
                    // Store in localStorage
                    localStorage.setItem('adminToken', data.data.token);
                    localStorage.setItem('adminData', JSON.stringify(data.data.admin));
                    
                    log('✅ Token and data stored in localStorage', 'success');
                } else {
                    log('❌ Login failed: ' + (data.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                log('❌ Login error: ' + error.message, 'error');
            }
        }

        async function testUsersAPI() {
            const adminToken = localStorage.getItem('adminToken');
            
            if (!adminToken) {
                log('❌ No admin token found. Please login first.', 'error');
                return;
            }
            
            log('Testing users API...');
            
            try {
                const response = await fetch('http://localhost:3000/api/users/stats', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + adminToken,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log('✅ Users API successful!', 'success');
                    log('Stats: ' + JSON.stringify(data, null, 2));
                } else {
                    log('❌ Users API failed: ' + (data.message || 'Unknown error'), 'error');
                    log('Status: ' + response.status);
                }
            } catch (error) {
                log('❌ Users API error: ' + error.message, 'error');
            }
        }

        // Check current storage on load
        window.onload = function() {
            const adminToken = localStorage.getItem('adminToken');
            const adminData = localStorage.getItem('adminData');
            
            if (adminToken) {
                log('✅ Admin token found in storage', 'success');
                if (adminData) {
                    try {
                        const data = JSON.parse(adminData);
                        log('Admin: ' + data.username + ' (' + data.role + ')');
                    } catch (e) {
                        log('❌ Error parsing admin data', 'error');
                    }
                }
            } else {
                log('ℹ️ No admin token found in storage');
            }
        };
    </script>
</body>
</html>
