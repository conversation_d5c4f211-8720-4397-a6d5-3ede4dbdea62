{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport notificationService from '@/services/notificationService';\nexport default {\n  name: 'AdminRequests',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n  data() {\n    return {\n      // UI State\n      loading: true,\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      errorMessage: '',\n      viewMode: 'table',\n      // 'card' or 'table' - default to table view\n\n      // Request Management Data\n      requests: [],\n      selectedRequests: [],\n      currentRequest: null,\n      statusOptions: [],\n      // Pagination\n      pagination: {\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n      },\n      // Filters\n      filters: {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      },\n      // Statistics\n      requestStats: {\n        total: 0,\n        pending: 0,\n        approved: 0,\n        completed: 0,\n        thisMonth: 0\n      },\n      // UI State\n      showFilters: false,\n      showBulkActions: false,\n      showRequestDetails: false,\n      showRejectForm: false,\n      showQuickReject: false,\n      bulkAction: '',\n      bulkReason: '',\n      // Status Update Forms\n      statusUpdateForm: {\n        status_id: '',\n        reason: ''\n      },\n      rejectForm: {\n        reason: ''\n      },\n      quickRejectForm: {\n        reason: '',\n        loading: false,\n        error: ''\n      },\n      selectedRequestForReject: null,\n      // Real-time features\n      refreshInterval: null,\n      autoRefreshEnabled: true,\n      refreshRate: 30000,\n      // 30 seconds\n      lastRefresh: null\n    };\n  },\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load component data\n    await this.loadComponentData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n  },\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n    // Load all component data\n    async loadComponentData() {\n      this.loading = true;\n      try {\n        await Promise.all([this.loadAdminProfile(), this.loadStatusOptions(), this.loadRequests(), this.loadDashboardStats()]);\n      } catch (error) {\n        console.error('Failed to load component data:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request data';\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n    // Load status options\n    async loadStatusOptions() {\n      try {\n        const response = await adminDocumentService.getStatusOptions();\n        if (response.success) {\n          this.statusOptions = response.data || [];\n        }\n      } catch (error) {\n        console.error('Failed to load status options:', error);\n        this.statusOptions = [];\n      }\n    },\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        const response = await adminDocumentService.getDashboardStats();\n        if (response.success) {\n          this.requestStats = {\n            total: response.data.totalRequests || 0,\n            pending: response.data.pendingRequests || 0,\n            approved: response.data.approvedRequests || 0,\n            completed: response.data.completedRequests || 0,\n            thisMonth: response.data.todayRequests || 0\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load dashboard stats:', error);\n      }\n    },\n    // Load requests with current filters and pagination\n    async loadRequests() {\n      try {\n        const params = {\n          page: this.pagination.currentPage,\n          limit: this.pagination.itemsPerPage,\n          ...this.filters\n        };\n        const response = await adminDocumentService.getAllRequests(params);\n        if (response.success) {\n          this.requests = response.data.requests || [];\n          this.pagination = {\n            currentPage: response.data.pagination?.current_page || 1,\n            totalPages: response.data.pagination?.total_pages || 1,\n            totalItems: response.data.pagination?.total_records || 0,\n            itemsPerPage: response.data.pagination?.per_page || 10\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load requests';\n        this.requests = [];\n      }\n    },\n    // Filter and search methods\n    applyFilters() {\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n    clearFilters() {\n      this.filters = {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      };\n      this.applyFilters();\n    },\n    // Pagination methods\n    changePage(page) {\n      if (page >= 1 && page <= this.pagination.totalPages) {\n        this.pagination.currentPage = page;\n        this.loadRequests();\n      }\n    },\n    changeItemsPerPage(itemsPerPage) {\n      this.pagination.itemsPerPage = itemsPerPage;\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n    goBack() {\n      this.$router.push('/admin/dashboard');\n    },\n    // Request selection methods\n    toggleRequestSelection(requestId) {\n      const index = this.selectedRequests.indexOf(requestId);\n      if (index > -1) {\n        this.selectedRequests.splice(index, 1);\n      } else {\n        this.selectedRequests.push(requestId);\n      }\n    },\n    selectAllRequests() {\n      if (this.selectedRequests.length === this.requests.length) {\n        this.selectedRequests = [];\n      } else {\n        this.selectedRequests = this.requests.map(r => r.id);\n      }\n    },\n    // Request details\n    async viewRequestDetails(requestId) {\n      console.log('🚀 View details clicked for request ID:', requestId);\n      try {\n        const response = await adminDocumentService.getRequestDetails(requestId);\n        if (response.success) {\n          this.currentRequest = response.data;\n          this.showRequestDetails = true;\n          // Reset forms\n          this.statusUpdateForm = {\n            status_id: '',\n            reason: ''\n          };\n          this.rejectForm = {\n            reason: ''\n          };\n          this.showRejectForm = false;\n          console.log('📋 Request details loaded:', response.data);\n          this.showToast('Success', `Request details loaded for ${response.data.request_number}`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to load request details:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request details';\n        this.showToast('Error', 'Failed to load request details', 'error');\n      }\n    },\n    // Refresh request details\n    async refreshRequestDetails() {\n      if (this.currentRequest) {\n        await this.viewRequestDetails(this.currentRequest.id);\n      }\n    },\n    // Update request status from modal\n    async updateRequestStatusFromModal() {\n      if (!this.statusUpdateForm.status_id || !this.currentRequest) return;\n      try {\n        const response = await adminDocumentService.updateRequestStatus(this.currentRequest.id, {\n          status_id: parseInt(this.statusUpdateForm.status_id),\n          reason: this.statusUpdateForm.reason\n        });\n        if (response.success) {\n          // Refresh the request details\n          await this.refreshRequestDetails();\n          // Refresh the main requests list\n          await this.loadRequests();\n          // Reset form\n          this.statusUpdateForm = {\n            status_id: '',\n            reason: ''\n          };\n\n          // Show success message\n          this.errorMessage = '';\n          // You could add a success message here if needed\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n    // Approve request from modal\n    async approveRequestFromModal() {\n      if (!this.currentRequest) return;\n      try {\n        const response = await adminDocumentService.approveRequest(this.currentRequest.id, {\n          reason: 'Approved from request details'\n        });\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n    // Reject request from modal\n    async rejectRequestFromModal() {\n      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;\n      try {\n        const response = await adminDocumentService.rejectRequest(this.currentRequest.id, {\n          reason: this.rejectForm.reason\n        });\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n          this.rejectForm = {\n            reason: ''\n          };\n          this.showRejectForm = false;\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n    // Status update methods\n    async updateRequestStatus(requestId, statusId, reason = '') {\n      try {\n        const response = await adminDocumentService.updateRequestStatus(requestId, {\n          status_id: statusId,\n          reason: reason\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n    async approveRequest(requestId, reason = '') {\n      try {\n        const response = await adminDocumentService.approveRequest(requestId, {\n          reason\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n    async rejectRequest(requestId, reason) {\n      if (!reason || reason.trim() === '') {\n        this.errorMessage = 'Rejection reason is required';\n        return;\n      }\n      try {\n        const response = await adminDocumentService.rejectRequest(requestId, {\n          reason\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n    // Quick approval/rejection methods\n    canApprove(request) {\n      // Can only approve requests that are not already approved or rejected\n      return ['pending', 'under_review', 'additional_info_required'].includes(request.status_name);\n    },\n    canReject(request) {\n      // Can only reject requests that are not already approved or rejected\n      // Once approved or rejected, the request status is final\n      return ['pending', 'under_review', 'additional_info_required'].includes(request.status_name);\n    },\n    async quickApprove(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n      try {\n        this.loading = true;\n        const response = await adminDocumentService.approveRequest(request.id, {\n          reason: 'Quick approval from admin interface'\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');\n      } finally {\n        this.loading = false;\n      }\n    },\n    showQuickRejectModal(request) {\n      console.log('🚀 Quick reject clicked for request:', request);\n      this.selectedRequestForReject = request;\n      this.quickRejectForm = {\n        reason: '',\n        loading: false,\n        error: ''\n      };\n      this.showQuickReject = true;\n    },\n    closeQuickRejectModal() {\n      this.showQuickReject = false;\n      this.selectedRequestForReject = null;\n      this.quickRejectForm = {\n        reason: '',\n        loading: false,\n        error: ''\n      };\n    },\n    async confirmQuickReject() {\n      if (!this.quickRejectForm.reason.trim()) {\n        this.quickRejectForm.error = 'Rejection reason is required';\n        return;\n      }\n      this.quickRejectForm.loading = true;\n      this.quickRejectForm.error = '';\n      try {\n        const response = await adminDocumentService.rejectRequest(this.selectedRequestForReject.id, {\n          reason: this.quickRejectForm.reason\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');\n          this.closeQuickRejectModal();\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickRejectForm.error = errorData.message || 'Failed to reject request';\n      } finally {\n        this.quickRejectForm.loading = false;\n      }\n    },\n    // Bulk operations\n    async performBulkAction() {\n      if (this.selectedRequests.length === 0) {\n        this.errorMessage = 'Please select at least one request';\n        return;\n      }\n      if (!this.bulkAction) {\n        this.errorMessage = 'Please select a bulk action';\n        return;\n      }\n      try {\n        const response = await adminDocumentService.bulkUpdateRequests({\n          request_ids: this.selectedRequests,\n          status_id: parseInt(this.bulkAction),\n          reason: this.bulkReason\n        });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.selectedRequests = [];\n          this.bulkAction = '';\n          this.bulkReason = '';\n          this.showBulkActions = false;\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to perform bulk action:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to perform bulk action';\n      }\n    },\n    // Export functionality\n    async exportRequests() {\n      try {\n        const csvData = await adminDocumentService.exportRequests(this.filters);\n        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;\n        adminDocumentService.downloadCSV(csvData, filename);\n      } catch (error) {\n        console.error('Failed to export requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to export requests';\n      }\n    },\n    // Utility methods\n    formatStatus(status) {\n      return adminDocumentService.formatStatus(status);\n    },\n    getStatusColor(status) {\n      return adminDocumentService.getStatusColor(status);\n    },\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        style: 'currency',\n        currency: 'PHP'\n      }).format(amount || 0);\n    },\n    formatDateTime(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n    formatTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminRequests');\n      try {\n        // Initialize notification service\n        await notificationService.init('admin');\n\n        // Listen for request-related notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('new_request', this.handleNewRequest);\n\n        // Start auto-refresh if enabled\n        if (this.autoRefreshEnabled) {\n          this.startAutoRefresh();\n        }\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminRequests');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('new_request', this.handleNewRequest);\n\n      // Cleanup (simplified)\n      notificationService.cleanup();\n\n      // Stop auto-refresh\n      this.stopAutoRefresh();\n    },\n    startAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n      }\n      this.refreshInterval = setInterval(() => {\n        if (this.autoRefreshEnabled && !this.loading) {\n          console.log('Auto-refreshing requests data...');\n          this.refreshRequestsData();\n        }\n      }, this.refreshRate);\n      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);\n    },\n    stopAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n        this.refreshInterval = null;\n        console.log('Auto-refresh stopped');\n      }\n    },\n    toggleAutoRefresh() {\n      this.autoRefreshEnabled = !this.autoRefreshEnabled;\n      if (this.autoRefreshEnabled) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n    async refreshRequestsData() {\n      try {\n        this.lastRefresh = new Date();\n\n        // Refresh requests list\n        await this.loadRequests();\n\n        // Refresh statistics\n        await this.loadDashboardStats();\n\n        // If request details modal is open, refresh it\n        if (this.showRequestDetails && this.currentRequest) {\n          await this.refreshRequestDetails();\n        }\n        console.log('Requests data refreshed successfully');\n      } catch (error) {\n        console.error('Failed to refresh requests data:', error);\n      }\n    },\n    handleRealTimeNotification(notification) {\n      console.log('Real-time notification received:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_updated':\n          this.handleRequestUpdate(notification.data);\n          break;\n        case 'unread_count_update':\n        case 'heartbeat':\n          // Polling system notifications - handled by notification service\n          break;\n        default:\n          // Only log unknown types, not system types\n          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {\n            console.log('Unhandled notification type:', notification.type);\n          }\n      }\n    },\n    handleStatusChange(data) {\n      console.log('Request status changed:', data);\n\n      // Update the request in the list if it exists\n      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);\n      if (requestIndex !== -1) {\n        // Refresh the specific request or reload all requests\n        this.refreshRequestsData();\n      }\n\n      // Show toast notification\n      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');\n    },\n    handleNewRequest(data) {\n      console.log('New request received:', data);\n\n      // Refresh requests to show the new request\n      this.refreshRequestsData();\n\n      // Show toast notification\n      this.showToast('New Request', `New ${data.document_type} request received`, 'success');\n    },\n    handleRequestUpdate(data) {\n      console.log('Request updated:', data);\n\n      // If the updated request is currently being viewed, refresh details\n      if (this.currentRequest && this.currentRequest.id === data.request_id) {\n        this.refreshRequestDetails();\n      }\n\n      // Refresh the requests list\n      this.refreshRequestsData();\n    },\n    showToast(title, message, type = 'info') {\n      // Log to console for debugging\n      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);\n\n      // Create a simple toast notification\n      const toast = document.createElement('div');\n      toast.className = `toast-notification toast-${type}`;\n      toast.innerHTML = `\n        <div class=\"toast-header\">\n          <strong>${title}</strong>\n          <button type=\"button\" class=\"toast-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>\n        </div>\n        <div class=\"toast-body\">${message}</div>\n      `;\n\n      // Add toast styles if not already added\n      if (!document.getElementById('toast-styles')) {\n        const styles = document.createElement('style');\n        styles.id = 'toast-styles';\n        styles.textContent = `\n          .toast-notification {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            min-width: 300px;\n            background: white;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n            z-index: 9999;\n            animation: slideIn 0.3s ease;\n          }\n          .toast-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 12px 16px 8px;\n            border-bottom: 1px solid #e9ecef;\n          }\n          .toast-body {\n            padding: 8px 16px 12px;\n            color: #6c757d;\n          }\n          .toast-close {\n            background: none;\n            border: none;\n            font-size: 18px;\n            cursor: pointer;\n            color: #6c757d;\n          }\n          .toast-success { border-left: 4px solid #28a745; }\n          .toast-error { border-left: 4px solid #dc3545; }\n          .toast-info { border-left: 4px solid #17a2b8; }\n          .toast-warning { border-left: 4px solid #ffc107; }\n          @keyframes slideIn {\n            from { transform: translateX(100%); opacity: 0; }\n            to { transform: translateX(0); opacity: 1; }\n          }\n        `;\n        document.head.appendChild(styles);\n      }\n\n      // Add toast to page\n      document.body.appendChild(toast);\n\n      // Auto-remove after 5 seconds\n      setTimeout(() => {\n        if (toast.parentElement) {\n          toast.style.animation = 'slideIn 0.3s ease reverse';\n          setTimeout(() => toast.remove(), 300);\n        }\n      }, 5000);\n    }\n  }\n};", "map": {"version": 3, "names": ["Ad<PERSON><PERSON><PERSON><PERSON>", "AdminSidebar", "adminAuthService", "adminDocumentService", "notificationService", "name", "components", "data", "loading", "sidebarCollapsed", "showUserDropdown", "isMobile", "adminData", "errorMessage", "viewMode", "requests", "selectedRequests", "currentRequest", "statusOptions", "pagination", "currentPage", "totalPages", "totalItems", "itemsPerPage", "filters", "status", "document_type", "priority", "search", "date_from", "date_to", "requestStats", "total", "pending", "approved", "completed", "thisMonth", "showFilters", "showBulkActions", "showRequestDetails", "showRejectForm", "showQuickReject", "bulkAction", "bulkReason", "statusUpdateForm", "status_id", "reason", "rejectForm", "quickRejectForm", "error", "selectedRequestForReject", "refreshInterval", "autoRefreshEnabled", "refreshRate", "lastRefresh", "mounted", "isLoggedIn", "$router", "push", "initializeUI", "loadComponentData", "initializeRealTimeFeatures", "beforeUnmount", "handleResize", "window", "removeEventListener", "cleanupRealTimeFeatures", "computed", "activeMenu", "path", "$route", "includes", "methods", "innerWidth", "saved", "localStorage", "getItem", "JSON", "parse", "was<PERSON><PERSON><PERSON>", "addEventListener", "handleSidebarToggle", "setItem", "stringify", "handleMenuChange", "menu", "routes", "handleUserDropdownToggle", "handleMenuAction", "action", "closeMobileSidebar", "handleLogout", "logout", "loadAdminProfile", "response", "getProfile", "success", "console", "getAdminData", "Promise", "all", "loadStatusOptions", "loadRequests", "loadDashboardStats", "errorData", "parseError", "message", "getStatusOptions", "getDashboardStats", "totalRequests", "pendingRequests", "approvedRequests", "completedRequests", "todayRequests", "params", "page", "limit", "getAllRequests", "current_page", "total_pages", "total_records", "per_page", "applyFilters", "clearFilters", "changePage", "changeItemsPerPage", "goBack", "toggleRequestSelection", "requestId", "index", "indexOf", "splice", "selectAllRequests", "length", "map", "r", "id", "viewRequestDetails", "log", "getRequestDetails", "showToast", "request_number", "refreshRequestDetails", "updateRequestStatusFromModal", "updateRequestStatus", "parseInt", "approveRequestFromModal", "approveRequest", "rejectRequestFromModal", "trim", "rejectRequest", "statusId", "canApprove", "request", "status_name", "canReject", "quickApprove", "showQuickRejectModal", "closeQuickRejectModal", "confirmQuickReject", "performBulkAction", "bulkUpdateRequests", "request_ids", "exportRequests", "csvData", "filename", "Date", "toISOString", "split", "downloadCSV", "formatStatus", "getStatusColor", "formatDate", "dateString", "date", "toLocaleDateString", "year", "month", "day", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "formatDateTime", "toLocaleString", "hour", "minute", "formatTime", "toLocaleTimeString", "hour12", "init", "on", "handleRealTimeNotification", "handleStatusChange", "handleNewRequest", "startAutoRefresh", "off", "cleanup", "stopAutoRefresh", "clearInterval", "setInterval", "refreshRequestsData", "toggleAutoRefresh", "notification", "type", "handleRequestUpdate", "requestIndex", "findIndex", "req", "request_id", "new_status", "title", "toUpperCase", "toast", "document", "createElement", "className", "innerHTML", "getElementById", "styles", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "setTimeout", "parentElement", "animation", "remove"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRequests.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-requests\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <!-- Loading State -->\n        <div v-if=\"loading\" class=\"d-flex justify-content-center align-items-center\" style=\"min-height: 400px;\">\n          <div class=\"spinner-border text-primary\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n\n        <!-- Main Content -->\n        <div v-else class=\"container-fluid py-4\">\n          <!-- Error Message -->\n          <div v-if=\"errorMessage\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ errorMessage }}\n            <button type=\"button\" class=\"btn-close\" @click=\"errorMessage = ''\" aria-label=\"Close\"></button>\n          </div>\n\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n                <div>\n                  <p class=\"text-muted mb-0\">\n                    <span v-if=\"lastRefresh\" class=\"ms-2 small\">\n                      <i class=\"fas fa-clock text-muted\"></i>\n                      Last updated: {{ formatTime(lastRefresh) }}\n                    </span>\n                  </p>\n                </div>\n                <div class=\"d-flex gap-2 align-items-center\">\n                  <!-- Real-time status indicator -->\n                  <div class=\"real-time-status me-2\">\n                    <span class=\"badge\" :class=\"autoRefreshEnabled ? 'bg-success' : 'bg-secondary'\">\n                      <i class=\"fas fa-circle pulse\" v-if=\"autoRefreshEnabled\"></i>\n                      <i class=\"fas fa-pause\" v-else></i>\n                      {{ autoRefreshEnabled ? 'Live' : 'Paused' }}\n                    </span>\n                  </div>\n\n                  <button class=\"btn btn-outline-secondary btn-sm\" @click=\"toggleAutoRefresh\" :title=\"autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'\">\n                    <i class=\"fas\" :class=\"autoRefreshEnabled ? 'fa-pause' : 'fa-play'\"></i>\n                  </button>\n                  <button class=\"btn btn-outline-primary btn-sm\" @click=\"showFilters = !showFilters\">\n                    <i class=\"fas fa-filter me-1\"></i>\n                    {{ showFilters ? 'Hide' : 'Show' }} Filters\n                  </button>\n                  <!-- <button class=\"btn btn-success btn-sm\" @click=\"exportRequests\" :disabled=\"loading\">\n                    <i class=\"fas fa-download me-1\"></i>\n                    Export CSV\n                  </button> -->\n                  <button class=\"btn btn-primary btn-sm\" @click=\"refreshRequestsData\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Request Statistics -->\n          <div class=\"row mb-3\">\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-primary shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-primary text-uppercase mb-1\">Total Requests</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.total || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-file-alt fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-warning shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-warning text-uppercase mb-1\">Pending</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.pending || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-clock fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-success shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-success text-uppercase mb-1\">Completed</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.completed || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-check-circle fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-6 col-md-3 mb-2\">\n              <div class=\"card border-left-info shadow py-1\">\n                <div class=\"card-body p-2\">\n                  <div class=\"d-flex align-items-center\">\n                    <div class=\"flex-grow-1\">\n                      <div class=\"text-xs fw-bold text-info text-uppercase mb-1\">Approved</div>\n                      <div class=\"h6 mb-0 fw-bold text-dark\">{{ requestStats.approved || 0 }}</div>\n                    </div>\n                    <i class=\"fas fa-thumbs-up fa-lg text-muted ms-2\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Filters Panel -->\n          <div v-if=\"showFilters\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3\">\n              <h6 class=\"m-0 fw-bold text-primary\">Filter Requests</h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Search</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    v-model=\"filters.search\"\n                    placeholder=\"Search by name, email, or request number\"\n                    @keyup.enter=\"applyFilters\"\n                  >\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Status</label>\n                  <select class=\"form-select\" v-model=\"filters.status\">\n                    <option value=\"\">All Statuses</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.status_name\">\n                      {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Document Type</label>\n                  <select class=\"form-select\" v-model=\"filters.document_type\">\n                    <option value=\"\">All Types</option>\n                    <option value=\"barangay_clearance\">Barangay Clearance</option>\n                    <option value=\"cedula\">Cedula</option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date From</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_from\">\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date To</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_to\">\n                </div>\n                <div class=\"col-md-1 mb-3 d-flex align-items-end\">\n                  <div class=\"d-flex gap-1 w-100\">\n                    <button class=\"btn btn-primary btn-sm\" @click=\"applyFilters\">\n                      <i class=\"fas fa-search\"></i>\n                    </button>\n                    <button class=\"btn btn-outline-secondary btn-sm\" @click=\"clearFilters\">\n                      <i class=\"fas fa-times\"></i>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bulk Actions Panel -->\n          <div v-if=\"selectedRequests.length > 0\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3 bg-warning\">\n              <h6 class=\"m-0 fw-bold text-dark\">\n                <i class=\"fas fa-tasks me-2\"></i>\n                Bulk Actions ({{ selectedRequests.length }} selected)\n              </h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row align-items-end\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Action</label>\n                  <select class=\"form-select\" v-model=\"bulkAction\">\n                    <option value=\"\">Select Action</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.id\">\n                      Change to {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label class=\"form-label\">Reason (Optional)</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    v-model=\"bulkReason\"\n                    placeholder=\"Enter reason for bulk action\"\n                  >\n                </div>\n                <div class=\"col-md-3 mb-3\">\n                  <div class=\"d-flex gap-2\">\n                    <button class=\"btn btn-warning\" @click=\"performBulkAction\" :disabled=\"!bulkAction\">\n                      <i class=\"fas fa-play me-1\"></i>\n                      Apply\n                    </button>\n                    <button class=\"btn btn-outline-secondary\" @click=\"selectedRequests = []\">\n                      <i class=\"fas fa-times me-1\"></i>\n                      Cancel\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- View Toggle -->\n          <div class=\"d-flex justify-content-between align-items-center mb-4\">\n            <div class=\"d-flex align-items-center gap-3\">\n              <div class=\"btn-group\" role=\"group\" aria-label=\"View toggle\">\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"cardView\" v-model=\"viewMode\" value=\"card\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"cardView\">\n                  <i class=\"fas fa-th-large me-1\"></i>Cards\n                </label>\n\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"tableView\" v-model=\"viewMode\" value=\"table\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"tableView\">\n                  <i class=\"fas fa-table me-1\"></i>Table\n                </label>\n              </div>\n\n              <div class=\"d-flex align-items-center gap-2\">\n                <span class=\"text-muted small\">\n                  Showing {{ ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1 }} -\n                  {{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}\n                  of {{ pagination.totalItems }} requests\n                </span>\n                <select class=\"form-select form-select-sm\" style=\"width: auto;\" v-model=\"pagination.itemsPerPage\" @change=\"changeItemsPerPage(pagination.itemsPerPage)\">\n                  <option value=\"10\">10 per page</option>\n                  <option value=\"25\">25 per page</option>\n                  <option value=\"50\">50 per page</option>\n                  <option value=\"100\">100 per page</option>\n                </select>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-items-center gap-2\">\n              <button class=\"btn btn-sm btn-outline-secondary\" @click=\"selectAllRequests\" v-if=\"requests.length > 0\">\n                <i class=\"fas fa-check-square me-1\"></i>\n                {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}\n              </button>\n            </div>\n          </div>\n\n          <!-- Card View -->\n          <div v-if=\"viewMode === 'card'\" class=\"requests-grid\">\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"empty-state text-center py-5\">\n              <div class=\"empty-state-icon mb-3\">\n                <i class=\"fas fa-inbox fa-4x text-muted\"></i>\n              </div>\n              <h5 class=\"text-muted mb-2\">No Document Requests Found</h5>\n              <p class=\"text-muted\">There are no document requests matching your current filters.</p>\n            </div>\n\n            <!-- Request Cards -->\n            <div v-else class=\"row g-4\">\n              <div v-for=\"request in requests\" :key=\"request.id\" class=\"col-xl-4 col-lg-6 col-md-6\">\n                <div class=\"request-card\" :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n                  <!-- Card Header -->\n                  <div class=\"request-card-header\">\n                    <div class=\"d-flex justify-content-between align-items-start\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <input\n                          type=\"checkbox\"\n                          class=\"form-check-input\"\n                          :checked=\"selectedRequests.includes(request.id)\"\n                          @change=\"toggleRequestSelection(request.id)\"\n                        >\n                        <div class=\"request-number\">\n                          <span class=\"badge bg-primary\">{{ request.request_number }}</span>\n                        </div>\n                      </div>\n                      <div class=\"request-actions-simple\">\n                        <button class=\"btn btn-sm btn-outline-primary\" @click=\"viewRequestDetails(request.id)\" title=\"View Details\">\n                          <i class=\"fas fa-eye\"></i>\n                        </button>\n                        <button\n                          v-if=\"canApprove(request)\"\n                          class=\"btn btn-sm btn-success\"\n                          @click=\"quickApprove(request)\"\n                          title=\"Quick Approve\"\n                          :disabled=\"loading\"\n                        >\n                          <i class=\"fas fa-check\"></i>\n                        </button>\n                        <button\n                          v-if=\"canReject(request)\"\n                          class=\"btn btn-sm btn-danger\"\n                          @click=\"showQuickRejectModal(request)\"\n                          title=\"Quick Reject\"\n                          :disabled=\"loading\"\n                        >\n                          <i class=\"fas fa-times\"></i>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Card Body -->\n                  <div class=\"request-card-body\">\n                    <!-- Client Info -->\n                    <div class=\"client-info mb-3\">\n                      <div class=\"d-flex align-items-center gap-2 mb-2\">\n                        <div class=\"client-avatar\">\n                          <i class=\"fas fa-user-circle fa-2x text-primary\"></i>\n                        </div>\n                        <div>\n                          <h6 class=\"mb-0 fw-bold\">{{ request.client_name }}</h6>\n                          <small class=\"text-muted\">{{ request.client_email }}</small>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Document Type -->\n                    <div class=\"document-type mb-3\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <i class=\"fas fa-file-alt text-info\"></i>\n                        <span class=\"badge bg-info-subtle text-info-emphasis px-3 py-2\">\n                          {{ request.document_type }}\n                        </span>\n                      </div>\n                    </div>\n\n                    <!-- Status and Amount -->\n                    <div class=\"request-meta mb-3\">\n                      <div class=\"row g-2\">\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Status</small>\n                            <span class=\"badge\" :class=\"`bg-${getStatusColor(request.status_name)}`\">\n                              {{ formatStatus(request.status_name) }}\n                            </span>\n                          </div>\n                        </div>\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Amount</small>\n                            <span class=\"fw-bold text-success\">{{ formatCurrency(request.total_fee) }}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Date -->\n                    <div class=\"request-date\">\n                      <small class=\"text-muted\">\n                        <i class=\"fas fa-calendar-alt me-1\"></i>\n                        Submitted {{ formatDate(request.requested_at) }}\n                      </small>\n                    </div>\n                  </div>\n\n                  <!-- Card Footer -->\n                  <div class=\"request-card-footer\">\n                    <div class=\"d-flex gap-2\">\n                      <button class=\"btn btn-sm btn-outline-primary flex-fill\" @click=\"viewRequestDetails(request.id)\">\n                        <i class=\"fas fa-eye me-1\"></i>View\n                      </button>\n                      <button\n                        v-if=\"canApprove(request)\"\n                        class=\"btn btn-sm btn-success\"\n                        @click=\"quickApprove(request)\"\n                        :title=\"'Approve Request'\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-check\"></i>\n                      </button>\n                      <button\n                        v-if=\"canReject(request)\"\n                        class=\"btn btn-sm btn-danger\"\n                        @click=\"showQuickRejectModal(request)\"\n                        :title=\"'Reject Request'\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Table View -->\n          <div v-else class=\"modern-table-container\">\n\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"modern-table-empty\">\n              <div class=\"empty-content\">\n                <div class=\"empty-icon\">\n                  <i class=\"fas fa-inbox\"></i>\n                </div>\n                <h6 class=\"empty-title\">No Document Requests Found</h6>\n                <p class=\"empty-text\">There are no document requests matching your current filters.</p>\n              </div>\n            </div>\n\n            <!-- Modern Compact Table -->\n            <div v-else class=\"compact-table-wrapper\">\n              <!-- Table Header -->\n              <div class=\"compact-table-header\">\n                <div class=\"header-cell selection-header\">\n                  <input\n                    type=\"checkbox\"\n                    class=\"form-check-input\"\n                    :checked=\"selectedRequests.length === requests.length && requests.length > 0\"\n                    @change=\"selectAllRequests\"\n                  >\n                </div>\n                <div class=\"header-cell\">Request ID</div>\n                <div class=\"header-cell\">Client</div>\n                <div class=\"header-cell\">Document</div>\n                <div class=\"header-cell\">Status</div>\n                <div class=\"header-cell\">Amount</div>\n                <div class=\"header-cell\">Date</div>\n                <div class=\"header-cell\">Actions</div>\n              </div>\n\n              <!-- Table Body -->\n              <div class=\"compact-table-body\">\n                <div v-for=\"request in requests\" :key=\"request.id\"\n                     class=\"compact-row\"\n                     :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n\n                  <!-- Selection -->\n                  <div class=\"row-cell selection-cell\">\n                    <input\n                      type=\"checkbox\"\n                      class=\"form-check-input\"\n                      :checked=\"selectedRequests.includes(request.id)\"\n                      @change=\"toggleRequestSelection(request.id)\"\n                    >\n                  </div>\n\n                  <!-- Request ID -->\n                  <div class=\"row-cell request-id-cell\">\n                    <div class=\"request-id-content\">\n                      <span class=\"request-number\">{{ request.request_number }}</span>\n                      <span class=\"request-id-small\">{{ request.id }}</span>\n                    </div>\n                  </div>\n\n                  <!-- Client -->\n                  <div class=\"row-cell client-cell\">\n                    <div class=\"client-compact\">\n                      <div class=\"client-avatar-tiny\">\n                        <i class=\"fas fa-user\"></i>\n                      </div>\n                      <div class=\"client-info-compact\">\n                        <div class=\"client-name-compact\">{{ request.client_name }}</div>\n                        <div class=\"client-email-compact\">{{ request.client_email }}</div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Document Type -->\n                  <div class=\"row-cell document-cell\">\n                    <span class=\"document-badge\">\n                      <i class=\"fas fa-file-alt\"></i>\n                      {{ request.document_type }}\n                    </span>\n                  </div>\n\n                  <!-- Status -->\n                  <div class=\"row-cell status-cell\">\n                    <span class=\"status-compact\" :class=\"`status-${getStatusColor(request.status_name)}`\">\n                      <i class=\"fas fa-circle\"></i>\n                      {{ formatStatus(request.status_name) }}\n                    </span>\n                  </div>\n\n                  <!-- Amount -->\n                  <div class=\"row-cell amount-cell\">\n                    <span class=\"amount-compact\">{{ formatCurrency(request.total_fee) }}</span>\n                  </div>\n\n                  <!-- Date -->\n                  <div class=\"row-cell date-cell\">\n                    <div class=\"date-compact\">\n                      <span class=\"date-main\">{{ formatDate(request.requested_at) }}</span>\n                      <span class=\"time-small\">{{ formatTime(request.requested_at) }}</span>\n                    </div>\n                  </div>\n\n                  <!-- Actions -->\n                  <div class=\"row-cell actions-cell\">\n                    <div class=\"actions-simple\">\n                      <button class=\"action-btn-sm view-btn-sm\" @click=\"viewRequestDetails(request.id)\" title=\"View Details\">\n                        <i class=\"fas fa-eye\"></i>\n                      </button>\n                      <button\n                        v-if=\"canApprove(request)\"\n                        class=\"action-btn-sm approve-btn-sm\"\n                        @click=\"quickApprove(request)\"\n                        title=\"Quick Approve\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-check\"></i>\n                      </button>\n                      <button\n                        v-if=\"canReject(request)\"\n                        class=\"action-btn-sm reject-btn-sm\"\n                        @click=\"showQuickRejectModal(request)\"\n                        title=\"Quick Reject\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Pagination -->\n          <div v-if=\"pagination.totalPages > 1\" class=\"pagination-container\">\n              <nav aria-label=\"Requests pagination\">\n                <ul class=\"pagination pagination-sm justify-content-center mb-0\">\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === 1 }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage - 1)\">\n                      <i class=\"fas fa-chevron-left\"></i>\n                    </a>\n                  </li>\n                  <li\n                    v-for=\"page in Math.min(pagination.totalPages, 10)\"\n                    :key=\"page\"\n                    class=\"page-item\"\n                    :class=\"{ active: page === pagination.currentPage }\"\n                  >\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(page)\">{{ page }}</a>\n                  </li>\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === pagination.totalPages }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage + 1)\">\n                      <i class=\"fas fa-chevron-right\"></i>\n                    </a>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n\n          <!-- Request Details Modal -->\n          <div v-if=\"showRequestDetails && currentRequest\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog modal-xl modal-dialog-scrollable\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-file-alt me-2\"></i>\n                    Request Details - {{ currentRequest.request_number }}\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"showRequestDetails = false\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"row\">\n                    <!-- Left Column - Request Information -->\n                    <div class=\"col-lg-8\">\n                      <!-- Basic Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-info-circle me-2\"></i>Request Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Request Number</label>\n                                <p class=\"mb-0\">{{ currentRequest.request_number }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Document Type</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge bg-info\">{{ currentRequest.document_type }}</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Category</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_category }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Details</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_details || 'Not specified' }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Current Status</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"`bg-${getStatusColor(currentRequest.status_name)}`\">\n                                    {{ formatStatus(currentRequest.status_name) }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Priority</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"currentRequest.priority === 'high' ? 'bg-danger' : currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'\">\n                                    {{ currentRequest.priority || 'Normal' }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Delivery Method</label>\n                                <p class=\"mb-0\">{{ currentRequest.delivery_method || 'Pickup' }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Date Submitted</label>\n                                <p class=\"mb-0\">{{ formatDateTime(currentRequest.requested_at) }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Client Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-user me-2\"></i>Client Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Full Name</label>\n                                <p class=\"mb-0\">{{ currentRequest.client_name }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Email Address</label>\n                                <p class=\"mb-0\">\n                                  <a :href=\"`mailto:${currentRequest.client_email}`\">{{ currentRequest.client_email }}</a>\n                                </p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Phone Number</label>\n                                <p class=\"mb-0\">\n                                  <a :href=\"`tel:${currentRequest.client_phone}`\">{{ currentRequest.client_phone || 'Not provided' }}</a>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Address</label>\n                                <p class=\"mb-0\">{{ currentRequest.client_address || 'Not provided' }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Document-Specific Details -->\n                      <div v-if=\"currentRequest.specific_details\" class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-clipboard-list me-2\"></i>Document-Specific Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <!-- Barangay Clearance Details -->\n                          <div v-if=\"currentRequest.document_type === 'Barangay Clearance'\">\n                            <div class=\"row\">\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Residency Period</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.residency_period || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Civil Status</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.civil_status || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Occupation</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.occupation || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Monthly Income</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.monthly_income) || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Cedula Details -->\n                          <div v-else-if=\"currentRequest.document_type === 'Cedula'\">\n                            <div class=\"row\">\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Birth Date</label>\n                                  <p class=\"mb-0\">{{ formatDate(currentRequest.specific_details.birth_date) || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Birth Place</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.birth_place || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Civil Status</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.civil_status || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Citizenship</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.citizenship || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Occupation</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.occupation || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Annual Income</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.annual_income) || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Tax Amount</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.tax_amount) || 'Not calculated' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Interest/Penalty</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.interest_penalty) || 'None' }}</p>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Right Column - Status Management -->\n                    <div class=\"col-lg-4\">\n                      <!-- Status Management -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-tasks me-2\"></i>Status Management</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Change Status</label>\n                            <select class=\"form-select\" v-model=\"statusUpdateForm.status_id\">\n                              <option value=\"\">Select new status</option>\n                              <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.id\">\n                                {{ formatStatus(status.status_name) }}\n                              </option>\n                            </select>\n                          </div>\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Reason/Notes</label>\n                            <textarea\n                              class=\"form-control\"\n                              rows=\"3\"\n                              v-model=\"statusUpdateForm.reason\"\n                              placeholder=\"Enter reason for status change (optional)\"\n                            ></textarea>\n                          </div>\n                          <div class=\"d-grid gap-2\">\n                            <button\n                              class=\"btn btn-primary\"\n                              @click=\"updateRequestStatusFromModal\"\n                              :disabled=\"!statusUpdateForm.status_id\"\n                            >\n                              <i class=\"fas fa-save me-1\"></i>\n                              Update Status\n                            </button>\n                            <button class=\"btn btn-success\" @click=\"approveRequestFromModal\">\n                              <i class=\"fas fa-check me-1\"></i>\n                              Quick Approve\n                            </button>\n                            <button class=\"btn btn-danger\" @click=\"showRejectForm = !showRejectForm\">\n                              <i class=\"fas fa-times me-1\"></i>\n                              {{ showRejectForm ? 'Cancel' : 'Reject Request' }}\n                            </button>\n                          </div>\n\n                          <!-- Rejection Form -->\n                          <div v-if=\"showRejectForm\" class=\"mt-3 p-3 border rounded bg-light\">\n                            <div class=\"mb-3\">\n                              <label class=\"form-label fw-bold text-danger\">Rejection Reason *</label>\n                              <textarea\n                                class=\"form-control\"\n                                rows=\"3\"\n                                v-model=\"rejectForm.reason\"\n                                placeholder=\"Please provide a detailed reason for rejection\"\n                                required\n                              ></textarea>\n                            </div>\n                            <div class=\"d-grid\">\n                              <button\n                                class=\"btn btn-danger\"\n                                @click=\"rejectRequestFromModal\"\n                                :disabled=\"!rejectForm.reason || rejectForm.reason.trim() === ''\"\n                              >\n                                <i class=\"fas fa-times me-1\"></i>\n                                Confirm Rejection\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Payment Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-credit-card me-2\"></i>Payment Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Method</label>\n                            <p class=\"mb-0\">{{ currentRequest.payment_method || 'Not specified' }}</p>\n                          </div>\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Status</label>\n                            <p class=\"mb-0\">\n                              <span class=\"badge\" :class=\"currentRequest.payment_status === 'paid' ? 'bg-success' : currentRequest.payment_status === 'pending' ? 'bg-warning' : 'bg-secondary'\">\n                                {{ currentRequest.payment_status || 'Unpaid' }}\n                              </span>\n                            </p>\n                          </div>\n                          <div class=\"row\">\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Base Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.base_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Additional Fees</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.additional_fees) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Processing Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.processing_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Total Amount</label>\n                                <p class=\"mb-0 fw-bold text-primary\">{{ formatCurrency(currentRequest.total_fee) }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Status History Timeline -->\n                  <div class=\"card\">\n                    <div class=\"card-header\">\n                      <h6 class=\"mb-0\"><i class=\"fas fa-history me-2\"></i>Status History</h6>\n                    </div>\n                    <div class=\"card-body\">\n                      <div v-if=\"currentRequest.status_history && currentRequest.status_history.length > 0\" class=\"timeline\">\n                        <div\n                          v-for=\"(history, index) in currentRequest.status_history\"\n                          :key=\"history.id\"\n                          class=\"timeline-item\"\n                          :class=\"{ 'timeline-item-last': index === currentRequest.status_history.length - 1 }\"\n                        >\n                          <div class=\"timeline-marker\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                            <i class=\"fas fa-circle\"></i>\n                          </div>\n                          <div class=\"timeline-content\">\n                            <div class=\"timeline-header\">\n                              <span class=\"badge\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                                {{ formatStatus(history.new_status_name) }}\n                              </span>\n                              <small class=\"text-muted ms-2\">{{ formatDateTime(history.changed_at) }}</small>\n                            </div>\n                            <div class=\"timeline-body\">\n                              <p class=\"mb-1\">\n                                <strong>Changed by:</strong> {{ history.changed_by_name }}\n                              </p>\n                              <p v-if=\"history.old_status_name\" class=\"mb-1\">\n                                <strong>From:</strong> {{ formatStatus(history.old_status_name) }}\n                              </p>\n                              <p v-if=\"history.change_reason\" class=\"mb-0\">\n                                <strong>Reason:</strong> {{ history.change_reason }}\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div v-else class=\"text-center text-muted py-3\">\n                        <i class=\"fas fa-history fa-2x mb-2\"></i>\n                        <p>No status history available</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"showRequestDetails = false\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Close\n                  </button>\n                  <button type=\"button\" class=\"btn btn-primary\" @click=\"refreshRequestDetails\">\n                    <i class=\"fas fa-sync-alt me-1\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Reject Modal -->\n          <div v-if=\"showQuickReject && selectedRequestForReject\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-times-circle text-danger me-2\"></i>\n                    Reject Request\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"closeQuickRejectModal\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"alert alert-warning\">\n                    <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                    You are about to reject this document request. This action will notify the client immediately.\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <strong>Request Details:</strong>\n                    <ul class=\"list-unstyled mt-2\">\n                      <li><strong>Request Number:</strong> {{ selectedRequestForReject.request_number }}</li>\n                      <li><strong>Document Type:</strong> {{ selectedRequestForReject.document_type }}</li>\n                      <li><strong>Client:</strong> {{ selectedRequestForReject.client_name }}</li>\n                    </ul>\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <label for=\"rejectionReason\" class=\"form-label\">\n                      <strong>Rejection Reason <span class=\"text-danger\">*</span></strong>\n                    </label>\n                    <textarea\n                      id=\"rejectionReason\"\n                      v-model=\"quickRejectForm.reason\"\n                      class=\"form-control\"\n                      rows=\"4\"\n                      placeholder=\"Please provide a clear reason for rejecting this request...\"\n                      :class=\"{ 'is-invalid': quickRejectForm.error }\"\n                    ></textarea>\n                    <div v-if=\"quickRejectForm.error\" class=\"invalid-feedback\">\n                      {{ quickRejectForm.error }}\n                    </div>\n                    <div class=\"form-text\">\n                      This reason will be visible to the client and included in their notification.\n                    </div>\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeQuickRejectModal\" :disabled=\"quickRejectForm.loading\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Cancel\n                  </button>\n                  <button type=\"button\" class=\"btn btn-danger\" @click=\"confirmQuickReject\" :disabled=\"quickRejectForm.loading || !quickRejectForm.reason.trim()\">\n                    <i class=\"fas fa-times-circle me-1\"></i>\n                    <span v-if=\"quickRejectForm.loading\">\n                      <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                      Rejecting...\n                    </span>\n                    <span v-else>Reject Request</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  </template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport notificationService from '@/services/notificationService';\n\nexport default {\n  name: 'AdminRequests',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      loading: true,\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      errorMessage: '',\n      viewMode: 'table', // 'card' or 'table' - default to table view\n\n      // Request Management Data\n      requests: [],\n      selectedRequests: [],\n      currentRequest: null,\n      statusOptions: [],\n\n      // Pagination\n      pagination: {\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n      },\n\n      // Filters\n      filters: {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      },\n\n      // Statistics\n      requestStats: {\n        total: 0,\n        pending: 0,\n        approved: 0,\n        completed: 0,\n        thisMonth: 0\n      },\n\n      // UI State\n      showFilters: false,\n      showBulkActions: false,\n      showRequestDetails: false,\n      showRejectForm: false,\n      showQuickReject: false,\n      bulkAction: '',\n      bulkReason: '',\n\n      // Status Update Forms\n      statusUpdateForm: {\n        status_id: '',\n        reason: ''\n      },\n      rejectForm: {\n        reason: ''\n      },\n      quickRejectForm: {\n        reason: '',\n        loading: false,\n        error: ''\n      },\n      selectedRequestForReject: null,\n\n      // Real-time features\n      refreshInterval: null,\n      autoRefreshEnabled: true,\n      refreshRate: 30000, // 30 seconds\n      lastRefresh: null\n    };\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load component data\n    await this.loadComponentData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load all component data\n    async loadComponentData() {\n      this.loading = true;\n      try {\n        await Promise.all([\n          this.loadAdminProfile(),\n          this.loadStatusOptions(),\n          this.loadRequests(),\n          this.loadDashboardStats()\n        ]);\n      } catch (error) {\n        console.error('Failed to load component data:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request data';\n\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Load status options\n    async loadStatusOptions() {\n      try {\n        const response = await adminDocumentService.getStatusOptions();\n        if (response.success) {\n          this.statusOptions = response.data || [];\n        }\n      } catch (error) {\n        console.error('Failed to load status options:', error);\n        this.statusOptions = [];\n      }\n    },\n\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        const response = await adminDocumentService.getDashboardStats();\n        if (response.success) {\n          this.requestStats = {\n            total: response.data.totalRequests || 0,\n            pending: response.data.pendingRequests || 0,\n            approved: response.data.approvedRequests || 0,\n            completed: response.data.completedRequests || 0,\n            thisMonth: response.data.todayRequests || 0\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load dashboard stats:', error);\n      }\n    },\n\n    // Load requests with current filters and pagination\n    async loadRequests() {\n      try {\n        const params = {\n          page: this.pagination.currentPage,\n          limit: this.pagination.itemsPerPage,\n          ...this.filters\n        };\n\n        const response = await adminDocumentService.getAllRequests(params);\n        if (response.success) {\n          this.requests = response.data.requests || [];\n          this.pagination = {\n            currentPage: response.data.pagination?.current_page || 1,\n            totalPages: response.data.pagination?.total_pages || 1,\n            totalItems: response.data.pagination?.total_records || 0,\n            itemsPerPage: response.data.pagination?.per_page || 10\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load requests';\n        this.requests = [];\n      }\n    },\n\n    // Filter and search methods\n    applyFilters() {\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    clearFilters() {\n      this.filters = {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      };\n      this.applyFilters();\n    },\n\n    // Pagination methods\n    changePage(page) {\n      if (page >= 1 && page <= this.pagination.totalPages) {\n        this.pagination.currentPage = page;\n        this.loadRequests();\n      }\n    },\n\n    changeItemsPerPage(itemsPerPage) {\n      this.pagination.itemsPerPage = itemsPerPage;\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    goBack() {\n      this.$router.push('/admin/dashboard');\n    },\n\n    // Request selection methods\n    toggleRequestSelection(requestId) {\n      const index = this.selectedRequests.indexOf(requestId);\n      if (index > -1) {\n        this.selectedRequests.splice(index, 1);\n      } else {\n        this.selectedRequests.push(requestId);\n      }\n    },\n\n    selectAllRequests() {\n      if (this.selectedRequests.length === this.requests.length) {\n        this.selectedRequests = [];\n      } else {\n        this.selectedRequests = this.requests.map(r => r.id);\n      }\n    },\n\n    // Request details\n    async viewRequestDetails(requestId) {\n      console.log('🚀 View details clicked for request ID:', requestId);\n\n      try {\n        const response = await adminDocumentService.getRequestDetails(requestId);\n        if (response.success) {\n          this.currentRequest = response.data;\n          this.showRequestDetails = true;\n          // Reset forms\n          this.statusUpdateForm = { status_id: '', reason: '' };\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n          console.log('📋 Request details loaded:', response.data);\n          this.showToast('Success', `Request details loaded for ${response.data.request_number}`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to load request details:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request details';\n        this.showToast('Error', 'Failed to load request details', 'error');\n      }\n    },\n\n    // Refresh request details\n    async refreshRequestDetails() {\n      if (this.currentRequest) {\n        await this.viewRequestDetails(this.currentRequest.id);\n      }\n    },\n\n    // Update request status from modal\n    async updateRequestStatusFromModal() {\n      if (!this.statusUpdateForm.status_id || !this.currentRequest) return;\n\n      try {\n        const response = await adminDocumentService.updateRequestStatus(\n          this.currentRequest.id,\n          {\n            status_id: parseInt(this.statusUpdateForm.status_id),\n            reason: this.statusUpdateForm.reason\n          }\n        );\n\n        if (response.success) {\n          // Refresh the request details\n          await this.refreshRequestDetails();\n          // Refresh the main requests list\n          await this.loadRequests();\n          // Reset form\n          this.statusUpdateForm = { status_id: '', reason: '' };\n\n          // Show success message\n          this.errorMessage = '';\n          // You could add a success message here if needed\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n\n    // Approve request from modal\n    async approveRequestFromModal() {\n      if (!this.currentRequest) return;\n\n      try {\n        const response = await adminDocumentService.approveRequest(\n          this.currentRequest.id,\n          { reason: 'Approved from request details' }\n        );\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n\n    // Reject request from modal\n    async rejectRequestFromModal() {\n      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.currentRequest.id,\n          { reason: this.rejectForm.reason }\n        );\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Status update methods\n    async updateRequestStatus(requestId, statusId, reason = '') {\n      try {\n        const response = await adminDocumentService.updateRequestStatus(requestId, {\n          status_id: statusId,\n          reason: reason\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n\n    async approveRequest(requestId, reason = '') {\n      try {\n        const response = await adminDocumentService.approveRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n\n    async rejectRequest(requestId, reason) {\n      if (!reason || reason.trim() === '') {\n        this.errorMessage = 'Rejection reason is required';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.rejectRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Quick approval/rejection methods\n    canApprove(request) {\n      // Can only approve requests that are not already approved or rejected\n      return ['pending', 'under_review', 'additional_info_required'].includes(request.status_name);\n    },\n\n    canReject(request) {\n      // Can only reject requests that are not already approved or rejected\n      // Once approved or rejected, the request status is final\n      return ['pending', 'under_review', 'additional_info_required'].includes(request.status_name);\n    },\n\n    async quickApprove(request) {\n      console.log('🚀 Quick approve clicked for request:', request);\n\n      try {\n        this.loading = true;\n        const response = await adminDocumentService.approveRequest(request.id, {\n          reason: 'Quick approval from admin interface'\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    showQuickRejectModal(request) {\n      console.log('🚀 Quick reject clicked for request:', request);\n\n      this.selectedRequestForReject = request;\n      this.quickRejectForm = {\n        reason: '',\n        loading: false,\n        error: ''\n      };\n      this.showQuickReject = true;\n    },\n\n    closeQuickRejectModal() {\n      this.showQuickReject = false;\n      this.selectedRequestForReject = null;\n      this.quickRejectForm = {\n        reason: '',\n        loading: false,\n        error: ''\n      };\n    },\n\n    async confirmQuickReject() {\n      if (!this.quickRejectForm.reason.trim()) {\n        this.quickRejectForm.error = 'Rejection reason is required';\n        return;\n      }\n\n      this.quickRejectForm.loading = true;\n      this.quickRejectForm.error = '';\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.selectedRequestForReject.id,\n          { reason: this.quickRejectForm.reason }\n        );\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');\n          this.closeQuickRejectModal();\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickRejectForm.error = errorData.message || 'Failed to reject request';\n      } finally {\n        this.quickRejectForm.loading = false;\n      }\n    },\n\n    // Bulk operations\n    async performBulkAction() {\n      if (this.selectedRequests.length === 0) {\n        this.errorMessage = 'Please select at least one request';\n        return;\n      }\n\n      if (!this.bulkAction) {\n        this.errorMessage = 'Please select a bulk action';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.bulkUpdateRequests({\n          request_ids: this.selectedRequests,\n          status_id: parseInt(this.bulkAction),\n          reason: this.bulkReason\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.selectedRequests = [];\n          this.bulkAction = '';\n          this.bulkReason = '';\n          this.showBulkActions = false;\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to perform bulk action:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to perform bulk action';\n      }\n    },\n\n    // Export functionality\n    async exportRequests() {\n      try {\n        const csvData = await adminDocumentService.exportRequests(this.filters);\n        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;\n        adminDocumentService.downloadCSV(csvData, filename);\n      } catch (error) {\n        console.error('Failed to export requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to export requests';\n      }\n    },\n\n    // Utility methods\n    formatStatus(status) {\n      return adminDocumentService.formatStatus(status);\n    },\n\n    getStatusColor(status) {\n      return adminDocumentService.getStatusColor(status);\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        style: 'currency',\n        currency: 'PHP'\n      }).format(amount || 0);\n    },\n\n    formatDateTime(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    formatTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminRequests');\n\n      try {\n        // Initialize notification service\n        await notificationService.init('admin');\n\n        // Listen for request-related notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('new_request', this.handleNewRequest);\n\n        // Start auto-refresh if enabled\n        if (this.autoRefreshEnabled) {\n          this.startAutoRefresh();\n        }\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminRequests');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('new_request', this.handleNewRequest);\n\n      // Cleanup (simplified)\n      notificationService.cleanup();\n\n      // Stop auto-refresh\n      this.stopAutoRefresh();\n    },\n\n    startAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n      }\n\n      this.refreshInterval = setInterval(() => {\n        if (this.autoRefreshEnabled && !this.loading) {\n          console.log('Auto-refreshing requests data...');\n          this.refreshRequestsData();\n        }\n      }, this.refreshRate);\n\n      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);\n    },\n\n    stopAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n        this.refreshInterval = null;\n        console.log('Auto-refresh stopped');\n      }\n    },\n\n    toggleAutoRefresh() {\n      this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n      if (this.autoRefreshEnabled) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n\n    async refreshRequestsData() {\n      try {\n        this.lastRefresh = new Date();\n\n        // Refresh requests list\n        await this.loadRequests();\n\n        // Refresh statistics\n        await this.loadDashboardStats();\n\n        // If request details modal is open, refresh it\n        if (this.showRequestDetails && this.currentRequest) {\n          await this.refreshRequestDetails();\n        }\n\n        console.log('Requests data refreshed successfully');\n      } catch (error) {\n        console.error('Failed to refresh requests data:', error);\n      }\n    },\n\n    handleRealTimeNotification(notification) {\n      console.log('Real-time notification received:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_updated':\n          this.handleRequestUpdate(notification.data);\n          break;\n        case 'unread_count_update':\n        case 'heartbeat':\n          // Polling system notifications - handled by notification service\n          break;\n        default:\n          // Only log unknown types, not system types\n          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {\n            console.log('Unhandled notification type:', notification.type);\n          }\n      }\n    },\n\n    handleStatusChange(data) {\n      console.log('Request status changed:', data);\n\n      // Update the request in the list if it exists\n      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);\n      if (requestIndex !== -1) {\n        // Refresh the specific request or reload all requests\n        this.refreshRequestsData();\n      }\n\n      // Show toast notification\n      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');\n    },\n\n    handleNewRequest(data) {\n      console.log('New request received:', data);\n\n      // Refresh requests to show the new request\n      this.refreshRequestsData();\n\n      // Show toast notification\n      this.showToast('New Request', `New ${data.document_type} request received`, 'success');\n    },\n\n    handleRequestUpdate(data) {\n      console.log('Request updated:', data);\n\n      // If the updated request is currently being viewed, refresh details\n      if (this.currentRequest && this.currentRequest.id === data.request_id) {\n        this.refreshRequestDetails();\n      }\n\n      // Refresh the requests list\n      this.refreshRequestsData();\n    },\n\n    showToast(title, message, type = 'info') {\n      // Log to console for debugging\n      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);\n\n      // Create a simple toast notification\n      const toast = document.createElement('div');\n      toast.className = `toast-notification toast-${type}`;\n      toast.innerHTML = `\n        <div class=\"toast-header\">\n          <strong>${title}</strong>\n          <button type=\"button\" class=\"toast-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>\n        </div>\n        <div class=\"toast-body\">${message}</div>\n      `;\n\n      // Add toast styles if not already added\n      if (!document.getElementById('toast-styles')) {\n        const styles = document.createElement('style');\n        styles.id = 'toast-styles';\n        styles.textContent = `\n          .toast-notification {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            min-width: 300px;\n            background: white;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n            z-index: 9999;\n            animation: slideIn 0.3s ease;\n          }\n          .toast-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 12px 16px 8px;\n            border-bottom: 1px solid #e9ecef;\n          }\n          .toast-body {\n            padding: 8px 16px 12px;\n            color: #6c757d;\n          }\n          .toast-close {\n            background: none;\n            border: none;\n            font-size: 18px;\n            cursor: pointer;\n            color: #6c757d;\n          }\n          .toast-success { border-left: 4px solid #28a745; }\n          .toast-error { border-left: 4px solid #dc3545; }\n          .toast-info { border-left: 4px solid #17a2b8; }\n          .toast-warning { border-left: 4px solid #ffc107; }\n          @keyframes slideIn {\n            from { transform: translateX(100%); opacity: 0; }\n            to { transform: translateX(0); opacity: 1; }\n          }\n        `;\n        document.head.appendChild(styles);\n      }\n\n      // Add toast to page\n      document.body.appendChild(toast);\n\n      // Auto-remove after 5 seconds\n      setTimeout(() => {\n        if (toast.parentElement) {\n          toast.style.animation = 'slideIn 0.3s ease reverse';\n          setTimeout(() => toast.remove(), 300);\n        }\n      }, 5000);\n    }\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* Additional styles specific to AdminRequests */\n.admin-requests {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);\n}\n\n/* Ensure proper spacing for request statistics cards */\n.card.border-left-primary {\n  border-left: 4px solid #3b82f6 !important;\n}\n\n.card.border-left-warning {\n  border-left: 4px solid #f59e0b !important;\n}\n\n.card.border-left-success {\n  border-left: 4px solid #059669 !important;\n}\n\n.card.border-left-info {\n  border-left: 4px solid #06b6d4 !important;\n}\n\n/* Bootstrap utility classes for compatibility */\n.text-xs {\n  font-size: 0.75rem !important;\n}\n\n.text-gray-800 {\n  color: #1f2937 !important;\n}\n\n.text-gray-300 {\n  color: #d1d5db !important;\n}\n\n.text-muted {\n  color: #6c757d !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.g-0 {\n  --bs-gutter-x: 0;\n  --bs-gutter-y: 0;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n/* Improve button spacing */\n.d-flex.gap-2 {\n  gap: 0.5rem !important;\n}\n\n/* Timeline Styles */\n.timeline {\n  position: relative;\n  padding-left: 2rem;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 1rem;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  background: #e3e6f0;\n}\n\n.timeline-item {\n  position: relative;\n  margin-bottom: 2rem;\n}\n\n.timeline-item:last-child {\n  margin-bottom: 0;\n}\n\n.timeline-item.timeline-item-last::after {\n  display: none;\n}\n\n.timeline-marker {\n  position: absolute;\n  left: -2rem;\n  top: 0.25rem;\n  width: 2rem;\n  height: 2rem;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  z-index: 1;\n}\n\n.timeline-content {\n  background: #f8f9fc;\n  border-radius: 8px;\n  padding: 1rem;\n  border-left: 3px solid #e3e6f0;\n}\n\n.timeline-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.timeline-body p {\n  margin-bottom: 0.25rem;\n  font-size: 0.875rem;\n}\n\n.timeline-body p:last-child {\n  margin-bottom: 0;\n}\n\n/* Modal Styles */\n.modal-xl {\n  max-width: 1200px;\n}\n\n.modal-dialog-scrollable .modal-body {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n/* Real-time status indicator styles */\n.real-time-status .badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 1rem;\n}\n\n.pulse {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* Card View Styles */\n.requests-grid {\n  min-height: 400px;\n}\n\n.empty-state {\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 3rem 2rem;\n  margin: 2rem 0;\n}\n\n.empty-state-icon {\n  opacity: 0.5;\n}\n\n.request-card {\n  background: #ffffff;\n  border: 1px solid #e3e6f0;\n  border-radius: 12px;\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.request-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);\n  border-color: #5a5c69;\n}\n\n.request-card.selected {\n  border-color: #4e73df;\n  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);\n}\n\n.request-card-header {\n  padding: 1rem 1.25rem 0.5rem;\n  border-bottom: 1px solid #f1f1f1;\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n}\n\n.request-card-body {\n  padding: 1.25rem;\n  flex-grow: 1;\n}\n\n.request-card-footer {\n  padding: 0.75rem 1.25rem 1.25rem;\n  background: #f8f9fa;\n  border-top: 1px solid #e3e6f0;\n}\n\n.client-avatar {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  color: white;\n  font-size: 1.2rem;\n}\n\n.client-info h6 {\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n.document-type {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n  border-left: 4px solid #17a2b8;\n}\n\n.document-type .badge {\n  font-size: 0.875rem;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n}\n\n.request-meta {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n}\n\n.meta-item {\n  text-align: center;\n}\n\n.meta-item small {\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.request-date {\n  padding-top: 0.75rem;\n  border-top: 1px solid #e9ecef;\n  margin-top: 0.75rem;\n}\n\n.request-actions .dropdown-toggle {\n  border: none;\n  background: transparent;\n  color: #6c757d;\n  padding: 0.25rem 0.5rem;\n  border-radius: 6px;\n}\n\n.request-actions .dropdown-toggle:hover {\n  background: #e9ecef;\n  color: #495057;\n}\n\n/* View Toggle Styles */\n.btn-check:checked + .btn-outline-primary {\n  background-color: #4e73df;\n  border-color: #4e73df;\n  color: white;\n}\n\n/* Badge Enhancements */\n.badge.bg-info-subtle {\n  background-color: #cff4fc !important;\n  color: #055160 !important;\n  border: 1px solid #b6effb;\n}\n\n/* Button Enhancements */\n.request-card-footer .btn {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.request-card-footer .btn:hover {\n  transform: translateY(-1px);\n}\n\n/* Modern Table Styles */\n.modern-table-container {\n  background: #ffffff;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: visible;\n  border: 1px solid #e8ecef;\n}\n\n.modern-table-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.modern-table-header h5 {\n  color: white;\n  margin: 0;\n  font-weight: 600;\n}\n\n.table-actions .btn {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.table-actions .btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n.modern-table-empty {\n  padding: 4rem 2rem;\n  text-align: center;\n  background: #f8f9fa;\n}\n\n.empty-content {\n  max-width: 400px;\n  margin: 0 auto;\n}\n\n.empty-icon {\n  font-size: 4rem;\n  color: #6c757d;\n  margin-bottom: 1.5rem;\n  opacity: 0.5;\n}\n\n.empty-title {\n  color: #495057;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n}\n\n.empty-text {\n  color: #6c757d;\n  margin: 0;\n}\n\n/* Compact Table Styles */\n.compact-table-wrapper {\n  background: white;\n  border-radius: 12px;\n  overflow: visible;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  border: 1px solid #e8ecef;\n}\n\n.compact-table-header {\n  display: grid;\n  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  gap: 0.5rem;\n}\n\n.header-cell {\n  display: flex;\n  align-items: center;\n  padding: 0 0.5rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  font-size: 0.75rem;\n}\n\n.selection-header {\n  justify-content: center;\n}\n\n.compact-table-body {\n  background: white;\n  overflow: visible;\n}\n\n.compact-row {\n  display: grid;\n  grid-template-columns: 40px 140px 1fr 140px 120px 100px 120px 120px;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-bottom: 1px solid #f1f3f4;\n  transition: all 0.15s ease;\n  position: relative;\n  min-height: 48px;\n  gap: 0.5rem;\n}\n\n.compact-row:hover {\n  background: #f8f9fa;\n  transform: translateX(2px);\n  box-shadow: 2px 0 0 #667eea;\n}\n\n.compact-row.selected {\n  background: #e3f2fd;\n  border-left: 3px solid #2196f3;\n}\n\n.compact-row:last-child {\n  border-bottom: none;\n}\n\n.row-cell {\n  display: flex;\n  align-items: center;\n  padding: 0 0.5rem;\n  font-size: 0.875rem;\n}\n\n/* Selection Cell */\n.selection-cell {\n  justify-content: center;\n}\n\n.selection-cell .form-check-input {\n  width: 18px;\n  height: 18px;\n  border-radius: 4px;\n  border: 2px solid #dee2e6;\n}\n\n.selection-cell .form-check-input:checked {\n  background-color: #667eea;\n  border-color: #667eea;\n}\n\n/* Request Number Cell */\n.request-number-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.request-number {\n  font-weight: 700;\n  color: #667eea;\n  font-size: 1rem;\n  letter-spacing: 0.5px;\n}\n\n.request-id {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Client Cell */\n.client-info {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.client-avatar-sm {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1rem;\n  flex-shrink: 0;\n}\n\n.client-details {\n  min-width: 0;\n  flex: 1;\n}\n\n.client-name {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.95rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email {\n  font-size: 0.8rem;\n  color: #6c757d;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-top: 2px;\n}\n\n/* Document Type Cell */\n.document-type-badge {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n/* Status Cell */\n.status-badge {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-indicator {\n  font-size: 0.6rem;\n  animation: pulse 2s infinite;\n}\n\n.status-success {\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\n}\n\n.status-warning {\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\n  color: #212529;\n  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);\n}\n\n.status-danger {\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);\n}\n\n.status-info {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n.status-secondary {\n  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);\n}\n\n/* Amount Cell */\n.amount-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.amount {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 1.1rem;\n}\n\n.currency {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Date Cell */\n.date-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.9rem;\n}\n\n.time {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Actions Cell */\n.action-buttons {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n}\n\n.action-btn {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.view-btn {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn:hover {\n  background: #bbdefb;\n  transform: translateY(-2px);\n}\n\n.approve-btn {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn:hover {\n  background: #c8e6c9;\n  transform: translateY(-2px);\n}\n\n.reject-btn {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn:hover {\n  background: #ffcdd2;\n  transform: translateY(-2px);\n}\n\n.more-btn {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn:hover {\n  background: #e0e0e0;\n  transform: translateY(-2px);\n}\n\n.more-btn::after {\n  display: none;\n}\n\n/* Dropdown positioning fixes */\n.modern-table {\n  overflow: visible;\n}\n\n.table-row {\n  overflow: visible;\n}\n\n.action-buttons .dropdown {\n  position: static;\n}\n\n.action-buttons .dropdown-menu {\n  position: absolute !important;\n  top: 100% !important;\n  right: 0 !important;\n  left: auto !important;\n  z-index: 1050 !important;\n  transform: none !important;\n  margin-top: 0.25rem;\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 8px;\n  background: white;\n  min-width: 160px;\n}\n\n.action-buttons .dropdown-menu.show {\n  display: block !important;\n}\n\n/* Ensure dropdown appears above other elements */\n.action-buttons .dropdown.show {\n  z-index: 1051;\n}\n\n/* Pagination Container */\n.pagination-container {\n  background: white;\n  border-radius: 0 0 16px 16px;\n  padding: 1.5rem 2rem;\n  border-top: 1px solid #f1f3f4;\n  margin-top: -1px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.pagination-container .pagination {\n  margin: 0;\n}\n\n.pagination-container .page-link {\n  border: 1px solid #e3e6f0;\n  color: #667eea;\n  padding: 0.5rem 0.75rem;\n  margin: 0 2px;\n  border-radius: 8px;\n  transition: all 0.2s ease;\n}\n\n.pagination-container .page-link:hover {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  transform: translateY(-1px);\n}\n\n.pagination-container .page-item.active .page-link {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n\n.pagination-container .page-item.disabled .page-link {\n  color: #6c757d;\n  background: #f8f9fa;\n  border-color: #e3e6f0;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .d-flex.gap-2 .btn {\n    margin-bottom: 0.5rem;\n  }\n\n  .modal-xl {\n    max-width: 95%;\n    margin: 1rem auto;\n  }\n\n  .timeline {\n    padding-left: 1.5rem;\n  }\n\n  .timeline-marker {\n    left: -1.5rem;\n    width: 1.5rem;\n    height: 1.5rem;\n    font-size: 0.625rem;\n  }\n\n  /* Compact table mobile adjustments */\n  .compact-table-header {\n    display: none;\n  }\n\n  .compact-row {\n    grid-template-columns: 1fr;\n    padding: 1rem;\n    gap: 0.75rem;\n    border-radius: 8px;\n    margin-bottom: 0.75rem;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  }\n\n  .row-cell {\n    min-height: auto;\n    flex-direction: column;\n    align-items: flex-start;\n    padding: 0.25rem 0;\n    border-bottom: 1px solid #f1f3f4;\n  }\n\n  .row-cell:last-child {\n    border-bottom: none;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .client-info {\n    width: 100%;\n  }\n\n  .client-details {\n    flex: 1;\n  }\n\n  .document-type-badge,\n  .status-badge {\n    align-self: flex-start;\n  }\n\n  .amount-content,\n  .date-content {\n    align-items: flex-start;\n  }\n\n  .action-buttons {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .action-btn {\n    flex: 1;\n    max-width: 60px;\n  }\n\n  /* Mobile fixes for simple actions */\n  .actions-simple {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  .request-actions-simple {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  /* Card view mobile adjustments */\n  .request-card {\n    margin-bottom: 1rem;\n  }\n\n  .request-card-header,\n  .request-card-body,\n  .request-card-footer {\n    padding: 1rem;\n  }\n\n  .client-info .d-flex {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.5rem;\n  }\n\n  .client-avatar {\n    align-self: center;\n  }\n\n  .request-meta .row {\n    text-align: center;\n  }\n\n  .request-card-footer .d-flex {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .request-card-footer .btn {\n    width: 100%;\n  }\n\n  /* View toggle mobile */\n  .btn-group {\n    width: 100%;\n  }\n\n  .btn-group .btn {\n    flex: 1;\n  }\n}\n\n@media (max-width: 576px) {\n  .empty-state {\n    padding: 2rem 1rem;\n  }\n\n  .empty-state-icon {\n    font-size: 3rem;\n  }\n\n  .request-card-body {\n    padding: 1rem;\n  }\n\n  .document-type,\n  .request-meta {\n    padding: 0.5rem;\n  }\n}\n\n/* Compact Table Additional Styles */\n.document-badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  box-shadow: 0 1px 4px rgba(23, 162, 184, 0.3);\n}\n\n.client-compact {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  width: 100%;\n}\n\n.client-avatar-tiny {\n  width: 28px;\n  height: 28px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  flex-shrink: 0;\n}\n\n.client-info-compact {\n  flex: 1;\n  min-width: 0;\n}\n\n.client-name-compact {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.8rem;\n  line-height: 1.2;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email-compact {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.request-id-small {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n}\n\n.status-compact {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-compact i {\n  font-size: 0.5rem;\n}\n\n.amount-compact {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 0.85rem;\n}\n\n.date-compact {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date-main {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.8rem;\n  line-height: 1.2;\n}\n\n.time-small {\n  font-size: 0.7rem;\n  color: #6c757d;\n  margin-top: 1px;\n}\n\n.actions-compact {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n/* Fixed Actions Layout */\n.actions-compact-fixed {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  min-width: 120px;\n}\n\n.primary-actions {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n\n.dropdown-wrapper .dropdown {\n  position: static;\n}\n\n.compact-dropdown {\n  z-index: 1050 !important;\n}\n\n.compact-dropdown .dropdown-item {\n  padding: 0.5rem 1rem !important;\n  font-size: 0.875rem !important;\n  white-space: nowrap !important;\n  display: flex !important;\n  align-items: center !important;\n}\n\n.compact-dropdown .dropdown-item:hover {\n  background-color: #f8f9fa !important;\n}\n\n.compact-dropdown .dropdown-divider {\n  margin: 0.25rem 0 !important;\n}\n\n/* Ensure dropdown appears above table rows */\n.compact-row {\n  position: relative;\n  z-index: 1;\n}\n\n.compact-row:hover {\n  z-index: 2;\n}\n\n.dropdown-wrapper .dropdown.show {\n  z-index: 1051 !important;\n}\n\n.compact-dropdown.show {\n  display: block !important;\n}\n\n.action-btn-sm {\n  width: 28px;\n  height: 28px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.75rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.view-btn-sm {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn-sm:hover {\n  background: #bbdefb;\n  transform: translateY(-1px);\n}\n\n.approve-btn-sm {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn-sm:hover {\n  background: #c8e6c9;\n  transform: translateY(-1px);\n}\n\n.reject-btn-sm {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn-sm:hover {\n  background: #ffcdd2;\n  transform: translateY(-1px);\n}\n\n.more-btn-sm {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn-sm:hover {\n  background: #e0e0e0;\n  transform: translateY(-1px);\n}\n\n.more-btn-sm::after {\n  display: none;\n}\n\n/* Simple Actions Layout */\n.actions-simple {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  justify-content: center;\n}\n\n.request-actions-simple {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n</style>\n"], "mappings": ";;;AAo/BA,OAAOA,WAAU,MAAO,mBAAmB;AAC3C,OAAOC,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,gBAAe,MAAO,6BAA6B;AAC1D,OAAOC,oBAAmB,MAAO,iCAAiC;AAClE,OAAOC,mBAAkB,MAAO,gCAAgC;AAEhE,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVN,WAAW;IACXC;EACF,CAAC;EAEDM,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACbC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE,OAAO;MAAE;;MAEnB;MACAC,QAAQ,EAAE,EAAE;MACZC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE,EAAE;MAEjB;MACAC,UAAU,EAAE;QACVC,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,CAAC;QACbC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC;MAED;MACAC,OAAO,EAAE;QACPC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,EAAE;QACjBC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE;MACX,CAAC;MAED;MACAC,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE;MACb,CAAC;MAED;MACAC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE,KAAK;MACtBC,kBAAkB,EAAE,KAAK;MACzBC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,KAAK;MACtBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MAEd;MACAC,gBAAgB,EAAE;QAChBC,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE;MACV,CAAC;MACDC,UAAU,EAAE;QACVD,MAAM,EAAE;MACV,CAAC;MACDE,eAAe,EAAE;QACfF,MAAM,EAAE,EAAE;QACVtC,OAAO,EAAE,KAAK;QACdyC,KAAK,EAAE;MACT,CAAC;MACDC,wBAAwB,EAAE,IAAI;MAE9B;MACAC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,WAAW,EAAE,KAAK;MAAE;MACpBC,WAAW,EAAE;IACf,CAAC;EACH,CAAC;EAED,MAAMC,OAAOA,CAAA,EAAG;IACd;IACA,IAAI,CAACrD,gBAAgB,CAACsD,UAAU,CAAC,CAAC,EAAE;MAClC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;MACjC;IACF;;IAEA;IACA,IAAI,CAACC,YAAY,CAAC,CAAC;;IAEnB;IACA,MAAM,IAAI,CAACC,iBAAiB,CAAC,CAAC;;IAE9B;IACA,IAAI,CAACC,0BAA0B,CAAC,CAAC;EACnC,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,YAAY,CAAC;IACzD;;IAEA;IACA,IAAI,CAACG,uBAAuB,CAAC,CAAC;EAChC,CAAC;EAEDC,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;MAC7B,IAAIA,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,OAAO;MACjD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,sBAAsB,CAAC,EAAE,OAAO,UAAU;MAC5D,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,OAAO,WAAW;IACpB;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACAb,YAAYA,CAAA,EAAG;MACb,IAAI,CAAChD,QAAO,GAAIqD,MAAM,CAACS,UAAS,IAAK,GAAG;;MAExC;MACA,IAAI,CAAC,IAAI,CAAC9D,QAAQ,EAAE;QAClB,MAAM+D,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAACnE,gBAAe,GAAIiE,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;MAC3D,OAAO;QACL,IAAI,CAACjE,gBAAe,GAAI,IAAI,EAAE;MAChC;;MAEA;MACA,IAAI,CAACsD,YAAW,GAAI,MAAM;QACxB,MAAMgB,SAAQ,GAAI,IAAI,CAACpE,QAAQ;QAC/B,IAAI,CAACA,QAAO,GAAIqD,MAAM,CAACS,UAAS,IAAK,GAAG;QAExC,IAAI,IAAI,CAAC9D,QAAO,IAAK,CAACoE,SAAS,EAAE;UAC/B,IAAI,CAACtE,gBAAe,GAAI,IAAI,EAAE;QAChC,OAAO,IAAI,CAAC,IAAI,CAACE,QAAO,IAAKoE,SAAS,EAAE;UACtC;UACA,MAAML,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;UAC3D,IAAI,CAACnE,gBAAe,GAAIiE,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;QAC3D;MACF,CAAC;MACDV,MAAM,CAACgB,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACjB,YAAY,CAAC;IACtD,CAAC;IAED;IACAkB,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACxE,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;MAC9CkE,YAAY,CAACO,OAAO,CAAC,uBAAuB,EAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAAC1E,gBAAgB,CAAC,CAAC;IACtF,CAAC;IAED;IACA2E,gBAAgBA,CAACC,IAAI,EAAE;MACrB,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,cAAc;QACvB,UAAU,EAAE,iBAAiB;QAC7B,SAAS,EAAE,gBAAgB;QAC3B,UAAU,EAAE,iBAAiB;QAC7B,UAAU,EAAE,sBAAsB;QAClC,SAAS,EAAE;MACb,CAAC;;MAED;MACA,IAAI,IAAI,CAAC3E,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;MAEA,IAAI6E,MAAM,CAACD,IAAI,CAAC,EAAE;QAChB,IAAI,CAAC5B,OAAO,CAACC,IAAI,CAAC4B,MAAM,CAACD,IAAI,CAAC,CAAC;MACjC;IACF,CAAC;IAED;IACAE,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAAC7E,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED;IACA8E,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAIA,MAAK,KAAM,SAAS,EAAE;QACxB,IAAI,CAAChC,OAAO,CAACC,IAAI,CAAC,gBAAgB,CAAC;MACrC,OAAO,IAAI+B,MAAK,KAAM,UAAU,EAAE;QAChC,IAAI,CAAChC,OAAO,CAACC,IAAI,CAAC,iBAAiB,CAAC;MACtC;MACA,IAAI,CAAChD,gBAAe,GAAI,KAAK;IAC/B,CAAC;IAED;IACAgF,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC/E,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;IACF,CAAC;IAED;IACAkF,YAAYA,CAAA,EAAG;MACbzF,gBAAgB,CAAC0F,MAAM,CAAC,CAAC;MACzB,IAAI,CAACnC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;IACA,MAAMmC,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAMC,QAAO,GAAI,MAAM5F,gBAAgB,CAAC6F,UAAU,CAAC,CAAC;QACpD,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACpF,SAAQ,GAAIkF,QAAQ,CAACvF,IAAI;QAChC;MACF,EAAE,OAAO0C,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACrC,SAAQ,GAAIV,gBAAgB,CAACgG,YAAY,CAAC,CAAC;MAClD;IACF,CAAC;IAED;IACA,MAAMtC,iBAAiBA,CAAA,EAAG;MACxB,IAAI,CAACpD,OAAM,GAAI,IAAI;MACnB,IAAI;QACF,MAAM2F,OAAO,CAACC,GAAG,CAAC,CAChB,IAAI,CAACP,gBAAgB,CAAC,CAAC,EACvB,IAAI,CAACQ,iBAAiB,CAAC,CAAC,EACxB,IAAI,CAACC,YAAY,CAAC,CAAC,EACnB,IAAI,CAACC,kBAAkB,CAAC,EACzB,CAAC;MACJ,EAAE,OAAOtD,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,6BAA6B;QAEtE,IAAIF,SAAS,CAAC/E,MAAK,KAAM,GAAG,EAAE;UAC5BvB,gBAAgB,CAAC0F,MAAM,CAAC,CAAC;UACzB,IAAI,CAACnC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC;QACnC;MACF,UAAU;QACR,IAAI,CAAClD,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA,MAAM6F,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF,MAAMP,QAAO,GAAI,MAAM3F,oBAAoB,CAACwG,gBAAgB,CAAC,CAAC;QAC9D,IAAIb,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC9E,aAAY,GAAI4E,QAAQ,CAACvF,IAAG,IAAK,EAAE;QAC1C;MACF,EAAE,OAAO0C,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC/B,aAAY,GAAI,EAAE;MACzB;IACF,CAAC;IAED;IACA,MAAMqF,kBAAkBA,CAAA,EAAG;MACzB,IAAI;QACF,MAAMT,QAAO,GAAI,MAAM3F,oBAAoB,CAACyG,iBAAiB,CAAC,CAAC;QAC/D,IAAId,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACjE,YAAW,GAAI;YAClBC,KAAK,EAAE8D,QAAQ,CAACvF,IAAI,CAACsG,aAAY,IAAK,CAAC;YACvC5E,OAAO,EAAE6D,QAAQ,CAACvF,IAAI,CAACuG,eAAc,IAAK,CAAC;YAC3C5E,QAAQ,EAAE4D,QAAQ,CAACvF,IAAI,CAACwG,gBAAe,IAAK,CAAC;YAC7C5E,SAAS,EAAE2D,QAAQ,CAACvF,IAAI,CAACyG,iBAAgB,IAAK,CAAC;YAC/C5E,SAAS,EAAE0D,QAAQ,CAACvF,IAAI,CAAC0G,aAAY,IAAK;UAC5C,CAAC;QACH;MACF,EAAE,OAAOhE,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IACF,CAAC;IAED;IACA,MAAMqD,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF,MAAMY,MAAK,GAAI;UACbC,IAAI,EAAE,IAAI,CAAChG,UAAU,CAACC,WAAW;UACjCgG,KAAK,EAAE,IAAI,CAACjG,UAAU,CAACI,YAAY;UACnC,GAAG,IAAI,CAACC;QACV,CAAC;QAED,MAAMsE,QAAO,GAAI,MAAM3F,oBAAoB,CAACkH,cAAc,CAACH,MAAM,CAAC;QAClE,IAAIpB,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACjF,QAAO,GAAI+E,QAAQ,CAACvF,IAAI,CAACQ,QAAO,IAAK,EAAE;UAC5C,IAAI,CAACI,UAAS,GAAI;YAChBC,WAAW,EAAE0E,QAAQ,CAACvF,IAAI,CAACY,UAAU,EAAEmG,YAAW,IAAK,CAAC;YACxDjG,UAAU,EAAEyE,QAAQ,CAACvF,IAAI,CAACY,UAAU,EAAEoG,WAAU,IAAK,CAAC;YACtDjG,UAAU,EAAEwE,QAAQ,CAACvF,IAAI,CAACY,UAAU,EAAEqG,aAAY,IAAK,CAAC;YACxDjG,YAAY,EAAEuE,QAAQ,CAACvF,IAAI,CAACY,UAAU,EAAEsG,QAAO,IAAK;UACtD,CAAC;QACH;MACF,EAAE,OAAOxE,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,yBAAyB;QAClE,IAAI,CAAC3F,QAAO,GAAI,EAAE;MACpB;IACF,CAAC;IAED;IACA2G,YAAYA,CAAA,EAAG;MACb,IAAI,CAACvG,UAAU,CAACC,WAAU,GAAI,CAAC;MAC/B,IAAI,CAACkF,YAAY,CAAC,CAAC;IACrB,CAAC;IAEDqB,YAAYA,CAAA,EAAG;MACb,IAAI,CAACnG,OAAM,GAAI;QACbC,MAAM,EAAE,EAAE;QACVC,aAAa,EAAE,EAAE;QACjBC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE;MACX,CAAC;MACD,IAAI,CAAC4F,YAAY,CAAC,CAAC;IACrB,CAAC;IAED;IACAE,UAAUA,CAACT,IAAI,EAAE;MACf,IAAIA,IAAG,IAAK,KAAKA,IAAG,IAAK,IAAI,CAAChG,UAAU,CAACE,UAAU,EAAE;QACnD,IAAI,CAACF,UAAU,CAACC,WAAU,GAAI+F,IAAI;QAClC,IAAI,CAACb,YAAY,CAAC,CAAC;MACrB;IACF,CAAC;IAEDuB,kBAAkBA,CAACtG,YAAY,EAAE;MAC/B,IAAI,CAACJ,UAAU,CAACI,YAAW,GAAIA,YAAY;MAC3C,IAAI,CAACJ,UAAU,CAACC,WAAU,GAAI,CAAC;MAC/B,IAAI,CAACkF,YAAY,CAAC,CAAC;IACrB,CAAC;IAEDwB,MAAMA,CAAA,EAAG;MACP,IAAI,CAACrE,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IACvC,CAAC;IAED;IACAqE,sBAAsBA,CAACC,SAAS,EAAE;MAChC,MAAMC,KAAI,GAAI,IAAI,CAACjH,gBAAgB,CAACkH,OAAO,CAACF,SAAS,CAAC;MACtD,IAAIC,KAAI,GAAI,CAAC,CAAC,EAAE;QACd,IAAI,CAACjH,gBAAgB,CAACmH,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACxC,OAAO;QACL,IAAI,CAACjH,gBAAgB,CAAC0C,IAAI,CAACsE,SAAS,CAAC;MACvC;IACF,CAAC;IAEDI,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAACpH,gBAAgB,CAACqH,MAAK,KAAM,IAAI,CAACtH,QAAQ,CAACsH,MAAM,EAAE;QACzD,IAAI,CAACrH,gBAAe,GAAI,EAAE;MAC5B,OAAO;QACL,IAAI,CAACA,gBAAe,GAAI,IAAI,CAACD,QAAQ,CAACuH,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACC,EAAE,CAAC;MACtD;IACF,CAAC;IAED;IACA,MAAMC,kBAAkBA,CAACT,SAAS,EAAE;MAClC/B,OAAO,CAACyC,GAAG,CAAC,yCAAyC,EAAEV,SAAS,CAAC;MAEjE,IAAI;QACF,MAAMlC,QAAO,GAAI,MAAM3F,oBAAoB,CAACwI,iBAAiB,CAACX,SAAS,CAAC;QACxE,IAAIlC,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC/E,cAAa,GAAI6E,QAAQ,CAACvF,IAAI;UACnC,IAAI,CAACgC,kBAAiB,GAAI,IAAI;UAC9B;UACA,IAAI,CAACK,gBAAe,GAAI;YAAEC,SAAS,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UACrD,IAAI,CAACC,UAAS,GAAI;YAAED,MAAM,EAAE;UAAG,CAAC;UAChC,IAAI,CAACN,cAAa,GAAI,KAAK;UAC3ByD,OAAO,CAACyC,GAAG,CAAC,4BAA4B,EAAE5C,QAAQ,CAACvF,IAAI,CAAC;UACxD,IAAI,CAACqI,SAAS,CAAC,SAAS,EAAE,8BAA8B9C,QAAQ,CAACvF,IAAI,CAACsI,cAAc,EAAE,EAAE,SAAS,CAAC;QACpG;MACF,EAAE,OAAO5F,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,gCAAgC;QACzE,IAAI,CAACkC,SAAS,CAAC,OAAO,EAAE,gCAAgC,EAAE,OAAO,CAAC;MACpE;IACF,CAAC;IAED;IACA,MAAME,qBAAqBA,CAAA,EAAG;MAC5B,IAAI,IAAI,CAAC7H,cAAc,EAAE;QACvB,MAAM,IAAI,CAACwH,kBAAkB,CAAC,IAAI,CAACxH,cAAc,CAACuH,EAAE,CAAC;MACvD;IACF,CAAC;IAED;IACA,MAAMO,4BAA4BA,CAAA,EAAG;MACnC,IAAI,CAAC,IAAI,CAACnG,gBAAgB,CAACC,SAAQ,IAAK,CAAC,IAAI,CAAC5B,cAAc,EAAE;MAE9D,IAAI;QACF,MAAM6E,QAAO,GAAI,MAAM3F,oBAAoB,CAAC6I,mBAAmB,CAC7D,IAAI,CAAC/H,cAAc,CAACuH,EAAE,EACtB;UACE3F,SAAS,EAAEoG,QAAQ,CAAC,IAAI,CAACrG,gBAAgB,CAACC,SAAS,CAAC;UACpDC,MAAM,EAAE,IAAI,CAACF,gBAAgB,CAACE;QAChC,CACF,CAAC;QAED,IAAIgD,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAM,IAAI,CAAC8C,qBAAqB,CAAC,CAAC;UAClC;UACA,MAAM,IAAI,CAACxC,YAAY,CAAC,CAAC;UACzB;UACA,IAAI,CAAC1D,gBAAe,GAAI;YAAEC,SAAS,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;;UAErD;UACA,IAAI,CAACjC,YAAW,GAAI,EAAE;UACtB;QACF;MACF,EAAE,OAAOoC,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,iCAAiC;MAC5E;IACF,CAAC;IAED;IACA,MAAMwC,uBAAuBA,CAAA,EAAG;MAC9B,IAAI,CAAC,IAAI,CAACjI,cAAc,EAAE;MAE1B,IAAI;QACF,MAAM6E,QAAO,GAAI,MAAM3F,oBAAoB,CAACgJ,cAAc,CACxD,IAAI,CAAClI,cAAc,CAACuH,EAAE,EACtB;UAAE1F,MAAM,EAAE;QAAgC,CAC5C,CAAC;QAED,IAAIgD,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAAC8C,qBAAqB,CAAC,CAAC;UAClC,MAAM,IAAI,CAACxC,YAAY,CAAC,CAAC;QAC3B;MACF,EAAE,OAAOrD,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,2BAA2B;MACtE;IACF,CAAC;IAED;IACA,MAAM0C,sBAAsBA,CAAA,EAAG;MAC7B,IAAI,CAAC,IAAI,CAACnI,cAAa,IAAK,CAAC,IAAI,CAAC8B,UAAU,CAACD,MAAM,CAACuG,IAAI,CAAC,CAAC,EAAE;MAE5D,IAAI;QACF,MAAMvD,QAAO,GAAI,MAAM3F,oBAAoB,CAACmJ,aAAa,CACvD,IAAI,CAACrI,cAAc,CAACuH,EAAE,EACtB;UAAE1F,MAAM,EAAE,IAAI,CAACC,UAAU,CAACD;QAAO,CACnC,CAAC;QAED,IAAIgD,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAAC8C,qBAAqB,CAAC,CAAC;UAClC,MAAM,IAAI,CAACxC,YAAY,CAAC,CAAC;UACzB,IAAI,CAACvD,UAAS,GAAI;YAAED,MAAM,EAAE;UAAG,CAAC;UAChC,IAAI,CAACN,cAAa,GAAI,KAAK;QAC7B;MACF,EAAE,OAAOS,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,0BAA0B;MACrE;IACF,CAAC;IAED;IACA,MAAMsC,mBAAmBA,CAAChB,SAAS,EAAEuB,QAAQ,EAAEzG,MAAK,GAAI,EAAE,EAAE;MAC1D,IAAI;QACF,MAAMgD,QAAO,GAAI,MAAM3F,oBAAoB,CAAC6I,mBAAmB,CAAChB,SAAS,EAAE;UACzEnF,SAAS,EAAE0G,QAAQ;UACnBzG,MAAM,EAAEA;QACV,CAAC,CAAC;QAEF,IAAIgD,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACM,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAAC1F,YAAW,GAAI,EAAE;QACxB;MACF,EAAE,OAAOoC,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,iCAAiC;MAC5E;IACF,CAAC;IAED,MAAMyC,cAAcA,CAACnB,SAAS,EAAElF,MAAK,GAAI,EAAE,EAAE;MAC3C,IAAI;QACF,MAAMgD,QAAO,GAAI,MAAM3F,oBAAoB,CAACgJ,cAAc,CAACnB,SAAS,EAAE;UAAElF;QAAO,CAAC,CAAC;QACjF,IAAIgD,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACM,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAAC1F,YAAW,GAAI,EAAE;QACxB;MACF,EAAE,OAAOoC,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,2BAA2B;MACtE;IACF,CAAC;IAED,MAAM4C,aAAaA,CAACtB,SAAS,EAAElF,MAAM,EAAE;MACrC,IAAI,CAACA,MAAK,IAAKA,MAAM,CAACuG,IAAI,CAAC,MAAM,EAAE,EAAE;QACnC,IAAI,CAACxI,YAAW,GAAI,8BAA8B;QAClD;MACF;MAEA,IAAI;QACF,MAAMiF,QAAO,GAAI,MAAM3F,oBAAoB,CAACmJ,aAAa,CAACtB,SAAS,EAAE;UAAElF;QAAO,CAAC,CAAC;QAChF,IAAIgD,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACM,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAAC1F,YAAW,GAAI,EAAE;QACxB;MACF,EAAE,OAAOoC,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,0BAA0B;MACrE;IACF,CAAC;IAED;IACA8C,UAAUA,CAACC,OAAO,EAAE;MAClB;MACA,OAAO,CAAC,SAAS,EAAE,cAAc,EAAE,0BAA0B,CAAC,CAAClF,QAAQ,CAACkF,OAAO,CAACC,WAAW,CAAC;IAC9F,CAAC;IAEDC,SAASA,CAACF,OAAO,EAAE;MACjB;MACA;MACA,OAAO,CAAC,SAAS,EAAE,cAAc,EAAE,0BAA0B,CAAC,CAAClF,QAAQ,CAACkF,OAAO,CAACC,WAAW,CAAC;IAC9F,CAAC;IAED,MAAME,YAAYA,CAACH,OAAO,EAAE;MAC1BxD,OAAO,CAACyC,GAAG,CAAC,uCAAuC,EAAEe,OAAO,CAAC;MAE7D,IAAI;QACF,IAAI,CAACjJ,OAAM,GAAI,IAAI;QACnB,MAAMsF,QAAO,GAAI,MAAM3F,oBAAoB,CAACgJ,cAAc,CAACM,OAAO,CAACjB,EAAE,EAAE;UACrE1F,MAAM,EAAE;QACV,CAAC,CAAC;QAEF,IAAIgD,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACM,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACqC,SAAS,CAAC,SAAS,EAAE,WAAWa,OAAO,CAACZ,cAAc,wBAAwB,EAAE,SAAS,CAAC;QACjG;MACF,EAAE,OAAO5F,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAAC2F,SAAS,CAAC,OAAO,EAAEpC,SAAS,CAACE,OAAM,IAAK,2BAA2B,EAAE,OAAO,CAAC;MACpF,UAAU;QACR,IAAI,CAAClG,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAEDqJ,oBAAoBA,CAACJ,OAAO,EAAE;MAC5BxD,OAAO,CAACyC,GAAG,CAAC,sCAAsC,EAAEe,OAAO,CAAC;MAE5D,IAAI,CAACvG,wBAAuB,GAAIuG,OAAO;MACvC,IAAI,CAACzG,eAAc,GAAI;QACrBF,MAAM,EAAE,EAAE;QACVtC,OAAO,EAAE,KAAK;QACdyC,KAAK,EAAE;MACT,CAAC;MACD,IAAI,CAACR,eAAc,GAAI,IAAI;IAC7B,CAAC;IAEDqH,qBAAqBA,CAAA,EAAG;MACtB,IAAI,CAACrH,eAAc,GAAI,KAAK;MAC5B,IAAI,CAACS,wBAAuB,GAAI,IAAI;MACpC,IAAI,CAACF,eAAc,GAAI;QACrBF,MAAM,EAAE,EAAE;QACVtC,OAAO,EAAE,KAAK;QACdyC,KAAK,EAAE;MACT,CAAC;IACH,CAAC;IAED,MAAM8G,kBAAkBA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAC/G,eAAe,CAACF,MAAM,CAACuG,IAAI,CAAC,CAAC,EAAE;QACvC,IAAI,CAACrG,eAAe,CAACC,KAAI,GAAI,8BAA8B;QAC3D;MACF;MAEA,IAAI,CAACD,eAAe,CAACxC,OAAM,GAAI,IAAI;MACnC,IAAI,CAACwC,eAAe,CAACC,KAAI,GAAI,EAAE;MAE/B,IAAI;QACF,MAAM6C,QAAO,GAAI,MAAM3F,oBAAoB,CAACmJ,aAAa,CACvD,IAAI,CAACpG,wBAAwB,CAACsF,EAAE,EAChC;UAAE1F,MAAM,EAAE,IAAI,CAACE,eAAe,CAACF;QAAO,CACxC,CAAC;QAED,IAAIgD,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACM,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACqC,SAAS,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC1F,wBAAwB,CAAC2F,cAAc,wBAAwB,EAAE,SAAS,CAAC;UACrH,IAAI,CAACiB,qBAAqB,CAAC,CAAC;QAC9B;MACF,EAAE,OAAO7G,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACD,eAAe,CAACC,KAAI,GAAIuD,SAAS,CAACE,OAAM,IAAK,0BAA0B;MAC9E,UAAU;QACR,IAAI,CAAC1D,eAAe,CAACxC,OAAM,GAAI,KAAK;MACtC;IACF,CAAC;IAED;IACA,MAAMwJ,iBAAiBA,CAAA,EAAG;MACxB,IAAI,IAAI,CAAChJ,gBAAgB,CAACqH,MAAK,KAAM,CAAC,EAAE;QACtC,IAAI,CAACxH,YAAW,GAAI,oCAAoC;QACxD;MACF;MAEA,IAAI,CAAC,IAAI,CAAC6B,UAAU,EAAE;QACpB,IAAI,CAAC7B,YAAW,GAAI,6BAA6B;QACjD;MACF;MAEA,IAAI;QACF,MAAMiF,QAAO,GAAI,MAAM3F,oBAAoB,CAAC8J,kBAAkB,CAAC;UAC7DC,WAAW,EAAE,IAAI,CAAClJ,gBAAgB;UAClC6B,SAAS,EAAEoG,QAAQ,CAAC,IAAI,CAACvG,UAAU,CAAC;UACpCI,MAAM,EAAE,IAAI,CAACH;QACf,CAAC,CAAC;QAEF,IAAImD,QAAQ,CAACE,OAAO,EAAE;UACpB,MAAM,IAAI,CAACM,YAAY,CAAC,CAAC;UACzB,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAC/B,IAAI,CAACvF,gBAAe,GAAI,EAAE;UAC1B,IAAI,CAAC0B,UAAS,GAAI,EAAE;UACpB,IAAI,CAACC,UAAS,GAAI,EAAE;UACpB,IAAI,CAACL,eAAc,GAAI,KAAK;UAC5B,IAAI,CAACzB,YAAW,GAAI,EAAE;QACxB;MACF,EAAE,OAAOoC,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,+BAA+B;MAC1E;IACF,CAAC;IAED;IACA,MAAMyD,cAAcA,CAAA,EAAG;MACrB,IAAI;QACF,MAAMC,OAAM,GAAI,MAAMjK,oBAAoB,CAACgK,cAAc,CAAC,IAAI,CAAC3I,OAAO,CAAC;QACvE,MAAM6I,QAAO,GAAI,qBAAqB,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;QAClFrK,oBAAoB,CAACsK,WAAW,CAACL,OAAO,EAAEC,QAAQ,CAAC;MACrD,EAAE,OAAOpH,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMuD,SAAQ,GAAIrG,oBAAoB,CAACsG,UAAU,CAACxD,KAAK,CAAC;QACxD,IAAI,CAACpC,YAAW,GAAI2F,SAAS,CAACE,OAAM,IAAK,2BAA2B;MACtE;IACF,CAAC;IAED;IACAgE,YAAYA,CAACjJ,MAAM,EAAE;MACnB,OAAOtB,oBAAoB,CAACuK,YAAY,CAACjJ,MAAM,CAAC;IAClD,CAAC;IAEDkJ,cAAcA,CAAClJ,MAAM,EAAE;MACrB,OAAOtB,oBAAoB,CAACwK,cAAc,CAAClJ,MAAM,CAAC;IACpD,CAAC;IAEDmJ,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIR,IAAI,CAACO,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC;IAEDC,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,UAAU;QACjBC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAK,IAAK,CAAC,CAAC;IACxB,CAAC;IAEDM,cAAcA,CAACb,UAAU,EAAE;MACzB,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,OAAO,IAAIP,IAAI,CAACO,UAAU,CAAC,CAACc,cAAc,CAAC,OAAO,EAAE;QAClDX,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdU,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAEDC,UAAUA,CAACjB,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIR,IAAI,CAACO,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACiB,kBAAkB,CAAC,OAAO,EAAE;QACtCH,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBG,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAED;IACA,MAAMnI,0BAA0BA,CAAA,EAAG;MACjCoC,OAAO,CAACyC,GAAG,CAAC,mDAAmD,CAAC;MAEhE,IAAI;QACF;QACA,MAAMtI,mBAAmB,CAAC6L,IAAI,CAAC,OAAO,CAAC;;QAEvC;QACA7L,mBAAmB,CAAC8L,EAAE,CAAC,cAAc,EAAE,IAAI,CAACC,0BAA0B,CAAC;QACvE/L,mBAAmB,CAAC8L,EAAE,CAAC,wBAAwB,EAAE,IAAI,CAACE,kBAAkB,CAAC;QACzEhM,mBAAmB,CAAC8L,EAAE,CAAC,aAAa,EAAE,IAAI,CAACG,gBAAgB,CAAC;;QAE5D;QACA,IAAI,IAAI,CAACjJ,kBAAkB,EAAE;UAC3B,IAAI,CAACkJ,gBAAgB,CAAC,CAAC;QACzB;MACF,EAAE,OAAOrJ,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;IACF,CAAC;IAEDiB,uBAAuBA,CAAA,EAAG;MACxB+B,OAAO,CAACyC,GAAG,CAAC,kDAAkD,CAAC;;MAE/D;MACAtI,mBAAmB,CAACmM,GAAG,CAAC,cAAc,EAAE,IAAI,CAACJ,0BAA0B,CAAC;MACxE/L,mBAAmB,CAACmM,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACH,kBAAkB,CAAC;MAC1EhM,mBAAmB,CAACmM,GAAG,CAAC,aAAa,EAAE,IAAI,CAACF,gBAAgB,CAAC;;MAE7D;MACAjM,mBAAmB,CAACoM,OAAO,CAAC,CAAC;;MAE7B;MACA,IAAI,CAACC,eAAe,CAAC,CAAC;IACxB,CAAC;IAEDH,gBAAgBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAACnJ,eAAe,EAAE;QACxBuJ,aAAa,CAAC,IAAI,CAACvJ,eAAe,CAAC;MACrC;MAEA,IAAI,CAACA,eAAc,GAAIwJ,WAAW,CAAC,MAAM;QACvC,IAAI,IAAI,CAACvJ,kBAAiB,IAAK,CAAC,IAAI,CAAC5C,OAAO,EAAE;UAC5CyF,OAAO,CAACyC,GAAG,CAAC,kCAAkC,CAAC;UAC/C,IAAI,CAACkE,mBAAmB,CAAC,CAAC;QAC5B;MACF,CAAC,EAAE,IAAI,CAACvJ,WAAW,CAAC;MAEpB4C,OAAO,CAACyC,GAAG,CAAC,6BAA6B,IAAI,CAACrF,WAAU,GAAI,IAAI,YAAY,CAAC;IAC/E,CAAC;IAEDoJ,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACtJ,eAAe,EAAE;QACxBuJ,aAAa,CAAC,IAAI,CAACvJ,eAAe,CAAC;QACnC,IAAI,CAACA,eAAc,GAAI,IAAI;QAC3B8C,OAAO,CAACyC,GAAG,CAAC,sBAAsB,CAAC;MACrC;IACF,CAAC;IAEDmE,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACzJ,kBAAiB,GAAI,CAAC,IAAI,CAACA,kBAAkB;MAElD,IAAI,IAAI,CAACA,kBAAkB,EAAE;QAC3B,IAAI,CAACkJ,gBAAgB,CAAC,CAAC;MACzB,OAAO;QACL,IAAI,CAACG,eAAe,CAAC,CAAC;MACxB;IACF,CAAC;IAED,MAAMG,mBAAmBA,CAAA,EAAG;MAC1B,IAAI;QACF,IAAI,CAACtJ,WAAU,GAAI,IAAIgH,IAAI,CAAC,CAAC;;QAE7B;QACA,MAAM,IAAI,CAAChE,YAAY,CAAC,CAAC;;QAEzB;QACA,MAAM,IAAI,CAACC,kBAAkB,CAAC,CAAC;;QAE/B;QACA,IAAI,IAAI,CAAChE,kBAAiB,IAAK,IAAI,CAACtB,cAAc,EAAE;UAClD,MAAM,IAAI,CAAC6H,qBAAqB,CAAC,CAAC;QACpC;QAEA7C,OAAO,CAACyC,GAAG,CAAC,sCAAsC,CAAC;MACrD,EAAE,OAAOzF,KAAK,EAAE;QACdgD,OAAO,CAAChD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAEDkJ,0BAA0BA,CAACW,YAAY,EAAE;MACvC7G,OAAO,CAACyC,GAAG,CAAC,kCAAkC,EAAEoE,YAAY,CAAC;;MAE7D;MACA,QAAQA,YAAY,CAACC,IAAI;QACvB,KAAK,wBAAwB;UAC3B,IAAI,CAACX,kBAAkB,CAACU,YAAY,CAACvM,IAAI,CAAC;UAC1C;QACF,KAAK,aAAa;UAChB,IAAI,CAAC8L,gBAAgB,CAACS,YAAY,CAACvM,IAAI,CAAC;UACxC;QACF,KAAK,iBAAiB;UACpB,IAAI,CAACyM,mBAAmB,CAACF,YAAY,CAACvM,IAAI,CAAC;UAC3C;QACF,KAAK,qBAAqB;QAC1B,KAAK,WAAW;UACd;UACA;QACF;UACE;UACA,IAAI,CAAC,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAACgE,QAAQ,CAACuI,YAAY,CAACC,IAAI,CAAC,EAAE;YACrE9G,OAAO,CAACyC,GAAG,CAAC,8BAA8B,EAAEoE,YAAY,CAACC,IAAI,CAAC;UAChE;MACJ;IACF,CAAC;IAEDX,kBAAkBA,CAAC7L,IAAI,EAAE;MACvB0F,OAAO,CAACyC,GAAG,CAAC,yBAAyB,EAAEnI,IAAI,CAAC;;MAE5C;MACA,MAAM0M,YAAW,GAAI,IAAI,CAAClM,QAAQ,CAACmM,SAAS,CAACC,GAAE,IAAKA,GAAG,CAAC3E,EAAC,KAAMjI,IAAI,CAAC6M,UAAU,CAAC;MAC/E,IAAIH,YAAW,KAAM,CAAC,CAAC,EAAE;QACvB;QACA,IAAI,CAACL,mBAAmB,CAAC,CAAC;MAC5B;;MAEA;MACA,IAAI,CAAChE,SAAS,CAAC,wBAAwB,EAAE,YAAYrI,IAAI,CAAC6M,UAAU,sBAAsB7M,IAAI,CAAC8M,UAAU,EAAE,EAAE,MAAM,CAAC;IACtH,CAAC;IAEDhB,gBAAgBA,CAAC9L,IAAI,EAAE;MACrB0F,OAAO,CAACyC,GAAG,CAAC,uBAAuB,EAAEnI,IAAI,CAAC;;MAE1C;MACA,IAAI,CAACqM,mBAAmB,CAAC,CAAC;;MAE1B;MACA,IAAI,CAAChE,SAAS,CAAC,aAAa,EAAE,OAAOrI,IAAI,CAACmB,aAAa,mBAAmB,EAAE,SAAS,CAAC;IACxF,CAAC;IAEDsL,mBAAmBA,CAACzM,IAAI,EAAE;MACxB0F,OAAO,CAACyC,GAAG,CAAC,kBAAkB,EAAEnI,IAAI,CAAC;;MAErC;MACA,IAAI,IAAI,CAACU,cAAa,IAAK,IAAI,CAACA,cAAc,CAACuH,EAAC,KAAMjI,IAAI,CAAC6M,UAAU,EAAE;QACrE,IAAI,CAACtE,qBAAqB,CAAC,CAAC;MAC9B;;MAEA;MACA,IAAI,CAAC8D,mBAAmB,CAAC,CAAC;IAC5B,CAAC;IAEDhE,SAASA,CAAC0E,KAAK,EAAE5G,OAAO,EAAEqG,IAAG,GAAI,MAAM,EAAE;MACvC;MACA9G,OAAO,CAACyC,GAAG,CAAC,IAAIqE,IAAI,CAACQ,WAAW,CAAC,CAAC,KAAKD,KAAK,KAAK5G,OAAO,EAAE,CAAC;;MAE3D;MACA,MAAM8G,KAAI,GAAIC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3CF,KAAK,CAACG,SAAQ,GAAI,4BAA4BZ,IAAI,EAAE;MACpDS,KAAK,CAACI,SAAQ,GAAI;;oBAEJN,KAAK;;;kCAGS5G,OAAO;OAClC;;MAED;MACA,IAAI,CAAC+G,QAAQ,CAACI,cAAc,CAAC,cAAc,CAAC,EAAE;QAC5C,MAAMC,MAAK,GAAIL,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;QAC9CI,MAAM,CAACtF,EAAC,GAAI,cAAc;QAC1BsF,MAAM,CAACC,WAAU,GAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAsCpB;QACDN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;MACnC;;MAEA;MACAL,QAAQ,CAACS,IAAI,CAACD,WAAW,CAACT,KAAK,CAAC;;MAEhC;MACAW,UAAU,CAAC,MAAM;QACf,IAAIX,KAAK,CAACY,aAAa,EAAE;UACvBZ,KAAK,CAACjC,KAAK,CAAC8C,SAAQ,GAAI,2BAA2B;UACnDF,UAAU,CAAC,MAAMX,KAAK,CAACc,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC;QACvC;MACF,CAAC,EAAE,IAAI,CAAC;IACV;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}