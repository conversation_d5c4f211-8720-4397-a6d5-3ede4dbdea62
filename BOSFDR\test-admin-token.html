<!DOCTYPE html>
<html>
<head>
    <title>Test Admin Token</title>
</head>
<body>
    <h1>Admin Token Test</h1>
    <div id="results"></div>

    <script>
        function log(message) {
            const div = document.getElementById('results');
            div.innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }

        // Check if admin token exists
        const adminToken = localStorage.getItem('adminToken');
        const adminData = localStorage.getItem('adminData');

        log('Admin Token: ' + (adminToken ? 'EXISTS (' + adminToken.substring(0, 50) + '...)' : 'NOT FOUND'));
        log('Admin Data: ' + (adminData ? 'EXISTS' : 'NOT FOUND'));

        if (adminData) {
            try {
                const data = JSON.parse(adminData);
                log('Admin Username: ' + data.username);
                log('Admin Role: ' + data.role);
                log('Admin Status: ' + data.status);
            } catch (e) {
                log('Error parsing admin data: ' + e.message);
            }
        }

        // Test API call
        if (adminToken) {
            log('Testing API call to /users/stats...');
            
            fetch('http://localhost:3000/api/users/stats', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + adminToken,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                log('Response status: ' + response.status);
                return response.json();
            })
            .then(data => {
                log('Response data: ' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                log('Error: ' + error.message);
            });
        } else {
            log('Cannot test API - no admin token found');
            log('Please login as admin first');
        }
    </script>
</body>
</html>
