const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
let adminToken = null;
let testUserId = null;

async function loginAdmin() {
  try {
    console.log('🔐 Logging in admin...');
    const response = await axios.post(`${BASE_URL}/admin/auth/login`, {
      username: 'admin12345',
      password: '12345QWERTqwert'
    });
    
    if (response.data.success && response.data.data.token) {
      adminToken = response.data.data.token;
      console.log('✅ Admin login successful');
      return true;
    } else {
      console.log('❌ Admin login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Admin login error:', error.response?.data || error.message);
    return false;
  }
}

async function createTestUser() {
  try {
    console.log('\n👤 Creating test user...');
    
    const userData = {
      username: 'testuser_' + Date.now(),
      password: 'testpass123',
      first_name: 'Test',
      last_name: 'User',
      role: 'client',
      phone_number: '09123456789',
      email: '<EMAIL>',
      birth_date: '1990-01-01',
      gender: 'male',
      civil_status_id: 1,
      nationality: 'Filipino',
      barangay: 'Test Barangay',
      city_municipality: 'Test City',
      province: 'Test Province'
    };
    
    const response = await axios.post(`${BASE_URL}/users`, userData, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      testUserId = response.data.data.id;
      console.log('✅ Test user created with ID:', testUserId);
      return true;
    } else {
      console.log('❌ Failed to create test user:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Create user error:', error.response?.data || error.message);
    return false;
  }
}

async function testUpdateUser() {
  try {
    console.log('\n✏️  Testing user update...');
    
    const updateData = {
      first_name: 'Updated Test',
      last_name: 'Updated User',
      email: '<EMAIL>',
      phone_number: '09987654321'
    };
    
    const response = await axios.put(`${BASE_URL}/users/${testUserId}`, updateData, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log('✅ User updated successfully');
      console.log('📋 Updated user data:', response.data.data);
      return true;
    } else {
      console.log('❌ Failed to update user:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Update user error:', error.response?.status, error.response?.data || error.message);
    return false;
  }
}

async function testDeleteUser() {
  try {
    console.log('\n🗑️  Testing user delete (soft delete)...');
    
    const response = await axios.delete(`${BASE_URL}/users/${testUserId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      console.log('✅ User deleted successfully (soft delete)');
      console.log('📋 Response:', response.data);
      return true;
    } else {
      console.log('❌ Failed to delete user:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Delete user error:', error.response?.status, error.response?.data || error.message);
    return false;
  }
}

async function verifyUserStatus() {
  try {
    console.log('\n🔍 Verifying user status after delete...');
    
    const response = await axios.get(`${BASE_URL}/users/${testUserId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      const user = response.data.data;
      console.log('✅ User found with status:', user.status);
      
      if (user.status === 'inactive') {
        console.log('✅ Soft delete working correctly - user status is inactive');
      } else {
        console.log('⚠️  User status is not inactive:', user.status);
      }
      return true;
    } else {
      console.log('❌ Failed to get user:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Get user error:', error.response?.status, error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting User CRUD Tests...\n');
  
  // Login admin
  const loginSuccess = await loginAdmin();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without admin login');
    return;
  }
  
  // Create test user
  const createSuccess = await createTestUser();
  if (!createSuccess) {
    console.log('❌ Cannot proceed without test user');
    return;
  }
  
  // Test update
  await testUpdateUser();
  
  // Test delete (soft delete)
  await testDeleteUser();
  
  // Verify soft delete
  await verifyUserStatus();
  
  console.log('\n✅ All CRUD tests completed');
}

runTests().catch(console.error);
