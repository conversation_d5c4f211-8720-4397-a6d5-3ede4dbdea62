import clientAuthService from '@/services/clientAuthService';
import Client<PERSON>idebar from '../ClientSidebar.vue';
import ClientHeader from '../ClientHeader.vue';
import HelpSupport from '../HelpSupport.vue';

export default {
  name: 'ClientDashboard',
  components: {
    ClientSidebar,
    ClientHeader,
    HelpSupport
  },
  data() {
    return {
      clientData: null,
      recentActivity: [],

      // UI State
      sidebarCollapsed: false,
      showUserDropdown: false,
      showNotifications: false,
      activeMenu: 'dashboard',

      // Dashboard Stats
      totalRequests: 0,
      pendingRequests: 0,
      completedRequests: 0,
      totalServices: 8,
      notificationCount: 0,

      // Performance optimization
      resizeTimeout: null,
      isMobile: false
    };
  },

  async mounted() {
    // Check if user is logged in
    if (!clientAuthService.isLoggedIn()) {
      this.$router.push('/client/login');
      return;
    }

    // Get client data from localStorage
    this.clientData = clientAuthService.getClientData();

    // Set initial sidebar state based on screen size
    this.handleResize();

    // Load dashboard data
    this.loadDashboardData();

    // Setup event listeners
    this.setupEventListeners();
  },

  beforeUnmount() {
    // Clean up event listeners
    this.removeEventListeners();
  },

  methods: {
    // Setup event listeners
    setupEventListeners() {
      document.addEventListener('click', this.handleOutsideClick);
      window.addEventListener('resize', this.throttledResize);
    },

    // Remove event listeners
    removeEventListeners() {
      document.removeEventListener('click', this.handleOutsideClick);
      window.removeEventListener('resize', this.throttledResize);
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout);
      }
    },

    // Handle outside clicks to close dropdowns
    handleOutsideClick(event) {
      if (!event.target.closest('.user-dropdown')) {
        this.showUserDropdown = false;
      }
      if (!event.target.closest('.notification-dropdown')) {
        this.showNotifications = false;
      }
    },

    // Throttled resize handler for better performance
    throttledResize() {
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout);
      }
      this.resizeTimeout = setTimeout(() => {
        this.handleResize();
      }, 150);
    },

    // Handle window resize
    handleResize() {
      this.isMobile = window.innerWidth <= 768;

      if (this.isMobile) {
        this.sidebarCollapsed = true;
      } else if (window.innerWidth <= 992) {
        // Auto-collapse on tablet sizes for better space usage
        this.sidebarCollapsed = true;
      }
    },

    // Close mobile sidebar
    closeMobileSidebar() {
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }
    },

    // Toggle sidebar
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },

    // Toggle user dropdown
    toggleUserDropdown() {
      this.showUserDropdown = !this.showUserDropdown;
      this.showNotifications = false;
    },

    // Toggle notifications
    toggleNotifications() {
      this.showNotifications = !this.showNotifications;
      this.showUserDropdown = false;
    },

    // Set active menu
    setActiveMenu(menu) {
      this.activeMenu = menu;

      // Close sidebar on mobile after selection
      if (window.innerWidth <= 768) {
        this.sidebarCollapsed = true;
      }
    },

    // Navigate to specific page/section
    navigateTo(page) {
      switch (page) {
        case 'profile':
          this.setActiveMenu('profile');
          break;
        case 'settings':
          this.setActiveMenu('settings');
          break;
        case 'new-request':
          // Navigate to new document request page
          this.$router.push({ name: 'NewDocumentRequest' });
          break;
        case 'track-request':
          // Navigate to my requests page
          this.$router.push({ name: 'MyRequests' });
          break;
        case 'payments':
          this.setActiveMenu('payments');
          break;
        case 'help':
          this.setActiveMenu('help');
          break;
        default:
          this.setActiveMenu('dashboard');
      }

      // Close dropdowns
      this.showUserDropdown = false;
      this.showNotifications = false;
    },

    // Get full name
    getFullName() {
      if (this.clientData?.profile) {
        const { first_name, middle_name, last_name } = this.clientData.profile;
        let fullName = first_name;
        if (middle_name) fullName += ` ${middle_name}`;
        fullName += ` ${last_name}`;
        return fullName;
      }
      return this.clientData?.username || 'User';
    },

    // Get status badge class
    getStatusBadgeClass() {
      const status = this.clientData?.status;
      switch (status) {
        case 'active': return 'status-active';
        case 'pending_verification': return 'status-pending';
        case 'suspended': return 'status-suspended';
        case 'inactive': return 'status-inactive';
        default: return 'status-unknown';
      }
    },

    // Get status text
    getStatusText() {
      const status = this.clientData?.status;
      switch (status) {
        case 'active': return 'Active';
        case 'pending_verification': return 'Pending Verification';
        case 'suspended': return 'Suspended';
        case 'inactive': return 'Inactive';
        default: return 'Unknown';
      }
    },

    // Get page title based on active menu
    getPageTitle() {
      const titles = {
        dashboard: 'Dashboard',
        services: 'Document Services',
        requests: 'My Requests',
        payments: 'Payments',
        history: 'History',
        profile: 'My Profile',
        help: 'Help & Support'
      };
      return titles[this.activeMenu] || 'Dashboard';
    },

    // Get page description based on active menu
    getPageDescription() {
      const descriptions = {
        dashboard: 'Overview of your account and recent activities',
        services: 'Apply for barangay documents and certificates',
        requests: 'Track and manage your document requests',
        payments: 'View and manage your payment history',
        history: 'View your complete transaction history',
        profile: 'Manage your personal information and settings',
        help: 'Get assistance and support for your account'
      };
      return descriptions[this.activeMenu] || '';
    },

    // Format date
    formatDate(dateString) {
      if (!dateString) return 'N/A';
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    },

    // Load dashboard data
    async loadDashboardData() {
      try {
        // This would typically fetch from APIs
        // For now, using placeholder data
        this.loadStats();
        this.loadRecentActivity();
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      }
    },

    // Load dashboard statistics
    loadStats() {
      // Placeholder data - would come from API
      this.totalRequests = 12;
      this.pendingRequests = 3;
      this.completedRequests = 9;
      this.totalServices = 8;
      this.notificationCount = 2;
    },

    // Load recent activity
    loadRecentActivity() {
      // Placeholder data - would come from API
      this.recentActivity = [
        {
          id: 1,
          title: 'Barangay Clearance Approved',
          description: 'Your barangay clearance request has been approved and is ready for pickup.',
          time: '2 hours ago',
          icon: 'fas fa-check-circle text-success'
        },
        {
          id: 2,
          title: 'Payment Received',
          description: 'Payment of ₱50 for Certificate of Residency has been received.',
          time: '1 day ago',
          icon: 'fas fa-credit-card text-primary'
        },
        {
          id: 3,
          title: 'New Request Submitted',
          description: 'Your Certificate of Indigency request has been submitted for review.',
          time: '3 days ago',
          icon: 'fas fa-file-alt text-info'
        }
      ];
    },

    // Handle menu actions from header
    handleMenuAction(action) {
      this.navigateTo(action);
    },

    // Handle view all notifications
    handleViewAllNotifications() {
      this.setActiveMenu('notifications');
      this.showNotifications = false;
    },

    // Logout
    logout() {
      if (confirm('Are you sure you want to logout?')) {
        clientAuthService.logout();
        this.$router.push('/client/login');
      }
    }
  }
};
