{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, vModelSelect as _vModelSelect, withDirectives as _withDirectives, vModelText as _vModelText, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, renderList as _renderList, withModifiers as _withModifiers, vModelCheckbox as _vModelCheckbox, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-users\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_3 = {\n  class: \"container-fluid p-4\"\n};\nconst _hoisted_4 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_5 = {\n  class: \"col-12\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex justify-content-between align-items-center flex-wrap\"\n};\nconst _hoisted_7 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_8 = [\"disabled\"];\nconst _hoisted_9 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_10 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_11 = {\n  class: \"card border-left-primary shadow h-100 py-2\"\n};\nconst _hoisted_12 = {\n  class: \"card-body\"\n};\nconst _hoisted_13 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_14 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_15 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_16 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_17 = {\n  class: \"card border-left-success shadow h-100 py-2\"\n};\nconst _hoisted_18 = {\n  class: \"card-body\"\n};\nconst _hoisted_19 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_20 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_21 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_22 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_23 = {\n  class: \"card border-left-warning shadow h-100 py-2\"\n};\nconst _hoisted_24 = {\n  class: \"card-body\"\n};\nconst _hoisted_25 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_26 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_27 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_28 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_29 = {\n  class: \"card border-left-info shadow h-100 py-2\"\n};\nconst _hoisted_30 = {\n  class: \"card-body\"\n};\nconst _hoisted_31 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_32 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_33 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_34 = {\n  class: \"row\"\n};\nconst _hoisted_35 = {\n  class: \"col-12\"\n};\nconst _hoisted_36 = {\n  class: \"card shadow\"\n};\nconst _hoisted_37 = {\n  class: \"card-header py-3 d-flex justify-content-between align-items-center\"\n};\nconst _hoisted_38 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_39 = {\n  class: \"card-body\"\n};\nconst _hoisted_40 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_41 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_42 = {\n  class: \"input-group\"\n};\nconst _hoisted_43 = {\n  class: \"col-md-6 text-end\"\n};\nconst _hoisted_44 = {\n  class: \"text-muted\"\n};\nconst _hoisted_45 = {\n  key: 0,\n  class: \"text-center py-4\"\n};\nconst _hoisted_46 = {\n  class: \"text-center py-5\"\n};\nconst _hoisted_47 = {\n  class: \"text-muted\"\n};\nconst _hoisted_48 = {\n  class: \"table-responsive\"\n};\nconst _hoisted_49 = {\n  class: \"table table-hover\"\n};\nconst _hoisted_50 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_51 = {\n  class: \"user-avatar me-3\"\n};\nconst _hoisted_52 = [\"src\", \"alt\"];\nconst _hoisted_53 = {\n  key: 1,\n  class: \"avatar-placeholder rounded-circle\"\n};\nconst _hoisted_54 = {\n  class: \"fw-bold\"\n};\nconst _hoisted_55 = {\n  class: \"text-muted small\"\n};\nconst _hoisted_56 = {\n  class: \"btn-group btn-group-sm\"\n};\nconst _hoisted_57 = [\"onClick\"];\nconst _hoisted_58 = [\"onClick\"];\nconst _hoisted_59 = [\"onClick\", \"title\"];\nconst _hoisted_60 = [\"onClick\"];\nconst _hoisted_61 = {\n  key: 3,\n  class: \"d-flex justify-content-between align-items-center mt-3\"\n};\nconst _hoisted_62 = {\n  class: \"text-muted\"\n};\nconst _hoisted_63 = {\n  class: \"pagination pagination-sm mb-0\"\n};\nconst _hoisted_64 = [\"disabled\"];\nconst _hoisted_65 = [\"onClick\"];\nconst _hoisted_66 = [\"disabled\"];\nconst _hoisted_67 = {\n  class: \"modal fade\",\n  id: \"addUserModal\",\n  tabindex: \"-1\",\n  \"aria-labelledby\": \"addUserModalLabel\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_68 = {\n  class: \"modal-dialog modal-lg\"\n};\nconst _hoisted_69 = {\n  class: \"modal-content\"\n};\nconst _hoisted_70 = {\n  class: \"modal-body\"\n};\nconst _hoisted_71 = {\n  class: \"row\"\n};\nconst _hoisted_72 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_73 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_74 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_75 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_76 = {\n  class: \"row\"\n};\nconst _hoisted_77 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_78 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_79 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_80 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_81 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_82 = {\n  class: \"row\"\n};\nconst _hoisted_83 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_84 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_85 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_86 = {\n  class: \"row\"\n};\nconst _hoisted_87 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_88 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_89 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_90 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_91 = {\n  key: 0\n};\nconst _hoisted_92 = {\n  class: \"row\"\n};\nconst _hoisted_93 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_94 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_95 = {\n  class: \"row\"\n};\nconst _hoisted_96 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_97 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_98 = {\n  key: 1\n};\nconst _hoisted_99 = {\n  class: \"row\"\n};\nconst _hoisted_100 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_101 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_102 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_103 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_104 = {\n  class: \"row\"\n};\nconst _hoisted_105 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_106 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_107 = {\n  class: \"row\"\n};\nconst _hoisted_108 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_109 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_110 = {\n  class: \"row\"\n};\nconst _hoisted_111 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_112 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_113 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_114 = {\n  class: \"row\"\n};\nconst _hoisted_115 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_116 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_117 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_118 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_119 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_120 = {\n  class: \"row\"\n};\nconst _hoisted_121 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_122 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_123 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_124 = [\"disabled\"];\nconst _hoisted_125 = {\n  key: 0,\n  class: \"spinner-border spinner-border-sm me-2\",\n  role: \"status\"\n};\nconst _hoisted_126 = {\n  key: 1,\n  class: \"fas fa-plus me-2\"\n};\nconst _hoisted_127 = {\n  class: \"modal fade\",\n  id: \"editUserModal\",\n  tabindex: \"-1\",\n  \"aria-labelledby\": \"editUserModalLabel\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_128 = {\n  class: \"modal-dialog modal-lg\"\n};\nconst _hoisted_129 = {\n  class: \"modal-content\"\n};\nconst _hoisted_130 = {\n  class: \"modal-body\"\n};\nconst _hoisted_131 = {\n  class: \"row\"\n};\nconst _hoisted_132 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_133 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_134 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_135 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_136 = {\n  class: \"row\"\n};\nconst _hoisted_137 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_138 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_139 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_140 = {\n  class: \"col-md-4 mb-3\"\n};\nconst _hoisted_141 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_142 = {\n  class: \"row\"\n};\nconst _hoisted_143 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_144 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_145 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_146 = {\n  class: \"row\"\n};\nconst _hoisted_147 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_148 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_149 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_150 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_151 = {\n  key: 0\n};\nconst _hoisted_152 = {\n  class: \"row\"\n};\nconst _hoisted_153 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_154 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_155 = {\n  class: \"row\"\n};\nconst _hoisted_156 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_157 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_158 = {\n  key: 1\n};\nconst _hoisted_159 = {\n  class: \"row\"\n};\nconst _hoisted_160 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_161 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_162 = [\"value\"];\nconst _hoisted_163 = {\n  class: \"row\"\n};\nconst _hoisted_164 = {\n  class: \"col-md-12 mb-3\"\n};\nconst _hoisted_165 = [\"value\"];\nconst _hoisted_166 = {\n  class: \"row\"\n};\nconst _hoisted_167 = {\n  class: \"col-12 mb-3\"\n};\nconst _hoisted_168 = {\n  class: \"form-check\"\n};\nconst _hoisted_169 = {\n  key: 2,\n  class: \"row\"\n};\nconst _hoisted_170 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_171 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_172 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_173 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_174 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_175 = [\"disabled\"];\nconst _hoisted_176 = {\n  key: 0,\n  class: \"spinner-border spinner-border-sm me-2\",\n  role: \"status\"\n};\nconst _hoisted_177 = {\n  key: 1,\n  class: \"fas fa-save me-2\"\n};\nconst _hoisted_178 = {\n  class: \"modal fade\",\n  id: \"viewUserModal\",\n  tabindex: \"-1\",\n  \"aria-labelledby\": \"viewUserModalLabel\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_179 = {\n  class: \"modal-dialog modal-lg\"\n};\nconst _hoisted_180 = {\n  class: \"modal-content\"\n};\nconst _hoisted_181 = {\n  key: 0,\n  class: \"modal-body\"\n};\nconst _hoisted_182 = {\n  class: \"row\"\n};\nconst _hoisted_183 = {\n  class: \"col-md-4 text-center mb-4\"\n};\nconst _hoisted_184 = {\n  class: \"user-avatar-large mx-auto mb-3\"\n};\nconst _hoisted_185 = [\"src\", \"alt\"];\nconst _hoisted_186 = {\n  key: 1,\n  class: \"avatar-placeholder-large rounded-circle\"\n};\nconst _hoisted_187 = {\n  class: \"col-md-8\"\n};\nconst _hoisted_188 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_189 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_190 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_191 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_192 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_193 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_194 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_195 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_196 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_197 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_198 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_199 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_200 = {\n  class: \"modal-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_AdminHeader = _resolveComponent(\"AdminHeader\");\n  const _component_AdminSidebar = _resolveComponent(\"AdminSidebar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_AdminHeader, {\n    userName: $data.adminData?.first_name || 'Admin',\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout\n  }, null, 8 /* PROPS */, [\"userName\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\"]), _createCommentVNode(\" Mobile Overlay \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"mobile-overlay\", {\n      active: !$data.sidebarCollapsed && $data.isMobile\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.closeMobileSidebar && $options.closeMobileSidebar(...args))\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_AdminSidebar, {\n    collapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onMenuChange: $options.handleMenuChange,\n    onLogout: $options.handleLogout,\n    onToggleSidebar: $options.handleSidebarToggle\n  }, null, 8 /* PROPS */, [\"collapsed\", \"activeMenu\", \"onMenuChange\", \"onLogout\", \"onToggleSidebar\"]), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" Page Header \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"button\", {\n    class: \"btn btn-outline-success btn-sm\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.loadUsers && $options.loadUsers(...args)),\n    disabled: $data.loading\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-sync-alt me-1\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */), _cache[60] || (_cache[60] = _createTextVNode(\" Refresh \"))], 8 /* PROPS */, _hoisted_8), _createElementVNode(\"button\", {\n    class: \"btn btn-success btn-sm\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.showAddUserModal && $options.showAddUserModal(...args))\n  }, _cache[61] || (_cache[61] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user-plus me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Add User \")]))])])])]), _createCommentVNode(\" User Statistics \"), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[62] || (_cache[62] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-primary text-uppercase mb-1\"\n  }, \" Total Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, _toDisplayString($data.userStats.total || 0), 1 /* TEXT */)]), _cache[63] || (_cache[63] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-users fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[64] || (_cache[64] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-success text-uppercase mb-1\"\n  }, \" Active Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, _toDisplayString($data.userStats.active || 0), 1 /* TEXT */)]), _cache[65] || (_cache[65] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-check fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_cache[66] || (_cache[66] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-warning text-uppercase mb-1\"\n  }, \" Pending Verification \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_27, _toDisplayString($data.userStats.pending || 0), 1 /* TEXT */)]), _cache[67] || (_cache[67] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-clock fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_cache[68] || (_cache[68] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-info text-uppercase mb-1\"\n  }, \" Admin Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_33, _toDisplayString($data.userStats.admins || 0), 1 /* TEXT */)]), _cache[69] || (_cache[69] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-shield fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])])]), _createCommentVNode(\" Users Table \"), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[72] || (_cache[72] = _createElementVNode(\"h6\", {\n    class: \"m-0 font-weight-bold text-primary\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-users me-2\"\n  }), _createTextVNode(\" User List \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_38, [_withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.filterStatus = $event),\n    onChange: _cache[4] || (_cache[4] = (...args) => $options.filterUsers && $options.filterUsers(...args))\n  }, _cache[70] || (_cache[70] = [_createStaticVNode(\"<option value=\\\"\\\" data-v-f270332e>All Status</option><option value=\\\"active\\\" data-v-f270332e>Active</option><option value=\\\"inactive\\\" data-v-f270332e>Inactive</option><option value=\\\"pending\\\" data-v-f270332e>Pending</option><option value=\\\"suspended\\\" data-v-f270332e>Suspended</option>\", 5)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.filterStatus]]), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filterType = $event),\n    onChange: _cache[6] || (_cache[6] = (...args) => $options.filterUsers && $options.filterUsers(...args))\n  }, _cache[71] || (_cache[71] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Types\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"client\"\n  }, \"Clients\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"admin\"\n  }, \"Admins\", -1 /* HOISTED */)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.filterType]])])]), _createElementVNode(\"div\", _hoisted_39, [_createCommentVNode(\" Search Bar \"), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_cache[73] || (_cache[73] = _createElementVNode(\"span\", {\n    class: \"input-group-text\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-search\"\n  })], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    placeholder: \"Search users by name, email, or username...\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.searchQuery = $event),\n    onInput: _cache[8] || (_cache[8] = (...args) => $options.searchUsers && $options.searchUsers(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.searchQuery]])])]), _createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"span\", _hoisted_44, \" Showing \" + _toDisplayString($data.filteredUsers.length) + \" of \" + _toDisplayString($data.users.length) + \" users \", 1 /* TEXT */)])]), _createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_45, _cache[74] || (_cache[74] = [_createElementVNode(\"div\", {\n    class: \"spinner-border text-primary\",\n    role: \"status\"\n  }, [_createElementVNode(\"span\", {\n    class: \"visually-hidden\"\n  }, \"Loading...\")], -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"text-muted mt-2\"\n  }, \"Loading users...\", -1 /* HOISTED */)]))) : $data.filteredUsers.length === 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Empty State \"), _createElementVNode(\"div\", _hoisted_46, [_cache[75] || (_cache[75] = _createElementVNode(\"i\", {\n    class: \"fas fa-users fa-3x text-gray-300 mb-3\"\n  }, null, -1 /* HOISTED */)), _cache[76] || (_cache[76] = _createElementVNode(\"h5\", {\n    class: \"text-gray-600\"\n  }, \"No users found\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_47, _toDisplayString($data.searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.'), 1 /* TEXT */)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Users Table \"), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"table\", _hoisted_49, [_cache[80] || (_cache[80] = _createElementVNode(\"thead\", {\n    class: \"table-light\"\n  }, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"User\"), _createElementVNode(\"th\", null, \"Email\"), _createElementVNode(\"th\", null, \"Type\"), _createElementVNode(\"th\", null, \"Status\"), _createElementVNode(\"th\", null, \"Registered\"), _createElementVNode(\"th\", null, \"Actions\")])], -1 /* HOISTED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.paginatedUsers, user => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: user.id\n    }, [_createElementVNode(\"td\", null, [_createElementVNode(\"div\", _hoisted_50, [_createElementVNode(\"div\", _hoisted_51, [user.profile_picture ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: user.profile_picture,\n      alt: user.full_name,\n      class: \"rounded-circle\"\n    }, null, 8 /* PROPS */, _hoisted_52)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_53, _toDisplayString($options.getInitials(user.full_name)), 1 /* TEXT */))]), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_54, _toDisplayString(user.full_name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_55, \"@\" + _toDisplayString(user.username), 1 /* TEXT */)])])]), _createElementVNode(\"td\", null, _toDisplayString(user.email), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", user.type === 'admin' ? 'bg-primary' : 'bg-info'])\n    }, _toDisplayString(user.type === 'admin' ? 'Admin' : 'Client'), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", $options.getStatusBadgeClass(user.status)])\n    }, _toDisplayString($options.formatStatus(user.status)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString($options.formatDate(user.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"button\", {\n      class: \"btn btn-outline-primary\",\n      onClick: $event => $options.viewUser(user),\n      title: \"View Details\"\n    }, [...(_cache[77] || (_cache[77] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_57), _createElementVNode(\"button\", {\n      class: \"btn btn-outline-warning\",\n      onClick: $event => $options.editUser(user),\n      title: \"Edit User\"\n    }, [...(_cache[78] || (_cache[78] = [_createElementVNode(\"i\", {\n      class: \"fas fa-edit\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_58), _createElementVNode(\"button\", {\n      class: _normalizeClass([\"btn\", user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success']),\n      onClick: $event => $options.toggleUserStatus(user),\n      title: user.status === 'active' ? 'Suspend User' : 'Activate User'\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass(user.status === 'active' ? 'fas fa-pause' : 'fas fa-play')\n    }, null, 2 /* CLASS */)], 10 /* CLASS, PROPS */, _hoisted_59), _createElementVNode(\"button\", {\n      class: \"btn btn-outline-danger\",\n      onClick: $event => $options.deleteUser(user),\n      title: \"Delete User\"\n    }, [...(_cache[79] || (_cache[79] = [_createElementVNode(\"i\", {\n      class: \"fas fa-trash\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_60)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Pagination \"), $options.totalPages > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_61, [_createElementVNode(\"div\", _hoisted_62, \" Page \" + _toDisplayString($data.currentPage) + \" of \" + _toDisplayString($options.totalPages), 1 /* TEXT */), _createElementVNode(\"nav\", null, [_createElementVNode(\"ul\", _hoisted_63, [_createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.currentPage === 1\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"page-link\",\n    onClick: _cache[9] || (_cache[9] = $event => $options.changePage($data.currentPage - 1)),\n    disabled: $data.currentPage === 1\n  }, \" Previous \", 8 /* PROPS */, _hoisted_64)], 2 /* CLASS */), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.visiblePages, page => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: page,\n      class: _normalizeClass([\"page-item\", {\n        active: page === $data.currentPage\n      }])\n    }, [_createElementVNode(\"button\", {\n      class: \"page-link\",\n      onClick: $event => $options.changePage(page)\n    }, _toDisplayString(page), 9 /* TEXT, PROPS */, _hoisted_65)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.currentPage === $options.totalPages\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"page-link\",\n    onClick: _cache[10] || (_cache[10] = $event => $options.changePage($data.currentPage + 1)),\n    disabled: $data.currentPage === $options.totalPages\n  }, \" Next \", 8 /* PROPS */, _hoisted_66)], 2 /* CLASS */)])])])) : _createCommentVNode(\"v-if\", true)])])])])])], 2 /* CLASS */)]), _createCommentVNode(\" Add User Modal \"), _createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"div\", _hoisted_68, [_createElementVNode(\"div\", _hoisted_69, [_cache[116] || (_cache[116] = _createElementVNode(\"div\", {\n    class: \"modal-header\"\n  }, [_createElementVNode(\"h5\", {\n    class: \"modal-title\",\n    id: \"addUserModalLabel\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-plus me-2\"\n  }), _createTextVNode(\" Add New User \")]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    \"data-bs-dismiss\": \"modal\",\n    \"aria-label\": \"Close\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_70, [_createElementVNode(\"form\", {\n    onSubmit: _cache[38] || (_cache[38] = _withModifiers((...args) => $options.submitAddUser && $options.submitAddUser(...args), [\"prevent\"]))\n  }, [_createCommentVNode(\" Basic Information \"), _createElementVNode(\"div\", _hoisted_71, [_createElementVNode(\"div\", _hoisted_72, [_cache[81] || (_cache[81] = _createElementVNode(\"label\", {\n    for: \"addUsername\",\n    class: \"form-label\"\n  }, \"Username *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.username\n    }]),\n    id: \"addUsername\",\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.addUserForm.username = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.username]]), $data.formErrors.username ? (_openBlock(), _createElementBlock(\"div\", _hoisted_73, _toDisplayString($data.formErrors.username), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_74, [_cache[82] || (_cache[82] = _createElementVNode(\"label\", {\n    for: \"addPassword\",\n    class: \"form-label\"\n  }, \"Password *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.password\n    }]),\n    id: \"addPassword\",\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.addUserForm.password = $event),\n    required: \"\",\n    minlength: \"6\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.password]]), $data.formErrors.password ? (_openBlock(), _createElementBlock(\"div\", _hoisted_75, _toDisplayString($data.formErrors.password), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Personal Information \"), _createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"div\", _hoisted_77, [_cache[83] || (_cache[83] = _createElementVNode(\"label\", {\n    for: \"addFirstName\",\n    class: \"form-label\"\n  }, \"First Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.first_name\n    }]),\n    id: \"addFirstName\",\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.addUserForm.first_name = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.first_name]]), $data.formErrors.first_name ? (_openBlock(), _createElementBlock(\"div\", _hoisted_78, _toDisplayString($data.formErrors.first_name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_79, [_cache[84] || (_cache[84] = _createElementVNode(\"label\", {\n    for: \"addMiddleName\",\n    class: \"form-label\"\n  }, \"Middle Name\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addMiddleName\",\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.addUserForm.middle_name = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.middle_name]])]), _createElementVNode(\"div\", _hoisted_80, [_cache[85] || (_cache[85] = _createElementVNode(\"label\", {\n    for: \"addLastName\",\n    class: \"form-label\"\n  }, \"Last Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.last_name\n    }]),\n    id: \"addLastName\",\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.addUserForm.last_name = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.last_name]]), $data.formErrors.last_name ? (_openBlock(), _createElementBlock(\"div\", _hoisted_81, _toDisplayString($data.formErrors.last_name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_82, [_createElementVNode(\"div\", _hoisted_83, [_cache[86] || (_cache[86] = _createElementVNode(\"label\", {\n    for: \"addSuffix\",\n    class: \"form-label\"\n  }, \"Suffix\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addSuffix\",\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.addUserForm.suffix = $event),\n    placeholder: \"Jr, Sr, III, etc.\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.suffix]])]), _createElementVNode(\"div\", _hoisted_84, [_cache[88] || (_cache[88] = _createElementVNode(\"label\", {\n    for: \"addRole\",\n    class: \"form-label\"\n  }, \"User Type *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: _normalizeClass([\"form-select\", {\n      'is-invalid': $data.formErrors.role\n    }]),\n    id: \"addRole\",\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.addUserForm.role = $event),\n    required: \"\",\n    onChange: _cache[18] || (_cache[18] = (...args) => _ctx.clearRoleSpecificFields && _ctx.clearRoleSpecificFields(...args))\n  }, _cache[87] || (_cache[87] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select User Type\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"admin\"\n  }, \"Administrator\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"client\"\n  }, \"Client\", -1 /* HOISTED */)]), 34 /* CLASS, NEED_HYDRATION */), [[_vModelSelect, $data.addUserForm.role]]), $data.formErrors.role ? (_openBlock(), _createElementBlock(\"div\", _hoisted_85, _toDisplayString($data.formErrors.role), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Contact Information \"), _createElementVNode(\"div\", _hoisted_86, [_createElementVNode(\"div\", _hoisted_87, [_cache[89] || (_cache[89] = _createElementVNode(\"label\", {\n    for: \"addEmail\",\n    class: \"form-label\"\n  }, \"Email\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"email\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.email\n    }]),\n    id: \"addEmail\",\n    \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.addUserForm.email = $event)\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.email]]), $data.formErrors.email ? (_openBlock(), _createElementBlock(\"div\", _hoisted_88, _toDisplayString($data.formErrors.email), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_89, [_cache[90] || (_cache[90] = _createElementVNode(\"label\", {\n    for: \"addPhone\",\n    class: \"form-label\"\n  }, \"Phone Number *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"tel\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.phone_number\n    }]),\n    id: \"addPhone\",\n    \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.addUserForm.phone_number = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.phone_number]]), $data.formErrors.phone_number ? (_openBlock(), _createElementBlock(\"div\", _hoisted_90, _toDisplayString($data.formErrors.phone_number), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Admin-specific fields \"), $data.addUserForm.role === 'admin' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_91, [_cache[95] || (_cache[95] = _createElementVNode(\"hr\", null, null, -1 /* HOISTED */)), _cache[96] || (_cache[96] = _createElementVNode(\"h6\", {\n    class: \"text-primary mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-shield me-2\"\n  }), _createTextVNode(\" Administrator Information \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_92, [_createElementVNode(\"div\", _hoisted_93, [_cache[91] || (_cache[91] = _createElementVNode(\"label\", {\n    for: \"addEmployeeId\",\n    class: \"form-label\"\n  }, \"Employee ID\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addEmployeeId\",\n    \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.addUserForm.employee_id = $event),\n    placeholder: \"e.g., EMP001\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.employee_id]])]), _createElementVNode(\"div\", _hoisted_94, [_cache[92] || (_cache[92] = _createElementVNode(\"label\", {\n    for: \"addPosition\",\n    class: \"form-label\"\n  }, \"Position\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addPosition\",\n    \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.addUserForm.position = $event),\n    placeholder: \"e.g., Barangay Secretary\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.position]])])]), _createElementVNode(\"div\", _hoisted_95, [_createElementVNode(\"div\", _hoisted_96, [_cache[93] || (_cache[93] = _createElementVNode(\"label\", {\n    for: \"addDepartment\",\n    class: \"form-label\"\n  }, \"Department\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addDepartment\",\n    \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.addUserForm.department = $event),\n    placeholder: \"e.g., Administration\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.department]])]), _createElementVNode(\"div\", _hoisted_97, [_cache[94] || (_cache[94] = _createElementVNode(\"label\", {\n    for: \"addHireDate\",\n    class: \"form-label\"\n  }, \"Hire Date\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    id: \"addHireDate\",\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.addUserForm.hire_date = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.hire_date]])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Client-specific fields \"), $data.addUserForm.role === 'client' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_98, [_cache[112] || (_cache[112] = _createElementVNode(\"hr\", null, null, -1 /* HOISTED */)), _cache[113] || (_cache[113] = _createElementVNode(\"h6\", {\n    class: \"text-info mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }), _createTextVNode(\" Client Information \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_99, [_createElementVNode(\"div\", _hoisted_100, [_cache[97] || (_cache[97] = _createElementVNode(\"label\", {\n    for: \"addBirthDate\",\n    class: \"form-label\"\n  }, \"Birth Date *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.birth_date\n    }]),\n    id: \"addBirthDate\",\n    \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.addUserForm.birth_date = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.birth_date]]), $data.formErrors.birth_date ? (_openBlock(), _createElementBlock(\"div\", _hoisted_101, _toDisplayString($data.formErrors.birth_date), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_102, [_cache[99] || (_cache[99] = _createElementVNode(\"label\", {\n    for: \"addGender\",\n    class: \"form-label\"\n  }, \"Gender *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: _normalizeClass([\"form-select\", {\n      'is-invalid': $data.formErrors.gender\n    }]),\n    id: \"addGender\",\n    \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.addUserForm.gender = $event),\n    required: \"\"\n  }, _cache[98] || (_cache[98] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select Gender\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"male\"\n  }, \"Male\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"female\"\n  }, \"Female\", -1 /* HOISTED */)]), 2 /* CLASS */), [[_vModelSelect, $data.addUserForm.gender]]), $data.formErrors.gender ? (_openBlock(), _createElementBlock(\"div\", _hoisted_103, _toDisplayString($data.formErrors.gender), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_104, [_createElementVNode(\"div\", _hoisted_105, [_cache[101] || (_cache[101] = _createElementVNode(\"label\", {\n    for: \"addCivilStatus\",\n    class: \"form-label\"\n  }, \"Civil Status *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    id: \"addCivilStatus\",\n    \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.addUserForm.civil_status_id = $event),\n    required: \"\"\n  }, _cache[100] || (_cache[100] = [_createStaticVNode(\"<option value=\\\"1\\\" data-v-f270332e>Single</option><option value=\\\"2\\\" data-v-f270332e>Married</option><option value=\\\"3\\\" data-v-f270332e>Widowed</option><option value=\\\"4\\\" data-v-f270332e>Divorced</option><option value=\\\"5\\\" data-v-f270332e>Separated</option>\", 5)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.addUserForm.civil_status_id]])]), _createElementVNode(\"div\", _hoisted_106, [_cache[102] || (_cache[102] = _createElementVNode(\"label\", {\n    for: \"addNationality\",\n    class: \"form-label\"\n  }, \"Nationality\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addNationality\",\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.addUserForm.nationality = $event),\n    placeholder: \"Filipino\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.nationality]])])]), _createCommentVNode(\" Address Information \"), _cache[114] || (_cache[114] = _createElementVNode(\"h6\", {\n    class: \"text-secondary mb-3 mt-4\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-map-marker-alt me-2\"\n  }), _createTextVNode(\" Address Information \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_107, [_createElementVNode(\"div\", _hoisted_108, [_cache[103] || (_cache[103] = _createElementVNode(\"label\", {\n    for: \"addHouseNumber\",\n    class: \"form-label\"\n  }, \"House Number\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addHouseNumber\",\n    \"onUpdate:modelValue\": _cache[29] || (_cache[29] = $event => $data.addUserForm.house_number = $event),\n    placeholder: \"e.g., 123\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.house_number]])]), _createElementVNode(\"div\", _hoisted_109, [_cache[104] || (_cache[104] = _createElementVNode(\"label\", {\n    for: \"addStreet\",\n    class: \"form-label\"\n  }, \"Street\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addStreet\",\n    \"onUpdate:modelValue\": _cache[30] || (_cache[30] = $event => $data.addUserForm.street = $event),\n    placeholder: \"e.g., Main Street\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.street]])])]), _createElementVNode(\"div\", _hoisted_110, [_createElementVNode(\"div\", _hoisted_111, [_cache[105] || (_cache[105] = _createElementVNode(\"label\", {\n    for: \"addSubdivision\",\n    class: \"form-label\"\n  }, \"Subdivision\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addSubdivision\",\n    \"onUpdate:modelValue\": _cache[31] || (_cache[31] = $event => $data.addUserForm.subdivision = $event),\n    placeholder: \"e.g., Greenfield Village\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.subdivision]])]), _createElementVNode(\"div\", _hoisted_112, [_cache[106] || (_cache[106] = _createElementVNode(\"label\", {\n    for: \"addBarangay\",\n    class: \"form-label\"\n  }, \"Barangay *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.barangay\n    }]),\n    id: \"addBarangay\",\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.addUserForm.barangay = $event),\n    required: \"\",\n    placeholder: \"e.g., Barangay Bula\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.barangay]]), $data.formErrors.barangay ? (_openBlock(), _createElementBlock(\"div\", _hoisted_113, _toDisplayString($data.formErrors.barangay), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_114, [_createElementVNode(\"div\", _hoisted_115, [_cache[107] || (_cache[107] = _createElementVNode(\"label\", {\n    for: \"addCity\",\n    class: \"form-label\"\n  }, \"City/Municipality *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.city_municipality\n    }]),\n    id: \"addCity\",\n    \"onUpdate:modelValue\": _cache[33] || (_cache[33] = $event => $data.addUserForm.city_municipality = $event),\n    required: \"\",\n    placeholder: \"e.g., Camarines Sur\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.city_municipality]]), $data.formErrors.city_municipality ? (_openBlock(), _createElementBlock(\"div\", _hoisted_116, _toDisplayString($data.formErrors.city_municipality), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_117, [_cache[108] || (_cache[108] = _createElementVNode(\"label\", {\n    for: \"addProvince\",\n    class: \"form-label\"\n  }, \"Province *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.formErrors.province\n    }]),\n    id: \"addProvince\",\n    \"onUpdate:modelValue\": _cache[34] || (_cache[34] = $event => $data.addUserForm.province = $event),\n    required: \"\",\n    placeholder: \"e.g., Camarines Sur\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.addUserForm.province]]), $data.formErrors.province ? (_openBlock(), _createElementBlock(\"div\", _hoisted_118, _toDisplayString($data.formErrors.province), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_119, [_cache[109] || (_cache[109] = _createElementVNode(\"label\", {\n    for: \"addPostalCode\",\n    class: \"form-label\"\n  }, \"Postal Code\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addPostalCode\",\n    \"onUpdate:modelValue\": _cache[35] || (_cache[35] = $event => $data.addUserForm.postal_code = $event),\n    placeholder: \"e.g., 4422\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.postal_code]])])]), _createCommentVNode(\" Residency Information \"), _createElementVNode(\"div\", _hoisted_120, [_createElementVNode(\"div\", _hoisted_121, [_cache[110] || (_cache[110] = _createElementVNode(\"label\", {\n    for: \"addYearsResidency\",\n    class: \"form-label\"\n  }, \"Years of Residency\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"number\",\n    class: \"form-control\",\n    id: \"addYearsResidency\",\n    \"onUpdate:modelValue\": _cache[36] || (_cache[36] = $event => $data.addUserForm.years_of_residency = $event),\n    min: \"0\",\n    placeholder: \"e.g., 5\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.years_of_residency]])]), _createElementVNode(\"div\", _hoisted_122, [_cache[111] || (_cache[111] = _createElementVNode(\"label\", {\n    for: \"addMonthsResidency\",\n    class: \"form-label\"\n  }, \"Additional Months\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"number\",\n    class: \"form-control\",\n    id: \"addMonthsResidency\",\n    \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.addUserForm.months_of_residency = $event),\n    min: \"0\",\n    max: \"11\",\n    placeholder: \"e.g., 6\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.months_of_residency]])])])])) : _createCommentVNode(\"v-if\", true)], 32 /* NEED_HYDRATION */)]), _createElementVNode(\"div\", _hoisted_123, [_cache[115] || (_cache[115] = _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    \"data-bs-dismiss\": \"modal\"\n  }, \"Cancel\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[39] || (_cache[39] = (...args) => $options.submitAddUser && $options.submitAddUser(...args)),\n    disabled: $data.addUserLoading\n  }, [$data.addUserLoading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_125)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_126)), _createTextVNode(\" \" + _toDisplayString($data.addUserLoading ? 'Creating...' : 'Create User'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_124)])])])]), _createCommentVNode(\" Edit User Modal \"), _createElementVNode(\"div\", _hoisted_127, [_createElementVNode(\"div\", _hoisted_128, [_createElementVNode(\"div\", _hoisted_129, [_cache[146] || (_cache[146] = _createElementVNode(\"div\", {\n    class: \"modal-header\"\n  }, [_createElementVNode(\"h5\", {\n    class: \"modal-title\",\n    id: \"editUserModalLabel\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-edit me-2\"\n  }), _createTextVNode(\" Edit User \")]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    \"data-bs-dismiss\": \"modal\",\n    \"aria-label\": \"Close\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_130, [$data.editUserForm.id ? (_openBlock(), _createElementBlock(\"form\", {\n    key: 0,\n    onSubmit: _cache[57] || (_cache[57] = _withModifiers((...args) => $options.submitEditUser && $options.submitEditUser(...args), [\"prevent\"]))\n  }, [_createCommentVNode(\" Basic Information \"), _createElementVNode(\"div\", _hoisted_131, [_createElementVNode(\"div\", _hoisted_132, [_cache[117] || (_cache[117] = _createElementVNode(\"label\", {\n    for: \"editUsername\",\n    class: \"form-label\"\n  }, \"Username *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.editFormErrors.username\n    }]),\n    id: \"editUsername\",\n    \"onUpdate:modelValue\": _cache[40] || (_cache[40] = $event => $data.editUserForm.username = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.editUserForm.username]]), $data.editFormErrors.username ? (_openBlock(), _createElementBlock(\"div\", _hoisted_133, _toDisplayString($data.editFormErrors.username), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_134, [_cache[119] || (_cache[119] = _createElementVNode(\"label\", {\n    for: \"editStatus\",\n    class: \"form-label\"\n  }, \"Status *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: _normalizeClass([\"form-select\", {\n      'is-invalid': $data.editFormErrors.status\n    }]),\n    id: \"editStatus\",\n    \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $data.editUserForm.status = $event),\n    required: \"\"\n  }, _cache[118] || (_cache[118] = [_createElementVNode(\"option\", {\n    value: \"active\"\n  }, \"Active\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"inactive\"\n  }, \"Inactive\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"suspended\"\n  }, \"Suspended\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"pending_verification\"\n  }, \"Pending Verification\", -1 /* HOISTED */)]), 2 /* CLASS */), [[_vModelSelect, $data.editUserForm.status]]), $data.editFormErrors.status ? (_openBlock(), _createElementBlock(\"div\", _hoisted_135, _toDisplayString($data.editFormErrors.status), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Personal Information \"), _createElementVNode(\"div\", _hoisted_136, [_createElementVNode(\"div\", _hoisted_137, [_cache[120] || (_cache[120] = _createElementVNode(\"label\", {\n    for: \"editFirstName\",\n    class: \"form-label\"\n  }, \"First Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.editFormErrors.first_name\n    }]),\n    id: \"editFirstName\",\n    \"onUpdate:modelValue\": _cache[42] || (_cache[42] = $event => $data.editUserForm.first_name = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.editUserForm.first_name]]), $data.editFormErrors.first_name ? (_openBlock(), _createElementBlock(\"div\", _hoisted_138, _toDisplayString($data.editFormErrors.first_name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_139, [_cache[121] || (_cache[121] = _createElementVNode(\"label\", {\n    for: \"editMiddleName\",\n    class: \"form-label\"\n  }, \"Middle Name\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"editMiddleName\",\n    \"onUpdate:modelValue\": _cache[43] || (_cache[43] = $event => $data.editUserForm.middle_name = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.middle_name]])]), _createElementVNode(\"div\", _hoisted_140, [_cache[122] || (_cache[122] = _createElementVNode(\"label\", {\n    for: \"editLastName\",\n    class: \"form-label\"\n  }, \"Last Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.editFormErrors.last_name\n    }]),\n    id: \"editLastName\",\n    \"onUpdate:modelValue\": _cache[44] || (_cache[44] = $event => $data.editUserForm.last_name = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.editUserForm.last_name]]), $data.editFormErrors.last_name ? (_openBlock(), _createElementBlock(\"div\", _hoisted_141, _toDisplayString($data.editFormErrors.last_name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_142, [_createElementVNode(\"div\", _hoisted_143, [_cache[123] || (_cache[123] = _createElementVNode(\"label\", {\n    for: \"editSuffix\",\n    class: \"form-label\"\n  }, \"Suffix\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"editSuffix\",\n    \"onUpdate:modelValue\": _cache[45] || (_cache[45] = $event => $data.editUserForm.suffix = $event),\n    placeholder: \"Jr, Sr, III, etc.\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.suffix]])]), _createElementVNode(\"div\", _hoisted_144, [_cache[125] || (_cache[125] = _createElementVNode(\"label\", {\n    for: \"editRole\",\n    class: \"form-label\"\n  }, \"User Type *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: _normalizeClass([\"form-select\", {\n      'is-invalid': $data.editFormErrors.role\n    }]),\n    id: \"editRole\",\n    \"onUpdate:modelValue\": _cache[46] || (_cache[46] = $event => $data.editUserForm.role = $event),\n    required: \"\",\n    disabled: \"\"\n  }, _cache[124] || (_cache[124] = [_createElementVNode(\"option\", {\n    value: \"admin\"\n  }, \"Administrator\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"client\"\n  }, \"Client\", -1 /* HOISTED */)]), 2 /* CLASS */), [[_vModelSelect, $data.editUserForm.role]]), _cache[126] || (_cache[126] = _createElementVNode(\"small\", {\n    class: \"text-muted\"\n  }, \"User type cannot be changed after creation\", -1 /* HOISTED */)), $data.editFormErrors.role ? (_openBlock(), _createElementBlock(\"div\", _hoisted_145, _toDisplayString($data.editFormErrors.role), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Contact Information \"), _createElementVNode(\"div\", _hoisted_146, [_createElementVNode(\"div\", _hoisted_147, [_cache[127] || (_cache[127] = _createElementVNode(\"label\", {\n    for: \"editEmail\",\n    class: \"form-label\"\n  }, \"Email\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"email\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.editFormErrors.email\n    }]),\n    id: \"editEmail\",\n    \"onUpdate:modelValue\": _cache[47] || (_cache[47] = $event => $data.editUserForm.email = $event)\n  }, null, 2 /* CLASS */), [[_vModelText, $data.editUserForm.email]]), $data.editFormErrors.email ? (_openBlock(), _createElementBlock(\"div\", _hoisted_148, _toDisplayString($data.editFormErrors.email), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_149, [_cache[128] || (_cache[128] = _createElementVNode(\"label\", {\n    for: \"editPhone\",\n    class: \"form-label\"\n  }, \"Phone Number *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"tel\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.editFormErrors.phone_number\n    }]),\n    id: \"editPhone\",\n    \"onUpdate:modelValue\": _cache[48] || (_cache[48] = $event => $data.editUserForm.phone_number = $event),\n    required: \"\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.editUserForm.phone_number]]), $data.editFormErrors.phone_number ? (_openBlock(), _createElementBlock(\"div\", _hoisted_150, _toDisplayString($data.editFormErrors.phone_number), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Admin-specific fields \"), $data.editUserForm.role === 'admin' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_151, [_cache[133] || (_cache[133] = _createElementVNode(\"hr\", null, null, -1 /* HOISTED */)), _cache[134] || (_cache[134] = _createElementVNode(\"h6\", {\n    class: \"text-primary mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-shield me-2\"\n  }), _createTextVNode(\" Administrator Information \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_152, [_createElementVNode(\"div\", _hoisted_153, [_cache[129] || (_cache[129] = _createElementVNode(\"label\", {\n    for: \"editEmployeeId\",\n    class: \"form-label\"\n  }, \"Employee ID\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"editEmployeeId\",\n    \"onUpdate:modelValue\": _cache[49] || (_cache[49] = $event => $data.editUserForm.employee_id = $event),\n    placeholder: \"e.g., EMP001\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.employee_id]])]), _createElementVNode(\"div\", _hoisted_154, [_cache[130] || (_cache[130] = _createElementVNode(\"label\", {\n    for: \"editPosition\",\n    class: \"form-label\"\n  }, \"Position\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"editPosition\",\n    \"onUpdate:modelValue\": _cache[50] || (_cache[50] = $event => $data.editUserForm.position = $event),\n    placeholder: \"e.g., Barangay Secretary\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.position]])])]), _createElementVNode(\"div\", _hoisted_155, [_createElementVNode(\"div\", _hoisted_156, [_cache[131] || (_cache[131] = _createElementVNode(\"label\", {\n    for: \"editDepartment\",\n    class: \"form-label\"\n  }, \"Department\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"editDepartment\",\n    \"onUpdate:modelValue\": _cache[51] || (_cache[51] = $event => $data.editUserForm.department = $event),\n    placeholder: \"e.g., Administration\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.department]])]), _createElementVNode(\"div\", _hoisted_157, [_cache[132] || (_cache[132] = _createElementVNode(\"label\", {\n    for: \"editHireDate\",\n    class: \"form-label\"\n  }, \"Hire Date\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    id: \"editHireDate\",\n    \"onUpdate:modelValue\": _cache[52] || (_cache[52] = $event => $data.editUserForm.hire_date = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.hire_date]])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Client-specific fields (read-only for editing) \"), $data.editUserForm.role === 'client' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_158, [_cache[138] || (_cache[138] = _createElementVNode(\"hr\", null, null, -1 /* HOISTED */)), _cache[139] || (_cache[139] = _createElementVNode(\"h6\", {\n    class: \"text-info mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }), _createTextVNode(\" Client Information \")], -1 /* HOISTED */)), _cache[140] || (_cache[140] = _createElementVNode(\"div\", {\n    class: \"alert alert-info\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-info-circle me-2\"\n  }), _createTextVNode(\" Client-specific information can only be updated by the client through their profile. \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_159, [_createElementVNode(\"div\", _hoisted_160, [_cache[135] || (_cache[135] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Birth Date\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[53] || (_cache[53] = $event => $data.editUserForm.birth_date = $event),\n    readonly: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.birth_date]])]), _createElementVNode(\"div\", _hoisted_161, [_cache[136] || (_cache[136] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Gender\", -1 /* HOISTED */)), _createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    value: $data.editUserForm.gender ? $data.editUserForm.gender.charAt(0).toUpperCase() + $data.editUserForm.gender.slice(1) : '',\n    readonly: \"\"\n  }, null, 8 /* PROPS */, _hoisted_162)])]), _createElementVNode(\"div\", _hoisted_163, [_createElementVNode(\"div\", _hoisted_164, [_cache[137] || (_cache[137] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Address\", -1 /* HOISTED */)), _createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    value: _ctx.getFullAddress($data.editUserForm),\n    readonly: \"\"\n  }, null, 8 /* PROPS */, _hoisted_165)])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Password Reset Section \"), _cache[144] || (_cache[144] = _createElementVNode(\"hr\", null, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_166, [_createElementVNode(\"div\", _hoisted_167, [_createElementVNode(\"div\", _hoisted_168, [_withDirectives(_createElementVNode(\"input\", {\n    class: \"form-check-input\",\n    type: \"checkbox\",\n    id: \"resetPassword\",\n    \"onUpdate:modelValue\": _cache[54] || (_cache[54] = $event => $data.editUserForm.resetPassword = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.editUserForm.resetPassword]]), _cache[141] || (_cache[141] = _createElementVNode(\"label\", {\n    class: \"form-check-label\",\n    for: \"resetPassword\"\n  }, \" Reset user password \", -1 /* HOISTED */))])])]), $data.editUserForm.resetPassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_169, [_createElementVNode(\"div\", _hoisted_170, [_cache[142] || (_cache[142] = _createElementVNode(\"label\", {\n    for: \"editNewPassword\",\n    class: \"form-label\"\n  }, \"New Password *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.editFormErrors.newPassword\n    }]),\n    id: \"editNewPassword\",\n    \"onUpdate:modelValue\": _cache[55] || (_cache[55] = $event => $data.editUserForm.newPassword = $event),\n    minlength: \"6\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.editUserForm.newPassword]]), $data.editFormErrors.newPassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_171, _toDisplayString($data.editFormErrors.newPassword), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_172, [_cache[143] || (_cache[143] = _createElementVNode(\"label\", {\n    for: \"editConfirmPassword\",\n    class: \"form-label\"\n  }, \"Confirm Password *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.editFormErrors.confirmPassword\n    }]),\n    id: \"editConfirmPassword\",\n    \"onUpdate:modelValue\": _cache[56] || (_cache[56] = $event => $data.editUserForm.confirmPassword = $event),\n    minlength: \"6\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.editUserForm.confirmPassword]]), $data.editFormErrors.confirmPassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_173, _toDisplayString($data.editFormErrors.confirmPassword), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)], 32 /* NEED_HYDRATION */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_174, [_cache[145] || (_cache[145] = _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    \"data-bs-dismiss\": \"modal\"\n  }, \"Cancel\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[58] || (_cache[58] = (...args) => $options.submitEditUser && $options.submitEditUser(...args)),\n    disabled: $data.editUserLoading\n  }, [$data.editUserLoading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_176)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_177)), _createTextVNode(\" \" + _toDisplayString($data.editUserLoading ? 'Updating...' : 'Update User'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_175)])])])]), _createCommentVNode(\" View User Modal \"), _createElementVNode(\"div\", _hoisted_178, [_createElementVNode(\"div\", _hoisted_179, [_createElementVNode(\"div\", _hoisted_180, [_cache[155] || (_cache[155] = _createElementVNode(\"div\", {\n    class: \"modal-header\"\n  }, [_createElementVNode(\"h5\", {\n    class: \"modal-title\",\n    id: \"viewUserModalLabel\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }), _createTextVNode(\" User Details \")]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    \"data-bs-dismiss\": \"modal\",\n    \"aria-label\": \"Close\"\n  })], -1 /* HOISTED */)), $data.viewUserData ? (_openBlock(), _createElementBlock(\"div\", _hoisted_181, [_createElementVNode(\"div\", _hoisted_182, [_createElementVNode(\"div\", _hoisted_183, [_createElementVNode(\"div\", _hoisted_184, [$data.viewUserData.profile_picture ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $data.viewUserData.profile_picture,\n    alt: $data.viewUserData.full_name,\n    class: \"rounded-circle\"\n  }, null, 8 /* PROPS */, _hoisted_185)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_186, _toDisplayString($options.getInitials($data.viewUserData.full_name)), 1 /* TEXT */))]), _createElementVNode(\"h5\", null, _toDisplayString($data.viewUserData.full_name), 1 /* TEXT */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $options.getStatusBadgeClass($data.viewUserData.status)])\n  }, _toDisplayString($options.formatStatus($data.viewUserData.status)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"div\", _hoisted_187, [_createElementVNode(\"div\", _hoisted_188, [_cache[147] || (_cache[147] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Username:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_189, _toDisplayString($data.viewUserData.username), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_190, [_cache[148] || (_cache[148] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Email:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_191, _toDisplayString($data.viewUserData.email), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_192, [_cache[149] || (_cache[149] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Type:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_193, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'])\n  }, _toDisplayString($data.viewUserData.type === 'admin' ? 'Administrator' : 'Client'), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_194, [_cache[150] || (_cache[150] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Phone:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_195, _toDisplayString($data.viewUserData.phone_number || 'N/A'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_196, [_cache[151] || (_cache[151] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Registered:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_197, _toDisplayString($options.formatDate($data.viewUserData.created_at)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_198, [_cache[152] || (_cache[152] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Last Login:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_199, _toDisplayString($data.viewUserData.last_login ? $options.formatDate($data.viewUserData.last_login) : 'Never'), 1 /* TEXT */)])])])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_200, [_cache[154] || (_cache[154] = _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    \"data-bs-dismiss\": \"modal\"\n  }, \"Close\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[59] || (_cache[59] = $event => $options.editUser($data.viewUserData)),\n    \"data-bs-dismiss\": \"modal\"\n  }, _cache[153] || (_cache[153] = [_createElementVNode(\"i\", {\n    class: \"fas fa-edit me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Edit User \")]))])])])])]);\n}", "map": {"version": 3, "names": ["class", "id", "tabindex", "role", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_AdminHeader", "userName", "$data", "adminData", "first_name", "showUserDropdown", "sidebarCollapsed", "activeMenu", "$options", "onSidebarToggle", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "active", "isMobile", "onClick", "_cache", "args", "closeMobileSidebar", "_hoisted_2", "_component_AdminSidebar", "collapsed", "onMenuChange", "handleMenuChange", "onToggleSidebar", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "loadUsers", "disabled", "loading", "showAddUserModal", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_toDisplayString", "userStats", "total", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "pending", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "admins", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "filterStatus", "$event", "onChange", "filterUsers", "filterType", "value", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "type", "placeholder", "searchQuery", "onInput", "searchUsers", "_hoisted_43", "_hoisted_44", "filteredUsers", "length", "users", "_hoisted_45", "_Fragment", "key", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_renderList", "paginatedUsers", "user", "_hoisted_50", "_hoisted_51", "profile_picture", "src", "alt", "full_name", "_hoisted_53", "getInitials", "_hoisted_54", "_hoisted_55", "username", "email", "getStatusBadgeClass", "status", "formatStatus", "formatDate", "created_at", "_hoisted_56", "viewUser", "title", "editUser", "toggleUserStatus", "deleteUser", "totalPages", "_hoisted_61", "_hoisted_62", "currentPage", "_hoisted_63", "changePage", "_hoisted_64", "visiblePages", "page", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "onSubmit", "_withModifiers", "submitAddUser", "_hoisted_71", "_hoisted_72", "for", "formErrors", "addUserForm", "required", "_hoisted_73", "_hoisted_74", "password", "minlength", "_hoisted_75", "_hoisted_76", "_hoisted_77", "_hoisted_78", "_hoisted_79", "middle_name", "_hoisted_80", "last_name", "_hoisted_81", "_hoisted_82", "_hoisted_83", "suffix", "_hoisted_84", "_ctx", "clearRoleSpecificFields", "_hoisted_85", "_hoisted_86", "_hoisted_87", "_hoisted_88", "_hoisted_89", "phone_number", "_hoisted_90", "_hoisted_91", "_hoisted_92", "_hoisted_93", "employee_id", "_hoisted_94", "position", "_hoisted_95", "_hoisted_96", "department", "_hoisted_97", "hire_date", "_hoisted_98", "_hoisted_99", "_hoisted_100", "birth_date", "_hoisted_101", "_hoisted_102", "gender", "_hoisted_103", "_hoisted_104", "_hoisted_105", "civil_status_id", "_hoisted_106", "nationality", "_hoisted_107", "_hoisted_108", "house_number", "_hoisted_109", "street", "_hoisted_110", "_hoisted_111", "subdivision", "_hoisted_112", "barangay", "_hoisted_113", "_hoisted_114", "_hoisted_115", "city_municipality", "_hoisted_116", "_hoisted_117", "province", "_hoisted_118", "_hoisted_119", "postal_code", "_hoisted_120", "_hoisted_121", "years_of_residency", "min", "_hoisted_122", "months_of_residency", "max", "_hoisted_123", "addUserLoading", "_hoisted_125", "_hoisted_126", "_hoisted_127", "_hoisted_128", "_hoisted_129", "_hoisted_130", "editUserForm", "submitEditUser", "_hoisted_131", "_hoisted_132", "editFormErrors", "_hoisted_133", "_hoisted_134", "_hoisted_135", "_hoisted_136", "_hoisted_137", "_hoisted_138", "_hoisted_139", "_hoisted_140", "_hoisted_141", "_hoisted_142", "_hoisted_143", "_hoisted_144", "_hoisted_145", "_hoisted_146", "_hoisted_147", "_hoisted_148", "_hoisted_149", "_hoisted_150", "_hoisted_151", "_hoisted_152", "_hoisted_153", "_hoisted_154", "_hoisted_155", "_hoisted_156", "_hoisted_157", "_hoisted_158", "_hoisted_159", "_hoisted_160", "readonly", "_hoisted_161", "char<PERSON>t", "toUpperCase", "slice", "_hoisted_163", "_hoisted_164", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_hoisted_166", "_hoisted_167", "_hoisted_168", "resetPassword", "_hoisted_169", "_hoisted_170", "newPassword", "_hoisted_171", "_hoisted_172", "confirmPassword", "_hoisted_173", "_hoisted_174", "editUserLoading", "_hoisted_176", "_hoisted_177", "_hoisted_178", "_hoisted_179", "_hoisted_180", "viewUserData", "_hoisted_181", "_hoisted_182", "_hoisted_183", "_hoisted_184", "_hoisted_186", "_hoisted_187", "_hoisted_188", "_hoisted_189", "_hoisted_190", "_hoisted_191", "_hoisted_192", "_hoisted_193", "_hoisted_194", "_hoisted_195", "_hoisted_196", "_hoisted_197", "_hoisted_198", "_hoisted_199", "last_login", "_hoisted_200"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminUsers.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-users\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"container-fluid p-4\">\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-outline-success btn-sm\" @click=\"loadUsers\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                  <button class=\"btn btn-success btn-sm\" @click=\"showAddUserModal\">\n                    <i class=\"fas fa-user-plus me-1\"></i>\n                    Add User\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- User Statistics -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-primary shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">\n                        Total Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.total || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-users fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-success shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">\n                        Active Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.active || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-check fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-warning shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">\n                        Pending Verification\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.pending || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-clock fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-info shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">\n                        Admin Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.admins || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-shield fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Users Table -->\n          <div class=\"row\">\n            <div class=\"col-12\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex justify-content-between align-items-center\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-users me-2\"></i>\n                    User List\n                  </h6>\n                  <div class=\"d-flex gap-2\">\n                    <select class=\"form-select form-select-sm\" v-model=\"filterStatus\" @change=\"filterUsers\">\n                      <option value=\"\">All Status</option>\n                      <option value=\"active\">Active</option>\n                      <option value=\"inactive\">Inactive</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"suspended\">Suspended</option>\n                    </select>\n                    <select class=\"form-select form-select-sm\" v-model=\"filterType\" @change=\"filterUsers\">\n                      <option value=\"\">All Types</option>\n                      <option value=\"client\">Clients</option>\n                      <option value=\"admin\">Admins</option>\n                    </select>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <!-- Search Bar -->\n                  <div class=\"row mb-3\">\n                    <div class=\"col-md-6\">\n                      <div class=\"input-group\">\n                        <span class=\"input-group-text\">\n                          <i class=\"fas fa-search\"></i>\n                        </span>\n                        <input\n                          type=\"text\"\n                          class=\"form-control\"\n                          placeholder=\"Search users by name, email, or username...\"\n                          v-model=\"searchQuery\"\n                          @input=\"searchUsers\"\n                        >\n                      </div>\n                    </div>\n                    <div class=\"col-md-6 text-end\">\n                      <span class=\"text-muted\">\n                        Showing {{ filteredUsers.length }} of {{ users.length }} users\n                      </span>\n                    </div>\n                  </div>\n\n                  <!-- Loading State -->\n                  <div v-if=\"loading\" class=\"text-center py-4\">\n                    <div class=\"spinner-border text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Loading...</span>\n                    </div>\n                    <p class=\"text-muted mt-2\">Loading users...</p>\n                  </div>\n\n                  <!-- Empty State -->\n                  <div v-else-if=\"filteredUsers.length === 0\" class=\"text-center py-5\">\n                    <i class=\"fas fa-users fa-3x text-gray-300 mb-3\"></i>\n                    <h5 class=\"text-gray-600\">No users found</h5>\n                    <p class=\"text-muted\">\n                      {{ searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.' }}\n                    </p>\n                  </div>\n\n                  <!-- Users Table -->\n                  <div v-else class=\"table-responsive\">\n                    <table class=\"table table-hover\">\n                      <thead class=\"table-light\">\n                        <tr>\n                          <th>User</th>\n                          <th>Email</th>\n                          <th>Type</th>\n                          <th>Status</th>\n                          <th>Registered</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        <tr v-for=\"user in paginatedUsers\" :key=\"user.id\">\n                          <td>\n                            <div class=\"d-flex align-items-center\">\n                              <div class=\"user-avatar me-3\">\n                                <img v-if=\"user.profile_picture\" :src=\"user.profile_picture\" :alt=\"user.full_name\" class=\"rounded-circle\">\n                                <div v-else class=\"avatar-placeholder rounded-circle\">\n                                  {{ getInitials(user.full_name) }}\n                                </div>\n                              </div>\n                              <div>\n                                <div class=\"fw-bold\">{{ user.full_name }}</div>\n                                <div class=\"text-muted small\">@{{ user.username }}</div>\n                              </div>\n                            </div>\n                          </td>\n                          <td>{{ user.email }}</td>\n                          <td>\n                            <span class=\"badge\" :class=\"user.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                              {{ user.type === 'admin' ? 'Admin' : 'Client' }}\n                            </span>\n                          </td>\n                          <td>\n                            <span class=\"badge\" :class=\"getStatusBadgeClass(user.status)\">\n                              {{ formatStatus(user.status) }}\n                            </span>\n                          </td>\n                          <td>{{ formatDate(user.created_at) }}</td>\n                          <td>\n                            <div class=\"btn-group btn-group-sm\">\n                              <button class=\"btn btn-outline-primary\" @click=\"viewUser(user)\" title=\"View Details\">\n                                <i class=\"fas fa-eye\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-warning\" @click=\"editUser(user)\" title=\"Edit User\">\n                                <i class=\"fas fa-edit\"></i>\n                              </button>\n                              <button\n                                class=\"btn\"\n                                :class=\"user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'\"\n                                @click=\"toggleUserStatus(user)\"\n                                :title=\"user.status === 'active' ? 'Suspend User' : 'Activate User'\"\n                              >\n                                <i :class=\"user.status === 'active' ? 'fas fa-pause' : 'fas fa-play'\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-danger\" @click=\"deleteUser(user)\" title=\"Delete User\">\n                                <i class=\"fas fa-trash\"></i>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      </tbody>\n                    </table>\n                  </div>\n\n                  <!-- Pagination -->\n                  <div v-if=\"totalPages > 1\" class=\"d-flex justify-content-between align-items-center mt-3\">\n                    <div class=\"text-muted\">\n                      Page {{ currentPage }} of {{ totalPages }}\n                    </div>\n                    <nav>\n                      <ul class=\"pagination pagination-sm mb-0\">\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === 1 }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage - 1)\" :disabled=\"currentPage === 1\">\n                            Previous\n                          </button>\n                        </li>\n                        <li\n                          v-for=\"page in visiblePages\"\n                          :key=\"page\"\n                          class=\"page-item\"\n                          :class=\"{ active: page === currentPage }\"\n                        >\n                          <button class=\"page-link\" @click=\"changePage(page)\">{{ page }}</button>\n                        </li>\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === totalPages }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage + 1)\" :disabled=\"currentPage === totalPages\">\n                            Next\n                          </button>\n                        </li>\n                      </ul>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n\n    <!-- Add User Modal -->\n    <div class=\"modal fade\" id=\"addUserModal\" tabindex=\"-1\" aria-labelledby=\"addUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"addUserModalLabel\">\n              <i class=\"fas fa-user-plus me-2\"></i>\n              Add New User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitAddUser\">\n              <!-- Basic Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addUsername\"\n                    v-model=\"addUserForm.username\"\n                    :class=\"{ 'is-invalid': formErrors.username }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.username\" class=\"invalid-feedback\">\n                    {{ formErrors.username }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addPassword\" class=\"form-label\">Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"addPassword\"\n                    v-model=\"addUserForm.password\"\n                    :class=\"{ 'is-invalid': formErrors.password }\"\n                    required\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"formErrors.password\" class=\"invalid-feedback\">\n                    {{ formErrors.password }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Personal Information -->\n              <div class=\"row\">\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addFirstName\"\n                    v-model=\"addUserForm.first_name\"\n                    :class=\"{ 'is-invalid': formErrors.first_name }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.first_name\" class=\"invalid-feedback\">\n                    {{ formErrors.first_name }}\n                  </div>\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addMiddleName\" class=\"form-label\">Middle Name</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addMiddleName\"\n                    v-model=\"addUserForm.middle_name\"\n                  >\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addLastName\"\n                    v-model=\"addUserForm.last_name\"\n                    :class=\"{ 'is-invalid': formErrors.last_name }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.last_name\" class=\"invalid-feedback\">\n                    {{ formErrors.last_name }}\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addSuffix\" class=\"form-label\">Suffix</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addSuffix\"\n                    v-model=\"addUserForm.suffix\"\n                    placeholder=\"Jr, Sr, III, etc.\"\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addRole\" class=\"form-label\">User Type *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"addRole\"\n                    v-model=\"addUserForm.role\"\n                    :class=\"{ 'is-invalid': formErrors.role }\"\n                    required\n                    @change=\"clearRoleSpecificFields\"\n                  >\n                    <option value=\"\">Select User Type</option>\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                  <div v-if=\"formErrors.role\" class=\"invalid-feedback\">\n                    {{ formErrors.role }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Contact Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addEmail\" class=\"form-label\">Email</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"addEmail\"\n                    v-model=\"addUserForm.email\"\n                    :class=\"{ 'is-invalid': formErrors.email }\"\n                  >\n                  <div v-if=\"formErrors.email\" class=\"invalid-feedback\">\n                    {{ formErrors.email }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addPhone\" class=\"form-label\">Phone Number *</label>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"addPhone\"\n                    v-model=\"addUserForm.phone_number\"\n                    :class=\"{ 'is-invalid': formErrors.phone_number }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.phone_number\" class=\"invalid-feedback\">\n                    {{ formErrors.phone_number }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Admin-specific fields -->\n              <div v-if=\"addUserForm.role === 'admin'\">\n                <hr>\n                <h6 class=\"text-primary mb-3\">\n                  <i class=\"fas fa-user-shield me-2\"></i>\n                  Administrator Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addEmployeeId\" class=\"form-label\">Employee ID</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addEmployeeId\"\n                      v-model=\"addUserForm.employee_id\"\n                      placeholder=\"e.g., EMP001\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addPosition\" class=\"form-label\">Position</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addPosition\"\n                      v-model=\"addUserForm.position\"\n                      placeholder=\"e.g., Barangay Secretary\"\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addDepartment\" class=\"form-label\">Department</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addDepartment\"\n                      v-model=\"addUserForm.department\"\n                      placeholder=\"e.g., Administration\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addHireDate\" class=\"form-label\">Hire Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"addHireDate\"\n                      v-model=\"addUserForm.hire_date\"\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Client-specific fields -->\n              <div v-if=\"addUserForm.role === 'client'\">\n                <hr>\n                <h6 class=\"text-info mb-3\">\n                  <i class=\"fas fa-user me-2\"></i>\n                  Client Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addBirthDate\" class=\"form-label\">Birth Date *</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"addBirthDate\"\n                      v-model=\"addUserForm.birth_date\"\n                      :class=\"{ 'is-invalid': formErrors.birth_date }\"\n                      required\n                    >\n                    <div v-if=\"formErrors.birth_date\" class=\"invalid-feedback\">\n                      {{ formErrors.birth_date }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addGender\" class=\"form-label\">Gender *</label>\n                    <select\n                      class=\"form-select\"\n                      id=\"addGender\"\n                      v-model=\"addUserForm.gender\"\n                      :class=\"{ 'is-invalid': formErrors.gender }\"\n                      required\n                    >\n                      <option value=\"\">Select Gender</option>\n                      <option value=\"male\">Male</option>\n                      <option value=\"female\">Female</option>\n                    </select>\n                    <div v-if=\"formErrors.gender\" class=\"invalid-feedback\">\n                      {{ formErrors.gender }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addCivilStatus\" class=\"form-label\">Civil Status *</label>\n                    <select\n                      class=\"form-select\"\n                      id=\"addCivilStatus\"\n                      v-model=\"addUserForm.civil_status_id\"\n                      required\n                    >\n                      <option value=\"1\">Single</option>\n                      <option value=\"2\">Married</option>\n                      <option value=\"3\">Widowed</option>\n                      <option value=\"4\">Divorced</option>\n                      <option value=\"5\">Separated</option>\n                    </select>\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addNationality\" class=\"form-label\">Nationality</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addNationality\"\n                      v-model=\"addUserForm.nationality\"\n                      placeholder=\"Filipino\"\n                    >\n                  </div>\n                </div>\n\n                <!-- Address Information -->\n                <h6 class=\"text-secondary mb-3 mt-4\">\n                  <i class=\"fas fa-map-marker-alt me-2\"></i>\n                  Address Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addHouseNumber\" class=\"form-label\">House Number</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addHouseNumber\"\n                      v-model=\"addUserForm.house_number\"\n                      placeholder=\"e.g., 123\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addStreet\" class=\"form-label\">Street</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addStreet\"\n                      v-model=\"addUserForm.street\"\n                      placeholder=\"e.g., Main Street\"\n                    >\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addSubdivision\" class=\"form-label\">Subdivision</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addSubdivision\"\n                      v-model=\"addUserForm.subdivision\"\n                      placeholder=\"e.g., Greenfield Village\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addBarangay\" class=\"form-label\">Barangay *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addBarangay\"\n                      v-model=\"addUserForm.barangay\"\n                      :class=\"{ 'is-invalid': formErrors.barangay }\"\n                      required\n                      placeholder=\"e.g., Barangay Bula\"\n                    >\n                    <div v-if=\"formErrors.barangay\" class=\"invalid-feedback\">\n                      {{ formErrors.barangay }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addCity\" class=\"form-label\">City/Municipality *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addCity\"\n                      v-model=\"addUserForm.city_municipality\"\n                      :class=\"{ 'is-invalid': formErrors.city_municipality }\"\n                      required\n                      placeholder=\"e.g., Camarines Sur\"\n                    >\n                    <div v-if=\"formErrors.city_municipality\" class=\"invalid-feedback\">\n                      {{ formErrors.city_municipality }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addProvince\" class=\"form-label\">Province *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addProvince\"\n                      v-model=\"addUserForm.province\"\n                      :class=\"{ 'is-invalid': formErrors.province }\"\n                      required\n                      placeholder=\"e.g., Camarines Sur\"\n                    >\n                    <div v-if=\"formErrors.province\" class=\"invalid-feedback\">\n                      {{ formErrors.province }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addPostalCode\" class=\"form-label\">Postal Code</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addPostalCode\"\n                      v-model=\"addUserForm.postal_code\"\n                      placeholder=\"e.g., 4422\"\n                    >\n                  </div>\n                </div>\n\n                <!-- Residency Information -->\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addYearsResidency\" class=\"form-label\">Years of Residency</label>\n                    <input\n                      type=\"number\"\n                      class=\"form-control\"\n                      id=\"addYearsResidency\"\n                      v-model=\"addUserForm.years_of_residency\"\n                      min=\"0\"\n                      placeholder=\"e.g., 5\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addMonthsResidency\" class=\"form-label\">Additional Months</label>\n                    <input\n                      type=\"number\"\n                      class=\"form-control\"\n                      id=\"addMonthsResidency\"\n                      v-model=\"addUserForm.months_of_residency\"\n                      min=\"0\"\n                      max=\"11\"\n                      placeholder=\"e.g., 6\"\n                    >\n                  </div>\n                </div>\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitAddUser\" :disabled=\"addUserLoading\">\n              <span v-if=\"addUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-plus me-2\"></i>\n              {{ addUserLoading ? 'Creating...' : 'Create User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Edit User Modal -->\n    <div class=\"modal fade\" id=\"editUserModal\" tabindex=\"-1\" aria-labelledby=\"editUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"editUserModalLabel\">\n              <i class=\"fas fa-user-edit me-2\"></i>\n              Edit User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitEditUser\" v-if=\"editUserForm.id\">\n              <!-- Basic Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editUsername\"\n                    v-model=\"editUserForm.username\"\n                    :class=\"{ 'is-invalid': editFormErrors.username }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.username\" class=\"invalid-feedback\">\n                    {{ editFormErrors.username }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editStatus\" class=\"form-label\">Status *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"editStatus\"\n                    v-model=\"editUserForm.status\"\n                    :class=\"{ 'is-invalid': editFormErrors.status }\"\n                    required\n                  >\n                    <option value=\"active\">Active</option>\n                    <option value=\"inactive\">Inactive</option>\n                    <option value=\"suspended\">Suspended</option>\n                    <option value=\"pending_verification\">Pending Verification</option>\n                  </select>\n                  <div v-if=\"editFormErrors.status\" class=\"invalid-feedback\">\n                    {{ editFormErrors.status }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Personal Information -->\n              <div class=\"row\">\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editFirstName\"\n                    v-model=\"editUserForm.first_name\"\n                    :class=\"{ 'is-invalid': editFormErrors.first_name }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.first_name\" class=\"invalid-feedback\">\n                    {{ editFormErrors.first_name }}\n                  </div>\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editMiddleName\" class=\"form-label\">Middle Name</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editMiddleName\"\n                    v-model=\"editUserForm.middle_name\"\n                  >\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editLastName\"\n                    v-model=\"editUserForm.last_name\"\n                    :class=\"{ 'is-invalid': editFormErrors.last_name }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.last_name\" class=\"invalid-feedback\">\n                    {{ editFormErrors.last_name }}\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editSuffix\" class=\"form-label\">Suffix</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editSuffix\"\n                    v-model=\"editUserForm.suffix\"\n                    placeholder=\"Jr, Sr, III, etc.\"\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editRole\" class=\"form-label\">User Type *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"editRole\"\n                    v-model=\"editUserForm.role\"\n                    :class=\"{ 'is-invalid': editFormErrors.role }\"\n                    required\n                    disabled\n                  >\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                  <small class=\"text-muted\">User type cannot be changed after creation</small>\n                  <div v-if=\"editFormErrors.role\" class=\"invalid-feedback\">\n                    {{ editFormErrors.role }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Contact Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editEmail\" class=\"form-label\">Email</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"editEmail\"\n                    v-model=\"editUserForm.email\"\n                    :class=\"{ 'is-invalid': editFormErrors.email }\"\n                  >\n                  <div v-if=\"editFormErrors.email\" class=\"invalid-feedback\">\n                    {{ editFormErrors.email }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editPhone\" class=\"form-label\">Phone Number *</label>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"editPhone\"\n                    v-model=\"editUserForm.phone_number\"\n                    :class=\"{ 'is-invalid': editFormErrors.phone_number }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.phone_number\" class=\"invalid-feedback\">\n                    {{ editFormErrors.phone_number }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Admin-specific fields -->\n              <div v-if=\"editUserForm.role === 'admin'\">\n                <hr>\n                <h6 class=\"text-primary mb-3\">\n                  <i class=\"fas fa-user-shield me-2\"></i>\n                  Administrator Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editEmployeeId\" class=\"form-label\">Employee ID</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editEmployeeId\"\n                      v-model=\"editUserForm.employee_id\"\n                      placeholder=\"e.g., EMP001\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editPosition\" class=\"form-label\">Position</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editPosition\"\n                      v-model=\"editUserForm.position\"\n                      placeholder=\"e.g., Barangay Secretary\"\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editDepartment\" class=\"form-label\">Department</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editDepartment\"\n                      v-model=\"editUserForm.department\"\n                      placeholder=\"e.g., Administration\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editHireDate\" class=\"form-label\">Hire Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"editHireDate\"\n                      v-model=\"editUserForm.hire_date\"\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Client-specific fields (read-only for editing) -->\n              <div v-if=\"editUserForm.role === 'client'\">\n                <hr>\n                <h6 class=\"text-info mb-3\">\n                  <i class=\"fas fa-user me-2\"></i>\n                  Client Information\n                </h6>\n                <div class=\"alert alert-info\">\n                  <i class=\"fas fa-info-circle me-2\"></i>\n                  Client-specific information can only be updated by the client through their profile.\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label class=\"form-label\">Birth Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      v-model=\"editUserForm.birth_date\"\n                      readonly\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label class=\"form-label\">Gender</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      :value=\"editUserForm.gender ? editUserForm.gender.charAt(0).toUpperCase() + editUserForm.gender.slice(1) : ''\"\n                      readonly\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-12 mb-3\">\n                    <label class=\"form-label\">Address</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      :value=\"getFullAddress(editUserForm)\"\n                      readonly\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Password Reset Section -->\n              <hr>\n              <div class=\"row\">\n                <div class=\"col-12 mb-3\">\n                  <div class=\"form-check\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"checkbox\"\n                      id=\"resetPassword\"\n                      v-model=\"editUserForm.resetPassword\"\n                    >\n                    <label class=\"form-check-label\" for=\"resetPassword\">\n                      Reset user password\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <div v-if=\"editUserForm.resetPassword\" class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editNewPassword\" class=\"form-label\">New Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"editNewPassword\"\n                    v-model=\"editUserForm.newPassword\"\n                    :class=\"{ 'is-invalid': editFormErrors.newPassword }\"\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"editFormErrors.newPassword\" class=\"invalid-feedback\">\n                    {{ editFormErrors.newPassword }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editConfirmPassword\" class=\"form-label\">Confirm Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"editConfirmPassword\"\n                    v-model=\"editUserForm.confirmPassword\"\n                    :class=\"{ 'is-invalid': editFormErrors.confirmPassword }\"\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"editFormErrors.confirmPassword\" class=\"invalid-feedback\">\n                    {{ editFormErrors.confirmPassword }}\n                  </div>\n                </div>\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitEditUser\" :disabled=\"editUserLoading\">\n              <span v-if=\"editUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-save me-2\"></i>\n              {{ editUserLoading ? 'Updating...' : 'Update User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- View User Modal -->\n    <div class=\"modal fade\" id=\"viewUserModal\" tabindex=\"-1\" aria-labelledby=\"viewUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"viewUserModalLabel\">\n              <i class=\"fas fa-user me-2\"></i>\n              User Details\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\" v-if=\"viewUserData\">\n            <div class=\"row\">\n              <div class=\"col-md-4 text-center mb-4\">\n                <div class=\"user-avatar-large mx-auto mb-3\">\n                  <img v-if=\"viewUserData.profile_picture\" :src=\"viewUserData.profile_picture\" :alt=\"viewUserData.full_name\" class=\"rounded-circle\">\n                  <div v-else class=\"avatar-placeholder-large rounded-circle\">\n                    {{ getInitials(viewUserData.full_name) }}\n                  </div>\n                </div>\n                <h5>{{ viewUserData.full_name }}</h5>\n                <span class=\"badge\" :class=\"getStatusBadgeClass(viewUserData.status)\">\n                  {{ formatStatus(viewUserData.status) }}\n                </span>\n              </div>\n              <div class=\"col-md-8\">\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Username:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.username }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Email:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.email }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Type:</strong></div>\n                  <div class=\"col-sm-8\">\n                    <span class=\"badge\" :class=\"viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                      {{ viewUserData.type === 'admin' ? 'Administrator' : 'Client' }}\n                    </span>\n                  </div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Phone:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.phone_number || 'N/A' }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Registered:</strong></div>\n                  <div class=\"col-sm-8\">{{ formatDate(viewUserData.created_at) }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Last Login:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.last_login ? formatDate(viewUserData.last_login) : 'Never' }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Close</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"editUser(viewUserData)\" data-bs-dismiss=\"modal\">\n              <i class=\"fas fa-edit me-2\"></i>\n              Edit User\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\nimport { Modal } from 'bootstrap';\n\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      },\n\n      // Modal data\n      viewUserData: null,\n      addUserLoading: false,\n      editUserLoading: false,\n\n      // Form validation errors\n      formErrors: {},\n      editFormErrors: {},\n\n      // Add user form\n      addUserForm: {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null\n      },\n\n      // Edit user form\n      editUserForm: {\n        id: null,\n        username: '',\n        email: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        status: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null,\n        // Password reset fields\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      },\n\n      // Available options\n      genderOptions: [\n        { value: 'male', label: 'Male' },\n        { value: 'female', label: 'Female' }\n      ],\n\n      statusOptions: [\n        { value: 'active', label: 'Active' },\n        { value: 'inactive', label: 'Inactive' },\n        { value: 'suspended', label: 'Suspended' },\n        { value: 'pending_verification', label: 'Pending Verification' }\n      ]\n    };\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Make bootstrap available globally for this component\n    this.$bootstrap = { Modal };\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUserStats();\n    await this.loadUsers();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load user statistics\n    async loadUserStats() {\n      try {\n        const response = await userManagementService.getUserStats();\n\n        if (response.success) {\n          this.userStats = response.data;\n        } else {\n          throw new Error(response.message || 'Failed to load user statistics');\n        }\n      } catch (error) {\n        console.error('Failed to load user statistics:', error);\n        // Fallback stats based on current users\n        this.calculateStats();\n      }\n    },\n\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50, // Load more for client-side filtering\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true :\n                     this.filterStatus === 'inactive' ? false : undefined\n        };\n\n        const response = await userManagementService.getUsers(params);\n\n        if (response.success) {\n          // Format users for display\n          this.users = response.data.users.map(user =>\n            userManagementService.formatUserData(user)\n          );\n\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.$toast?.error?.(error.message || 'Failed to load users');\n\n        // Fallback to mock data for development\n        this.users = [\n          {\n            id: 1,\n            username: 'admin12345',\n            full_name: 'System Administrator',\n            email: '<EMAIL>',\n            type: 'admin',\n            status: 'active',\n            created_at: new Date().toISOString(),\n            last_login: new Date().toISOString()\n          },\n          {\n            id: 2,\n            username: 'testapi',\n            full_name: 'Test User',\n            email: '<EMAIL>',\n            type: 'client',\n            status: 'active',\n            created_at: new Date().toISOString(),\n            last_login: null\n          }\n        ];\n\n        this.filteredUsers = [...this.users];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user =>\n          user.full_name.toLowerCase().includes(query) ||\n          user.email.toLowerCase().includes(query) ||\n          user.username.toLowerCase().includes(query)\n        );\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    // User actions\n\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n\n          this.$toast?.success?.(`User ${user.full_name} has been ${newStatus === 'active' ? 'activated' : 'suspended'}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user status. Please try again.');\n      }\n    },\n\n    async deleteUser(user) {\n      if (!confirm(`Are you sure you want to delete user \"${user.full_name}\"? This action cannot be undone.`)) {\n        return;\n      }\n\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n\n        if (response.success) {\n          // Remove from local data\n          const index = this.users.findIndex(u => u.id === user.id);\n          if (index > -1) {\n            this.users.splice(index, 1);\n            this.filterUsers();\n            this.calculateStats();\n          }\n\n          this.$toast?.success?.(`User ${user.full_name} has been deleted.`);\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        this.$toast?.error?.(error.message || 'Failed to delete user. Please try again.');\n      }\n    },\n\n\n\n    // Modal methods\n    showAddUserModal() {\n      this.resetAddUserForm();\n      try {\n        const modalElement = document.getElementById('addUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing add user modal:', error);\n        this.$toast?.error?.('Failed to open add user modal');\n      }\n    },\n\n    resetAddUserForm() {\n      this.addUserForm = {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: 'client',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        // Client specific fields\n        birth_date: '',\n        gender: 'male',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: ''\n      };\n    },\n\n    async submitAddUser() {\n      try {\n        this.addUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.addUserForm);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n\n        const response = await userManagementService.createUser(this.addUserForm);\n\n        if (response.success) {\n          this.$toast?.success?.('User created successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('addUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Reload data\n          await this.loadUsers();\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to create user');\n        }\n      } catch (error) {\n        console.error('Failed to create user:', error);\n        this.$toast?.error?.(error.message || 'Failed to create user');\n      } finally {\n        this.addUserLoading = false;\n      }\n    },\n\n    editUser(user) {\n      this.editUserForm = {\n        id: user.id,\n        username: user.username,\n        email: user.email || '',\n        first_name: user.first_name || '',\n        middle_name: user.middle_name || '',\n        last_name: user.last_name || '',\n        suffix: user.suffix || '',\n        role: user.type,\n        status: user.status,\n        phone_number: user.phone_number || '',\n        // Admin specific fields\n        position: user.position || '',\n        department: user.department || '',\n        employee_id: user.employee_id || '',\n        // Client specific fields\n        birth_date: user.birth_date ? user.birth_date.split('T')[0] : '',\n        gender: user.gender || 'male',\n        civil_status_id: user.civil_status_id || 1,\n        nationality: user.nationality || 'Filipino',\n        house_number: user.house_number || '',\n        street: user.street || '',\n        subdivision: user.subdivision || '',\n        barangay: user.barangay || '',\n        city_municipality: user.city_municipality || '',\n        province: user.province || '',\n        postal_code: user.postal_code || ''\n      };\n\n      try {\n        const modalElement = document.getElementById('editUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing edit user modal:', error);\n        this.$toast?.error?.('Failed to open edit user modal');\n      }\n    },\n\n    async submitEditUser() {\n      try {\n        this.editUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.editUserForm, true);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n\n        const response = await userManagementService.updateUser(this.editUserForm.id, this.editUserForm);\n\n        if (response.success) {\n          this.$toast?.success?.('User updated successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('editUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Update local data\n          const userIndex = this.users.findIndex(u => u.id === this.editUserForm.id);\n          if (userIndex > -1) {\n            this.users[userIndex] = { ...this.users[userIndex], ...this.editUserForm };\n            this.filterUsers();\n          }\n\n          // Reload stats\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to update user');\n        }\n      } catch (error) {\n        console.error('Failed to update user:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user');\n      } finally {\n        this.editUserLoading = false;\n      }\n    },\n\n    async viewUser(user) {\n      try {\n        const response = await userManagementService.getUser(user.id);\n\n        if (response.success) {\n          this.viewUserData = response.data;\n          try {\n            const modalElement = document.getElementById('viewUserModal');\n            if (modalElement) {\n              const modal = new Modal(modalElement);\n              modal.show();\n            }\n          } catch (error) {\n            console.error('Error showing view user modal:', error);\n            this.$toast?.error?.('Failed to open user details modal');\n          }\n        } else {\n          throw new Error(response.message || 'Failed to load user details');\n        }\n      } catch (error) {\n        console.error('Failed to load user details:', error);\n        this.$toast?.error?.(error.message || 'Failed to load user details');\n      }\n    },\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* User avatar styles */\n.user-avatar {\n  width: 40px;\n  height: 40px;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-placeholder {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  border-radius: 50%;\n}\n\n.user-avatar-large {\n  width: 80px;\n  height: 80px;\n}\n\n.avatar-placeholder-large {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 24px;\n  border-radius: 50%;\n}\n\n/* Modal styles */\n.modal-lg {\n  max-width: 800px;\n}\n\n/* Form styles */\n.form-label {\n  font-weight: 600;\n  color: #495057;\n}\n\n.form-control:focus,\n.form-select:focus {\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n/* Loading states */\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Table improvements */\n.table th {\n  border-top: none;\n  font-weight: 600;\n  color: #5a5c69;\n  font-size: 0.875rem;\n}\n\n.table td {\n  vertical-align: middle;\n  font-size: 0.875rem;\n}\n\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 123, 255, 0.05);\n}\n\n/* Button group improvements */\n.btn-group-sm .btn {\n  padding: 0.375rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n/* Search and filter improvements */\n.form-select-sm {\n  font-size: 0.875rem;\n  padding: 0.375rem 0.75rem;\n}\n\n/* Pagination improvements */\n.pagination-sm .page-link {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n}\n\n/* Badge improvements */\n.badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 0.5rem;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .table-responsive {\n    font-size: 0.8rem;\n  }\n\n  .btn-group-sm .btn {\n    padding: 0.25rem 0.375rem;\n    font-size: 0.7rem;\n  }\n\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n  }\n\n  .avatar-placeholder {\n    width: 32px;\n    height: 32px;\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    gap: 0.5rem !important;\n  }\n\n  .btn-group {\n    flex-direction: column;\n  }\n\n  .btn-group .btn {\n    border-radius: 0.375rem !important;\n    margin-bottom: 0.25rem;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;EAmBjBA,KAAK,EAAC;AAAqB;;EAUvBA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAA6D;;EAEjEA,KAAK,EAAC;AAAc;;;EAe1BA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAyC;;EAC7CA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EAY1DA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAoE;;EAKxEA,KAAK,EAAC;AAAc;;EAetBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAa;;EAarBA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAY;;;EAORA,KAAK,EAAC;;;EAQkBA,KAAK,EAAC;AAAkB;;EAG/DA,KAAK,EAAC;AAAY;;EAMXA,KAAK,EAAC;AAAkB;;EAC3BA,KAAK,EAAC;AAAmB;;EAcnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAkB;;;;EAEfA,KAAK,EAAC;;;EAKbA,KAAK,EAAC;AAAS;;EACfA,KAAK,EAAC;AAAkB;;EAiB5BA,KAAK,EAAC;AAAwB;;;;;;;EA0BlBA,KAAK,EAAC;;;EAC1BA,KAAK,EAAC;AAAY;;EAIjBA,KAAK,EAAC;AAA+B;;;;;EA+BtDA,KAAK,EAAC,YAAY;EAACC,EAAE,EAAC,cAAc;EAACC,QAAQ,EAAC,IAAI;EAAC,iBAAe,EAAC,mBAAmB;EAAC,aAAW,EAAC;;;EACjGF,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAe;;EAQnBA,KAAK,EAAC;AAAY;;EAGdA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;;EAUQA,KAAK,EAAC;;;EAInCA,KAAK,EAAC;AAAe;;;EAWQA,KAAK,EAAC;;;EAOrCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;;EAUUA,KAAK,EAAC;;;EAIrCA,KAAK,EAAC;AAAe;;EASrBA,KAAK,EAAC;AAAe;;;EAUSA,KAAK,EAAC;;;EAMtCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;;EAcIA,KAAK,EAAC;;;EAOjCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;;EASKA,KAAK,EAAC;;;EAIhCA,KAAK,EAAC;AAAe;;;EAUYA,KAAK,EAAC;;;;;;EAavCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;EAWvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;;;;EAmBvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;;EAUUA,KAAK,EAAC;;;EAIrCA,KAAK,EAAC;AAAe;;;EAaMA,KAAK,EAAC;;;EAMnCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAerBA,KAAK,EAAC;AAAe;;EAiBvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;EAYvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;;EAWQA,KAAK,EAAC;;;EAMrCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;;EAWiBA,KAAK,EAAC;;;EAI5CA,KAAK,EAAC;AAAe;;;EAWQA,KAAK,EAAC;;;EAInCA,KAAK,EAAC;AAAe;;EAavBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAWrBA,KAAK,EAAC;AAAe;;EAgB7BA,KAAK,EAAC;AAAc;;;;EAGOA,KAAK,EAAC,uCAAuC;EAACG,IAAI,EAAC;;;;EACrEH,KAAK,EAAC;;;EASrBA,KAAK,EAAC,YAAY;EAACC,EAAE,EAAC,eAAe;EAACC,QAAQ,EAAC,IAAI;EAAC,iBAAe,EAAC,oBAAoB;EAAC,aAAW,EAAC;;;EACnGF,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAe;;EAQnBA,KAAK,EAAC;AAAY;;EAGdA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;;EAUYA,KAAK,EAAC;;;EAIvCA,KAAK,EAAC;AAAe;;;EAcUA,KAAK,EAAC;;;EAOvCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;;EAUcA,KAAK,EAAC;;;EAIzCA,KAAK,EAAC;AAAe;;EASrBA,KAAK,EAAC;AAAe;;;EAUaA,KAAK,EAAC;;;EAM1CA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;;EAcQA,KAAK,EAAC;;;EAOrCA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;;EASSA,KAAK,EAAC;;;EAIpCA,KAAK,EAAC;AAAe;;;EAUgBA,KAAK,EAAC;;;;;;EAa3CA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;EAWvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;;;;EAuBvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EASrBA,KAAK,EAAC;AAAe;;;EAUvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAgB;;;EAc1BA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAY;;;EAcYA,KAAK,EAAC;;;EACtCA,KAAK,EAAC;AAAe;;;EAUeA,KAAK,EAAC;;;EAI1CA,KAAK,EAAC;AAAe;;;EAUmBA,KAAK,EAAC;;;EAOpDA,KAAK,EAAC;AAAc;;;;EAGQA,KAAK,EAAC,uCAAuC;EAACG,IAAI,EAAC;;;;EACtEH,KAAK,EAAC;;;EASrBA,KAAK,EAAC,YAAY;EAACC,EAAE,EAAC,eAAe;EAACC,QAAQ,EAAC,IAAI;EAAC,iBAAe,EAAC,oBAAoB;EAAC,aAAW,EAAC;;;EACnGF,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAe;;;EAQnBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAgC;;;;EAE7BA,KAAK,EAAC;;;EASjBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAMlBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAKxBA,KAAK,EAAC;AAAc;;;;uBA1iCjCI,mBAAA,CAojCM,OApjCNC,UAojCM,GAnjCJC,YAAA,CASEC,sBAAA;IARCC,QAAQ,EAAEC,KAAA,CAAAC,SAAS,EAAEC,UAAU;IAC/BC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBE,eAAc,EAAED,QAAA,CAAAE,mBAAmB;IACnCC,oBAAoB,EAAEH,QAAA,CAAAI,wBAAwB;IAC9CC,YAAW,EAAEL,QAAA,CAAAM,gBAAgB;IAC7BC,QAAM,EAAEP,QAAA,CAAAQ;sKAGXC,mBAAA,oBAAuB,EACvBC,mBAAA,CAIO;IAHLzB,KAAK,EAAA0B,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GACHlB,KAAA,CAAAI,gBAAgB,IAAIJ,KAAA,CAAAmB;IAAQ;IAC9CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiB,kBAAA,IAAAjB,QAAA,CAAAiB,kBAAA,IAAAD,IAAA,CAAkB;2BAG5BN,mBAAA,CA4QM,OA5QNQ,UA4QM,GA3QJ3B,YAAA,CAME4B,uBAAA;IALCC,SAAS,EAAE1B,KAAA,CAAAI,gBAAgB;IAC3BC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBsB,YAAW,EAAErB,QAAA,CAAAsB,gBAAgB;IAC7Bf,QAAM,EAAEP,QAAA,CAAAQ,YAAY;IACpBe,eAAc,EAAEvB,QAAA,CAAAE;uGAGnBQ,mBAAA,CAkQO;IAlQDzB,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,qBAAgCjB,KAAA,CAAAI;IAAgB;MACxEY,mBAAA,CAgQM,OAhQNc,UAgQM,GA/PJf,mBAAA,iBAAoB,EACpBC,mBAAA,CAgBM,OAhBNe,UAgBM,GAfJf,mBAAA,CAcM,OAdNgB,UAcM,GAbJhB,mBAAA,CAYM,OAZNiB,UAYM,GAVJjB,mBAAA,CASM,OATNkB,UASM,GARJlB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,gCAAgC;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAA6B,SAAA,IAAA7B,QAAA,CAAA6B,SAAA,IAAAb,IAAA,CAAS;IAAGc,QAAQ,EAAEpC,KAAA,CAAAqC;MAC3ErB,mBAAA,CAAoE;IAAjEzB,KAAK,EAAA0B,eAAA,EAAC,sBAAsB;MAAA,WAAsBjB,KAAA,CAAAqC;IAAO;wEAAQ,WAEtE,G,8BACArB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,wBAAwB;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAgC,gBAAA,IAAAhC,QAAA,CAAAgC,gBAAA,IAAAhB,IAAA,CAAgB;kCAC7DN,mBAAA,CAAqC;IAAlCzB,KAAK,EAAC;EAAuB,4B,iBAAK,YAEvC,E,YAMRwB,mBAAA,qBAAwB,EACxBC,mBAAA,CAqEM,OArENuB,UAqEM,GApEJvB,mBAAA,CAgBM,OAhBNwB,WAgBM,GAfJxB,mBAAA,CAcM,OAdNyB,WAcM,GAbJzB,mBAAA,CAYM,OAZN0B,WAYM,GAXJ1B,mBAAA,CAUM,OAVN2B,WAUM,GATJ3B,mBAAA,CAKM,OALN4B,WAKM,G,4BAJJ5B,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAA2D,GAAC,eAEvE,sBACAyB,mBAAA,CAAoF,OAApF6B,WAAoF,EAAAC,gBAAA,CAA7B9C,KAAA,CAAA+C,SAAS,CAACC,KAAK,sB,+BAExEhC,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAgD;IAA7CzB,KAAK,EAAC;EAAkC,G,8BAMrDyB,mBAAA,CAgBM,OAhBNiC,WAgBM,GAfJjC,mBAAA,CAcM,OAdNkC,WAcM,GAbJlC,mBAAA,CAYM,OAZNmC,WAYM,GAXJnC,mBAAA,CAUM,OAVNoC,WAUM,GATJpC,mBAAA,CAKM,OALNqC,WAKM,G,4BAJJrC,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAA2D,GAAC,gBAEvE,sBACAyB,mBAAA,CAAqF,OAArFsC,WAAqF,EAAAR,gBAAA,CAA9B9C,KAAA,CAAA+C,SAAS,CAAC7B,MAAM,sB,+BAEzEF,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAqD;IAAlDzB,KAAK,EAAC;EAAuC,G,8BAM1DyB,mBAAA,CAgBM,OAhBNuC,WAgBM,GAfJvC,mBAAA,CAcM,OAdNwC,WAcM,GAbJxC,mBAAA,CAYM,OAZNyC,WAYM,GAXJzC,mBAAA,CAUM,OAVN0C,WAUM,GATJ1C,mBAAA,CAKM,OALN2C,WAKM,G,4BAJJ3C,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAA2D,GAAC,wBAEvE,sBACAyB,mBAAA,CAAsF,OAAtF4C,WAAsF,EAAAd,gBAAA,CAA/B9C,KAAA,CAAA+C,SAAS,CAACc,OAAO,sB,+BAE1E7C,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAqD;IAAlDzB,KAAK,EAAC;EAAuC,G,8BAM1DyB,mBAAA,CAgBM,OAhBN8C,WAgBM,GAfJ9C,mBAAA,CAcM,OAdN+C,WAcM,GAbJ/C,mBAAA,CAYM,OAZNgD,WAYM,GAXJhD,mBAAA,CAUM,OAVNiD,WAUM,GATJjD,mBAAA,CAKM,OALNkD,WAKM,G,4BAJJlD,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAwD,GAAC,eAEpE,sBACAyB,mBAAA,CAAqF,OAArFmD,WAAqF,EAAArB,gBAAA,CAA9B9C,KAAA,CAAA+C,SAAS,CAACqB,MAAM,sB,+BAEzEpD,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAsD;IAAnDzB,KAAK,EAAC;EAAwC,G,gCAQ7DwB,mBAAA,iBAAoB,EACpBC,mBAAA,CAkKM,OAlKNqD,WAkKM,GAjKJrD,mBAAA,CAgKM,OAhKNsD,WAgKM,GA/JJtD,mBAAA,CA8JM,OA9JNuD,WA8JM,GA7JJvD,mBAAA,CAmBM,OAnBNwD,WAmBM,G,4BAlBJxD,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAmC,IAC3CyB,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,I,iBAAK,aAEnC,E,sBACAyB,mBAAA,CAaM,OAbNyD,WAaM,G,gBAZJzD,mBAAA,CAMS;IANDzB,KAAK,EAAC,4BAA4B;+DAAUS,KAAA,CAAA0E,YAAY,GAAAC,MAAA;IAAGC,QAAM,EAAAvD,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAuE,WAAA,IAAAvE,QAAA,CAAAuE,WAAA,IAAAvD,IAAA,CAAW;yZAAlCtB,KAAA,CAAA0E,YAAY,E,mBAOhE1D,mBAAA,CAIS;IAJDzB,KAAK,EAAC,4BAA4B;+DAAUS,KAAA,CAAA8E,UAAU,GAAAH,MAAA;IAAGC,QAAM,EAAAvD,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAuE,WAAA,IAAAvE,QAAA,CAAAuE,WAAA,IAAAvD,IAAA,CAAW;kCAClFN,mBAAA,CAAmC;IAA3B+D,KAAK,EAAC;EAAE,GAAC,WAAS,qBAC1B/D,mBAAA,CAAuC;IAA/B+D,KAAK,EAAC;EAAQ,GAAC,SAAO,qBAC9B/D,mBAAA,CAAqC;IAA7B+D,KAAK,EAAC;EAAO,GAAC,QAAM,oB,2DAHsB/E,KAAA,CAAA8E,UAAU,E,OAOlE9D,mBAAA,CAwIM,OAxINgE,WAwIM,GAvIJjE,mBAAA,gBAAmB,EACnBC,mBAAA,CAoBM,OApBNiE,WAoBM,GAnBJjE,mBAAA,CAaM,OAbNkE,WAaM,GAZJlE,mBAAA,CAWM,OAXNmE,WAWM,G,4BAVJnE,mBAAA,CAEO;IAFDzB,KAAK,EAAC;EAAkB,IAC5ByB,mBAAA,CAA6B;IAA1BzB,KAAK,EAAC;EAAe,G,sCAE1ByB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpB8F,WAAW,EAAC,6CAA6C;+DAChDrF,KAAA,CAAAsF,WAAW,GAAAX,MAAA;IACnBY,OAAK,EAAAlE,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAkF,WAAA,IAAAlF,QAAA,CAAAkF,WAAA,IAAAlE,IAAA,CAAW;iEADVtB,KAAA,CAAAsF,WAAW,E,OAK1BtE,mBAAA,CAIM,OAJNyE,WAIM,GAHJzE,mBAAA,CAEO,QAFP0E,WAEO,EAFkB,WACf,GAAA5C,gBAAA,CAAG9C,KAAA,CAAA2F,aAAa,CAACC,MAAM,IAAG,MAAI,GAAA9C,gBAAA,CAAG9C,KAAA,CAAA6F,KAAK,CAACD,MAAM,IAAG,SAC1D,gB,KAIJ7E,mBAAA,mBAAsB,EACXf,KAAA,CAAAqC,OAAO,I,cAAlB1C,mBAAA,CAKM,OALNmG,WAKM,EAAAzE,MAAA,SAAAA,MAAA,QAJJL,mBAAA,CAEM;IAFDzB,KAAK,EAAC,6BAA6B;IAACG,IAAI,EAAC;MAC5CsB,mBAAA,CAA+C;IAAzCzB,KAAK,EAAC;EAAiB,GAAC,YAAU,E,qBAE1CyB,mBAAA,CAA+C;IAA5CzB,KAAK,EAAC;EAAiB,GAAC,kBAAgB,oB,MAI7BS,KAAA,CAAA2F,aAAa,CAACC,MAAM,U,cAApCjG,mBAAA,CAMMoG,SAAA;IAAAC,GAAA;EAAA,IAPNjF,mBAAA,iBAAoB,EACpBC,mBAAA,CAMM,OANNiF,WAMM,G,4BALJjF,mBAAA,CAAqD;IAAlDzB,KAAK,EAAC;EAAuC,6B,4BAChDyB,mBAAA,CAA6C;IAAzCzB,KAAK,EAAC;EAAe,GAAC,gBAAc,sBACxCyB,mBAAA,CAEI,KAFJkF,WAEI,EAAApD,gBAAA,CADC9C,KAAA,CAAAsF,WAAW,gG,qEAKlB3F,mBAAA,CAgEMoG,SAAA;IAAAC,GAAA;EAAA,IAjENjF,mBAAA,iBAAoB,EACpBC,mBAAA,CAgEM,OAhENmF,WAgEM,GA/DJnF,mBAAA,CA8DQ,SA9DRoF,WA8DQ,G,4BA7DNpF,mBAAA,CASQ;IATDzB,KAAK,EAAC;EAAa,IACxByB,mBAAA,CAOK,aANHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAmB,YAAf,YAAU,GACdA,mBAAA,CAAgB,YAAZ,SAAO,E,wBAGfA,mBAAA,CAkDQ,iB,kBAjDNrB,mBAAA,CAgDKoG,SAAA,QAAAM,WAAA,CAhDc/F,QAAA,CAAAgG,cAAc,EAAtBC,IAAI;yBAAf5G,mBAAA,CAgDK;MAhD+BqG,GAAG,EAAEO,IAAI,CAAC/G;QAC5CwB,mBAAA,CAaK,aAZHA,mBAAA,CAWM,OAXNwF,WAWM,GAVJxF,mBAAA,CAKM,OALNyF,WAKM,GAJOF,IAAI,CAACG,eAAe,I,cAA/B/G,mBAAA,CAA0G;;MAAxEgH,GAAG,EAAEJ,IAAI,CAACG,eAAe;MAAGE,GAAG,EAAEL,IAAI,CAACM,SAAS;MAAEtH,KAAK,EAAC;2DACzFI,mBAAA,CAEM,OAFNmH,WAEM,EAAAhE,gBAAA,CADDxC,QAAA,CAAAyG,WAAW,CAACR,IAAI,CAACM,SAAS,mB,GAGjC7F,mBAAA,CAGM,cAFJA,mBAAA,CAA+C,OAA/CgG,WAA+C,EAAAlE,gBAAA,CAAvByD,IAAI,CAACM,SAAS,kBACtC7F,mBAAA,CAAwD,OAAxDiG,WAAwD,EAA1B,GAAC,GAAAnE,gBAAA,CAAGyD,IAAI,CAACW,QAAQ,iB,OAIrDlG,mBAAA,CAAyB,YAAA8B,gBAAA,CAAlByD,IAAI,CAACY,KAAK,kBACjBnG,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASsF,IAAI,CAACnB,IAAI;wBAChCmB,IAAI,CAACnB,IAAI,yD,GAGhBpE,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASX,QAAA,CAAA8G,mBAAmB,CAACb,IAAI,CAACc,MAAM;wBACtD/G,QAAA,CAAAgH,YAAY,CAACf,IAAI,CAACc,MAAM,yB,GAG/BrG,mBAAA,CAA0C,YAAA8B,gBAAA,CAAnCxC,QAAA,CAAAiH,UAAU,CAAChB,IAAI,CAACiB,UAAU,mBACjCxG,mBAAA,CAoBK,aAnBHA,mBAAA,CAkBM,OAlBNyG,WAkBM,GAjBJzG,mBAAA,CAES;MAFDzB,KAAK,EAAC,yBAAyB;MAAE6B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAoH,QAAQ,CAACnB,IAAI;MAAGoB,KAAK,EAAC;yCACpE3G,mBAAA,CAA0B;MAAvBzB,KAAK,EAAC;IAAY,2B,kCAEvByB,mBAAA,CAES;MAFDzB,KAAK,EAAC,yBAAyB;MAAE6B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAsH,QAAQ,CAACrB,IAAI;MAAGoB,KAAK,EAAC;yCACpE3G,mBAAA,CAA2B;MAAxBzB,KAAK,EAAC;IAAa,2B,kCAExByB,mBAAA,CAOS;MANPzB,KAAK,EAAA0B,eAAA,EAAC,KAAK,EACHsF,IAAI,CAACc,MAAM;MAClBjG,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAuH,gBAAgB,CAACtB,IAAI;MAC5BoB,KAAK,EAAEpB,IAAI,CAACc,MAAM;QAEnBrG,mBAAA,CAA0E;MAAtEzB,KAAK,EAAA0B,eAAA,CAAEsF,IAAI,CAACc,MAAM;mEAExBrG,mBAAA,CAES;MAFDzB,KAAK,EAAC,wBAAwB;MAAE6B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAwH,UAAU,CAACvB,IAAI;MAAGoB,KAAK,EAAC;yCACrE3G,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B;0FASrCwB,mBAAA,gBAAmB,EACRT,QAAA,CAAAyH,UAAU,Q,cAArBpI,mBAAA,CA0BM,OA1BNqI,WA0BM,GAzBJhH,mBAAA,CAEM,OAFNiH,WAEM,EAFkB,QACjB,GAAAnF,gBAAA,CAAG9C,KAAA,CAAAkI,WAAW,IAAG,MAAI,GAAApF,gBAAA,CAAGxC,QAAA,CAAAyH,UAAU,kBAEzC/G,mBAAA,CAqBM,cApBJA,mBAAA,CAmBK,MAnBLmH,WAmBK,GAlBHnH,mBAAA,CAIK;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,WAAW;MAAAmB,QAAA,EAAqBpC,KAAA,CAAAkI,WAAW;IAAA;MACnDlH,mBAAA,CAES;IAFDzB,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAsD,MAAA,IAAErE,QAAA,CAAA8H,UAAU,CAACpI,KAAA,CAAAkI,WAAW;IAAQ9F,QAAQ,EAAEpC,KAAA,CAAAkI,WAAW;KAAQ,YAE7F,iBAAAG,WAAA,E,qCAEF1I,mBAAA,CAOKoG,SAAA,QAAAM,WAAA,CANY/F,QAAA,CAAAgI,YAAY,EAApBC,IAAI;yBADb5I,mBAAA,CAOK;MALFqG,GAAG,EAAEuC,IAAI;MACVhJ,KAAK,EAAA0B,eAAA,EAAC,WAAW;QAAAC,MAAA,EACCqH,IAAI,KAAKvI,KAAA,CAAAkI;MAAW;QAEtClH,mBAAA,CAAuE;MAA/DzB,KAAK,EAAC,WAAW;MAAE6B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAA8H,UAAU,CAACG,IAAI;wBAAMA,IAAI,wBAAAC,WAAA,E;kCAE7DxH,mBAAA,CAIK;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,WAAW;MAAAmB,QAAA,EAAqBpC,KAAA,CAAAkI,WAAW,KAAK5H,QAAA,CAAAyH;IAAU;MAClE/G,mBAAA,CAES;IAFDzB,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAsD,MAAA,IAAErE,QAAA,CAAA8H,UAAU,CAACpI,KAAA,CAAAkI,WAAW;IAAQ9F,QAAQ,EAAEpC,KAAA,CAAAkI,WAAW,KAAK5H,QAAA,CAAAyH;KAAY,QAEtG,iBAAAU,WAAA,E,0FAatB1H,mBAAA,oBAAuB,EACvBC,mBAAA,CAuZM,OAvZN0H,WAuZM,GAtZJ1H,mBAAA,CAqZM,OArZN2H,WAqZM,GApZJ3H,mBAAA,CAmZM,OAnZN4H,WAmZM,G,8BAlZJ5H,mBAAA,CAMM;IANDzB,KAAK,EAAC;EAAc,IACvByB,mBAAA,CAGK;IAHDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC;MACzBwB,mBAAA,CAAqC;IAAlCzB,KAAK,EAAC;EAAuB,I,iBAAK,gBAEvC,E,GACAyB,mBAAA,CAA4F;IAApFoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,WAAW;IAAC,iBAAe,EAAC,OAAO;IAAC,YAAU,EAAC;2BAE7EyB,mBAAA,CAkYM,OAlYN6H,WAkYM,GAjYJ7H,mBAAA,CAgYO;IAhYA8H,QAAM,EAAAzH,MAAA,SAAAA,MAAA,OAAA0H,cAAA,KAAAzH,IAAA,KAAUhB,QAAA,CAAA0I,aAAA,IAAA1I,QAAA,CAAA0I,aAAA,IAAA1H,IAAA,CAAa;MAClCP,mBAAA,uBAA0B,EAC1BC,mBAAA,CA8BM,OA9BNiI,WA8BM,GA7BJjI,mBAAA,CAaM,OAbNkI,WAaM,G,4BAZJlI,mBAAA,CAA8D;IAAvDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACtDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAAClC;IAAQ;IAF3C1H,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAqJ,WAAW,CAACnC,QAAQ,GAAAvC,MAAA;IAE7B2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAAqJ,WAAW,CAACnC,QAAQ,E,GAIpBlH,KAAA,CAAAoJ,UAAU,CAAClC,QAAQ,I,cAA9BvH,mBAAA,CAEM,OAFN4J,WAEM,EAAAzG,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAAClC,QAAQ,oB,qCAG1BlG,mBAAA,CAcM,OAdNwI,WAcM,G,4BAbJxI,mBAAA,CAA8D;IAAvDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACtDyB,mBAAA,CAQC;IAPCoE,IAAI,EAAC,UAAU;IACf7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAACK;IAAQ;IAF3CjK,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAqJ,WAAW,CAACI,QAAQ,GAAA9E,MAAA;IAE7B2E,QAAQ,EAAR,EAAQ;IACRI,SAAS,EAAC;0CAHD1J,KAAA,CAAAqJ,WAAW,CAACI,QAAQ,E,GAKpBzJ,KAAA,CAAAoJ,UAAU,CAACK,QAAQ,I,cAA9B9J,mBAAA,CAEM,OAFNgK,WAEM,EAAA7G,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAACK,QAAQ,oB,uCAK5B1I,mBAAA,0BAA6B,EAC7BC,mBAAA,CAsCM,OAtCN4I,WAsCM,GArCJ5I,mBAAA,CAaM,OAbN6I,WAaM,G,4BAZJ7I,mBAAA,CAAiE;IAA1DmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,cAAY,sB,gBACzDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAAClJ;IAAU;IAF7CV,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAAqJ,WAAW,CAACnJ,UAAU,GAAAyE,MAAA;IAE/B2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAAqJ,WAAW,CAACnJ,UAAU,E,GAItBF,KAAA,CAAAoJ,UAAU,CAAClJ,UAAU,I,cAAhCP,mBAAA,CAEM,OAFNmK,WAEM,EAAAhH,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAAClJ,UAAU,oB,qCAG5Bc,mBAAA,CAQM,OARN+I,WAQM,G,4BAPJ/I,mBAAA,CAAiE;IAA1DmI,GAAG,EAAC,eAAe;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACzDyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,eAAe;iEACTQ,KAAA,CAAAqJ,WAAW,CAACW,WAAW,GAAArF,MAAA;iDAAvB3E,KAAA,CAAAqJ,WAAW,CAACW,WAAW,E,KAGpChJ,mBAAA,CAaM,OAbNiJ,WAaM,G,4BAZJjJ,mBAAA,CAA+D;IAAxDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACvDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAACc;IAAS;IAF5C1K,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAqJ,WAAW,CAACa,SAAS,GAAAvF,MAAA;IAE9B2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAAqJ,WAAW,CAACa,SAAS,E,GAIrBlK,KAAA,CAAAoJ,UAAU,CAACc,SAAS,I,cAA/BvK,mBAAA,CAEM,OAFNwK,WAEM,EAAArH,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAACc,SAAS,oB,uCAK7BlJ,mBAAA,CA6BM,OA7BNoJ,WA6BM,GA5BJpJ,mBAAA,CASM,OATNqJ,WASM,G,4BARJrJ,mBAAA,CAAwD;IAAjDmI,GAAG,EAAC,WAAW;IAAC5J,KAAK,EAAC;KAAa,QAAM,sB,gBAChDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,WAAW;iEACLQ,KAAA,CAAAqJ,WAAW,CAACiB,MAAM,GAAA3F,MAAA;IAC3BU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAACiB,MAAM,E,KAI/BtJ,mBAAA,CAiBM,OAjBNuJ,WAiBM,G,4BAhBJvJ,mBAAA,CAA2D;IAApDmI,GAAG,EAAC,SAAS;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACnDyB,mBAAA,CAWS;IAVPzB,KAAK,EAAA0B,eAAA,EAAC,aAAa;MAAA,cAGKjB,KAAA,CAAAoJ,UAAU,CAAC1J;IAAI;IAFvCF,EAAE,EAAC,SAAS;iEACHQ,KAAA,CAAAqJ,WAAW,CAAC3J,IAAI,GAAAiF,MAAA;IAEzB2E,QAAQ,EAAR,EAAQ;IACP1E,QAAM,EAAAvD,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEkJ,IAAA,CAAAC,uBAAA,IAAAD,IAAA,CAAAC,uBAAA,IAAAnJ,IAAA,CAAuB;kCAEhCN,mBAAA,CAA0C;IAAlC+D,KAAK,EAAC;EAAE,GAAC,kBAAgB,qBACjC/D,mBAAA,CAA4C;IAApC+D,KAAK,EAAC;EAAO,GAAC,eAAa,qBACnC/D,mBAAA,CAAsC;IAA9B+D,KAAK,EAAC;EAAQ,GAAC,QAAM,oB,qDAPpB/E,KAAA,CAAAqJ,WAAW,CAAC3J,IAAI,E,GAShBM,KAAA,CAAAoJ,UAAU,CAAC1J,IAAI,I,cAA1BC,mBAAA,CAEM,OAFN+K,WAEM,EAAA5H,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAAC1J,IAAI,oB,uCAKxBqB,mBAAA,yBAA4B,EAC5BC,mBAAA,CA4BM,OA5BN2J,WA4BM,GA3BJ3J,mBAAA,CAYM,OAZN4J,WAYM,G,4BAXJ5J,mBAAA,CAAsD;IAA/CmI,GAAG,EAAC,UAAU;IAAC5J,KAAK,EAAC;KAAa,OAAK,sB,gBAC9CyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,OAAO;IACZ7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAACjC;IAAK;IAFxC3H,EAAE,EAAC,UAAU;iEACJQ,KAAA,CAAAqJ,WAAW,CAAClC,KAAK,GAAAxC,MAAA;0CAAjB3E,KAAA,CAAAqJ,WAAW,CAAClC,KAAK,E,GAGjBnH,KAAA,CAAAoJ,UAAU,CAACjC,KAAK,I,cAA3BxH,mBAAA,CAEM,OAFNkL,WAEM,EAAA/H,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAACjC,KAAK,oB,qCAGvBnG,mBAAA,CAaM,OAbN8J,WAaM,G,4BAZJ9J,mBAAA,CAA+D;IAAxDmI,GAAG,EAAC,UAAU;IAAC5J,KAAK,EAAC;KAAa,gBAAc,sB,gBACvDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,KAAK;IACV7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAAC2B;IAAY;IAF/CvL,EAAE,EAAC,UAAU;iEACJQ,KAAA,CAAAqJ,WAAW,CAAC0B,YAAY,GAAApG,MAAA;IAEjC2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAAqJ,WAAW,CAAC0B,YAAY,E,GAIxB/K,KAAA,CAAAoJ,UAAU,CAAC2B,YAAY,I,cAAlCpL,mBAAA,CAEM,OAFNqL,WAEM,EAAAlI,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAAC2B,YAAY,oB,uCAKhChK,mBAAA,2BAA8B,EACnBf,KAAA,CAAAqJ,WAAW,CAAC3J,IAAI,gB,cAA3BC,mBAAA,CAiDM,OAAAsL,WAAA,G,4BAhDJjK,mBAAA,CAAI,sC,4BACJA,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAmB,IAC3ByB,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,I,iBAAK,6BAEzC,E,sBACAyB,mBAAA,CAqBM,OArBNkK,WAqBM,GApBJlK,mBAAA,CASM,OATNmK,WASM,G,4BARJnK,mBAAA,CAAiE;IAA1DmI,GAAG,EAAC,eAAe;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACzDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,eAAe;iEACTQ,KAAA,CAAAqJ,WAAW,CAAC+B,WAAW,GAAAzG,MAAA;IAChCU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAAC+B,WAAW,E,KAIpCpK,mBAAA,CASM,OATNqK,WASM,G,4BARJrK,mBAAA,CAA4D;IAArDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,UAAQ,sB,gBACpDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAqJ,WAAW,CAACiC,QAAQ,GAAA3G,MAAA;IAC7BU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAACiC,QAAQ,E,OAKnCtK,mBAAA,CAoBM,OApBNuK,WAoBM,GAnBJvK,mBAAA,CASM,OATNwK,WASM,G,4BARJxK,mBAAA,CAAgE;IAAzDmI,GAAG,EAAC,eAAe;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACxDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,eAAe;iEACTQ,KAAA,CAAAqJ,WAAW,CAACoC,UAAU,GAAA9G,MAAA;IAC/BU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAACoC,UAAU,E,KAInCzK,mBAAA,CAQM,OARN0K,WAQM,G,4BAPJ1K,mBAAA,CAA6D;IAAtDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,WAAS,sB,gBACrDyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAqJ,WAAW,CAACsC,SAAS,GAAAhH,MAAA;iDAArB3E,KAAA,CAAAqJ,WAAW,CAACsC,SAAS,E,8CAMtC5K,mBAAA,4BAA+B,EACpBf,KAAA,CAAAqJ,WAAW,CAAC3J,IAAI,iB,cAA3BC,mBAAA,CAiMM,OAAAiM,WAAA,G,8BAhMJ5K,mBAAA,CAAI,sC,8BACJA,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAgB,IACxByB,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,I,iBAAK,sBAElC,E,sBACAyB,mBAAA,CAgCM,OAhCN6K,WAgCM,GA/BJ7K,mBAAA,CAaM,OAbN8K,YAaM,G,4BAZJ9K,mBAAA,CAAiE;IAA1DmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,cAAY,sB,gBACzDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAAC2C;IAAU;IAF7CvM,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAAqJ,WAAW,CAAC0C,UAAU,GAAApH,MAAA;IAE/B2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAAqJ,WAAW,CAAC0C,UAAU,E,GAItB/L,KAAA,CAAAoJ,UAAU,CAAC2C,UAAU,I,cAAhCpM,mBAAA,CAEM,OAFNqM,YAEM,EAAAlJ,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAAC2C,UAAU,oB,qCAG5B/K,mBAAA,CAgBM,OAhBNiL,YAgBM,G,4BAfJjL,mBAAA,CAA0D;IAAnDmI,GAAG,EAAC,WAAW;IAAC5J,KAAK,EAAC;KAAa,UAAQ,sB,gBAClDyB,mBAAA,CAUS;IATPzB,KAAK,EAAA0B,eAAA,EAAC,aAAa;MAAA,cAGKjB,KAAA,CAAAoJ,UAAU,CAAC8C;IAAM;IAFzC1M,EAAE,EAAC,WAAW;iEACLQ,KAAA,CAAAqJ,WAAW,CAAC6C,MAAM,GAAAvH,MAAA;IAE3B2E,QAAQ,EAAR;kCAEAtI,mBAAA,CAAuC;IAA/B+D,KAAK,EAAC;EAAE,GAAC,eAAa,qBAC9B/D,mBAAA,CAAkC;IAA1B+D,KAAK,EAAC;EAAM,GAAC,MAAI,qBACzB/D,mBAAA,CAAsC;IAA9B+D,KAAK,EAAC;EAAQ,GAAC,QAAM,oB,oCANpB/E,KAAA,CAAAqJ,WAAW,CAAC6C,MAAM,E,GAQlBlM,KAAA,CAAAoJ,UAAU,CAAC8C,MAAM,I,cAA5BvM,mBAAA,CAEM,OAFNwM,YAEM,EAAArJ,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAAC8C,MAAM,oB,uCAK1BlL,mBAAA,CA0BM,OA1BNoL,YA0BM,GAzBJpL,mBAAA,CAcM,OAdNqL,YAcM,G,8BAbJrL,mBAAA,CAAqE;IAA9DmI,GAAG,EAAC,gBAAgB;IAAC5J,KAAK,EAAC;KAAa,gBAAc,sB,gBAC7DyB,mBAAA,CAWS;IAVPzB,KAAK,EAAC,aAAa;IACnBC,EAAE,EAAC,gBAAgB;iEACVQ,KAAA,CAAAqJ,WAAW,CAACiD,eAAe,GAAA3H,MAAA;IACpC2E,QAAQ,EAAR;+WADStJ,KAAA,CAAAqJ,WAAW,CAACiD,eAAe,E,KAUxCtL,mBAAA,CASM,OATNuL,YASM,G,8BARJvL,mBAAA,CAAkE;IAA3DmI,GAAG,EAAC,gBAAgB;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBAC1DyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,gBAAgB;iEACVQ,KAAA,CAAAqJ,WAAW,CAACmD,WAAW,GAAA7H,MAAA;IAChCU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAACmD,WAAW,E,OAMtCzL,mBAAA,yBAA4B,E,8BAC5BC,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAA0B,IAClCyB,mBAAA,CAA0C;IAAvCzB,KAAK,EAAC;EAA4B,I,iBAAK,uBAE5C,E,sBACAyB,mBAAA,CAqBM,OArBNyL,YAqBM,GApBJzL,mBAAA,CASM,OATN0L,YASM,G,8BARJ1L,mBAAA,CAAmE;IAA5DmI,GAAG,EAAC,gBAAgB;IAAC5J,KAAK,EAAC;KAAa,cAAY,sB,gBAC3DyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,gBAAgB;iEACVQ,KAAA,CAAAqJ,WAAW,CAACsD,YAAY,GAAAhI,MAAA;IACjCU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAACsD,YAAY,E,KAIrC3L,mBAAA,CASM,OATN4L,YASM,G,8BARJ5L,mBAAA,CAAwD;IAAjDmI,GAAG,EAAC,WAAW;IAAC5J,KAAK,EAAC;KAAa,QAAM,sB,gBAChDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,WAAW;iEACLQ,KAAA,CAAAqJ,WAAW,CAACwD,MAAM,GAAAlI,MAAA;IAC3BU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAACwD,MAAM,E,OAMjC7L,mBAAA,CA0BM,OA1BN8L,YA0BM,GAzBJ9L,mBAAA,CASM,OATN+L,YASM,G,8BARJ/L,mBAAA,CAAkE;IAA3DmI,GAAG,EAAC,gBAAgB;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBAC1DyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,gBAAgB;iEACVQ,KAAA,CAAAqJ,WAAW,CAAC2D,WAAW,GAAArI,MAAA;IAChCU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAAC2D,WAAW,E,KAIpChM,mBAAA,CAcM,OAdNiM,YAcM,G,8BAbJjM,mBAAA,CAA8D;IAAvDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACtDyB,mBAAA,CAQC;IAPCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAAC8D;IAAQ;IAF3C1N,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAqJ,WAAW,CAAC6D,QAAQ,GAAAvI,MAAA;IAE7B2E,QAAQ,EAAR,EAAQ;IACRjE,WAAW,EAAC;0CAHHrF,KAAA,CAAAqJ,WAAW,CAAC6D,QAAQ,E,GAKpBlN,KAAA,CAAAoJ,UAAU,CAAC8D,QAAQ,I,cAA9BvN,mBAAA,CAEM,OAFNwN,YAEM,EAAArK,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAAC8D,QAAQ,oB,uCAK5BlM,mBAAA,CAyCM,OAzCNoM,YAyCM,GAxCJpM,mBAAA,CAcM,OAdNqM,YAcM,G,8BAbJrM,mBAAA,CAAmE;IAA5DmI,GAAG,EAAC,SAAS;IAAC5J,KAAK,EAAC;KAAa,qBAAmB,sB,gBAC3DyB,mBAAA,CAQC;IAPCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAACkE;IAAiB;IAFpD9N,EAAE,EAAC,SAAS;iEACHQ,KAAA,CAAAqJ,WAAW,CAACiE,iBAAiB,GAAA3I,MAAA;IAEtC2E,QAAQ,EAAR,EAAQ;IACRjE,WAAW,EAAC;0CAHHrF,KAAA,CAAAqJ,WAAW,CAACiE,iBAAiB,E,GAK7BtN,KAAA,CAAAoJ,UAAU,CAACkE,iBAAiB,I,cAAvC3N,mBAAA,CAEM,OAFN4N,YAEM,EAAAzK,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAACkE,iBAAiB,oB,qCAGnCtM,mBAAA,CAcM,OAdNwM,YAcM,G,8BAbJxM,mBAAA,CAA8D;IAAvDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACtDyB,mBAAA,CAQC;IAPCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAoJ,UAAU,CAACqE;IAAQ;IAF3CjO,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAqJ,WAAW,CAACoE,QAAQ,GAAA9I,MAAA;IAE7B2E,QAAQ,EAAR,EAAQ;IACRjE,WAAW,EAAC;0CAHHrF,KAAA,CAAAqJ,WAAW,CAACoE,QAAQ,E,GAKpBzN,KAAA,CAAAoJ,UAAU,CAACqE,QAAQ,I,cAA9B9N,mBAAA,CAEM,OAFN+N,YAEM,EAAA5K,gBAAA,CADD9C,KAAA,CAAAoJ,UAAU,CAACqE,QAAQ,oB,qCAG1BzM,mBAAA,CASM,OATN2M,YASM,G,8BARJ3M,mBAAA,CAAiE;IAA1DmI,GAAG,EAAC,eAAe;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACzDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,eAAe;iEACTQ,KAAA,CAAAqJ,WAAW,CAACuE,WAAW,GAAAjJ,MAAA;IAChCU,WAAW,EAAC;iDADHrF,KAAA,CAAAqJ,WAAW,CAACuE,WAAW,E,OAMtC7M,mBAAA,2BAA8B,EAC9BC,mBAAA,CAwBM,OAxBN6M,YAwBM,GAvBJ7M,mBAAA,CAUM,OAVN8M,YAUM,G,8BATJ9M,mBAAA,CAA4E;IAArEmI,GAAG,EAAC,mBAAmB;IAAC5J,KAAK,EAAC;KAAa,oBAAkB,sB,gBACpEyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,QAAQ;IACb7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,mBAAmB;iEACbQ,KAAA,CAAAqJ,WAAW,CAAC0E,kBAAkB,GAAApJ,MAAA;IACvCqJ,GAAG,EAAC,GAAG;IACP3I,WAAW,EAAC;iDAFHrF,KAAA,CAAAqJ,WAAW,CAAC0E,kBAAkB,E,KAK3C/M,mBAAA,CAWM,OAXNiN,YAWM,G,8BAVJjN,mBAAA,CAA4E;IAArEmI,GAAG,EAAC,oBAAoB;IAAC5J,KAAK,EAAC;KAAa,mBAAiB,sB,gBACpEyB,mBAAA,CAQC;IAPCoE,IAAI,EAAC,QAAQ;IACb7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,oBAAoB;iEACdQ,KAAA,CAAAqJ,WAAW,CAAC6E,mBAAmB,GAAAvJ,MAAA;IACxCqJ,GAAG,EAAC,GAAG;IACPG,GAAG,EAAC,IAAI;IACR9I,WAAW,EAAC;iDAHHrF,KAAA,CAAAqJ,WAAW,CAAC6E,mBAAmB,E,2EAUpDlN,mBAAA,CAOM,OAPNoN,YAOM,G,8BANJpN,mBAAA,CAAuF;IAA/EoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,mBAAmB;IAAC,iBAAe,EAAC;KAAQ,QAAM,sBAC9EyB,mBAAA,CAIS;IAJDoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA0I,aAAA,IAAA1I,QAAA,CAAA0I,aAAA,IAAA1H,IAAA,CAAa;IAAGc,QAAQ,EAAEpC,KAAA,CAAAqO;MAClErO,KAAA,CAAAqO,cAAc,I,cAA1B1O,mBAAA,CAA+F,QAA/F2O,YAA+F,M,cAC/F3O,mBAAA,CAAuC,KAAvC4O,YAAuC,I,iBAAA,GACvC,GAAAzL,gBAAA,CAAG9C,KAAA,CAAAqO,cAAc,iD,wCAO3BtN,mBAAA,qBAAwB,EACxBC,mBAAA,CAmTM,OAnTNwN,YAmTM,GAlTJxN,mBAAA,CAiTM,OAjTNyN,YAiTM,GAhTJzN,mBAAA,CA+SM,OA/SN0N,YA+SM,G,8BA9SJ1N,mBAAA,CAMM;IANDzB,KAAK,EAAC;EAAc,IACvByB,mBAAA,CAGK;IAHDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC;MACzBwB,mBAAA,CAAqC;IAAlCzB,KAAK,EAAC;EAAuB,I,iBAAK,aAEvC,E,GACAyB,mBAAA,CAA4F;IAApFoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,WAAW;IAAC,iBAAe,EAAC,OAAO;IAAC,YAAU,EAAC;2BAE7EyB,mBAAA,CA8RM,OA9RN2N,YA8RM,GA7RyC3O,KAAA,CAAA4O,YAAY,CAACpP,EAAE,I,cAA5DG,mBAAA,CA4RO;;IA5RAmJ,QAAM,EAAAzH,MAAA,SAAAA,MAAA,OAAA0H,cAAA,KAAAzH,IAAA,KAAUhB,QAAA,CAAAuO,cAAA,IAAAvO,QAAA,CAAAuO,cAAA,IAAAvN,IAAA,CAAc;MACnCP,mBAAA,uBAA0B,EAC1BC,mBAAA,CAiCM,OAjCN8N,YAiCM,GAhCJ9N,mBAAA,CAaM,OAbN+N,YAaM,G,8BAZJ/N,mBAAA,CAA+D;IAAxDmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACvDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAgP,cAAc,CAAC9H;IAAQ;IAF/C1H,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAA4O,YAAY,CAAC1H,QAAQ,GAAAvC,MAAA;IAE9B2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAA4O,YAAY,CAAC1H,QAAQ,E,GAIrBlH,KAAA,CAAAgP,cAAc,CAAC9H,QAAQ,I,cAAlCvH,mBAAA,CAEM,OAFNsP,YAEM,EAAAnM,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAAC9H,QAAQ,oB,qCAG9BlG,mBAAA,CAiBM,OAjBNkO,YAiBM,G,8BAhBJlO,mBAAA,CAA2D;IAApDmI,GAAG,EAAC,YAAY;IAAC5J,KAAK,EAAC;KAAa,UAAQ,sB,gBACnDyB,mBAAA,CAWS;IAVPzB,KAAK,EAAA0B,eAAA,EAAC,aAAa;MAAA,cAGKjB,KAAA,CAAAgP,cAAc,CAAC3H;IAAM;IAF7C7H,EAAE,EAAC,YAAY;iEACNQ,KAAA,CAAA4O,YAAY,CAACvH,MAAM,GAAA1C,MAAA;IAE5B2E,QAAQ,EAAR;oCAEAtI,mBAAA,CAAsC;IAA9B+D,KAAK,EAAC;EAAQ,GAAC,QAAM,qBAC7B/D,mBAAA,CAA0C;IAAlC+D,KAAK,EAAC;EAAU,GAAC,UAAQ,qBACjC/D,mBAAA,CAA4C;IAApC+D,KAAK,EAAC;EAAW,GAAC,WAAS,qBACnC/D,mBAAA,CAAkE;IAA1D+D,KAAK,EAAC;EAAsB,GAAC,sBAAoB,oB,oCAPhD/E,KAAA,CAAA4O,YAAY,CAACvH,MAAM,E,GASnBrH,KAAA,CAAAgP,cAAc,CAAC3H,MAAM,I,cAAhC1H,mBAAA,CAEM,OAFNwP,YAEM,EAAArM,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAAC3H,MAAM,oB,uCAK9BtG,mBAAA,0BAA6B,EAC7BC,mBAAA,CAsCM,OAtCNoO,YAsCM,GArCJpO,mBAAA,CAaM,OAbNqO,YAaM,G,8BAZJrO,mBAAA,CAAkE;IAA3DmI,GAAG,EAAC,eAAe;IAAC5J,KAAK,EAAC;KAAa,cAAY,sB,gBAC1DyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAgP,cAAc,CAAC9O;IAAU;IAFjDV,EAAE,EAAC,eAAe;iEACTQ,KAAA,CAAA4O,YAAY,CAAC1O,UAAU,GAAAyE,MAAA;IAEhC2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAA4O,YAAY,CAAC1O,UAAU,E,GAIvBF,KAAA,CAAAgP,cAAc,CAAC9O,UAAU,I,cAApCP,mBAAA,CAEM,OAFN2P,YAEM,EAAAxM,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAAC9O,UAAU,oB,qCAGhCc,mBAAA,CAQM,OARNuO,YAQM,G,8BAPJvO,mBAAA,CAAkE;IAA3DmI,GAAG,EAAC,gBAAgB;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBAC1DyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,gBAAgB;iEACVQ,KAAA,CAAA4O,YAAY,CAAC5E,WAAW,GAAArF,MAAA;iDAAxB3E,KAAA,CAAA4O,YAAY,CAAC5E,WAAW,E,KAGrChJ,mBAAA,CAaM,OAbNwO,YAaM,G,8BAZJxO,mBAAA,CAAgE;IAAzDmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACxDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAgP,cAAc,CAAC9E;IAAS;IAFhD1K,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAA4O,YAAY,CAAC1E,SAAS,GAAAvF,MAAA;IAE/B2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAA4O,YAAY,CAAC1E,SAAS,E,GAItBlK,KAAA,CAAAgP,cAAc,CAAC9E,SAAS,I,cAAnCvK,mBAAA,CAEM,OAFN8P,YAEM,EAAA3M,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAAC9E,SAAS,oB,uCAKjClJ,mBAAA,CA6BM,OA7BN0O,YA6BM,GA5BJ1O,mBAAA,CASM,OATN2O,YASM,G,8BARJ3O,mBAAA,CAAyD;IAAlDmI,GAAG,EAAC,YAAY;IAAC5J,KAAK,EAAC;KAAa,QAAM,sB,gBACjDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,YAAY;iEACNQ,KAAA,CAAA4O,YAAY,CAACtE,MAAM,GAAA3F,MAAA;IAC5BU,WAAW,EAAC;iDADHrF,KAAA,CAAA4O,YAAY,CAACtE,MAAM,E,KAIhCtJ,mBAAA,CAiBM,OAjBN4O,YAiBM,G,8BAhBJ5O,mBAAA,CAA4D;IAArDmI,GAAG,EAAC,UAAU;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACpDyB,mBAAA,CAUS;IATPzB,KAAK,EAAA0B,eAAA,EAAC,aAAa;MAAA,cAGKjB,KAAA,CAAAgP,cAAc,CAACtP;IAAI;IAF3CF,EAAE,EAAC,UAAU;iEACJQ,KAAA,CAAA4O,YAAY,CAAClP,IAAI,GAAAiF,MAAA;IAE1B2E,QAAQ,EAAR,EAAQ;IACRlH,QAAQ,EAAR;oCAEApB,mBAAA,CAA4C;IAApC+D,KAAK,EAAC;EAAO,GAAC,eAAa,qBACnC/D,mBAAA,CAAsC;IAA9B+D,KAAK,EAAC;EAAQ,GAAC,QAAM,oB,oCANpB/E,KAAA,CAAA4O,YAAY,CAAClP,IAAI,E,iCAQ5BsB,mBAAA,CAA4E;IAArEzB,KAAK,EAAC;EAAY,GAAC,4CAA0C,sBACzDS,KAAA,CAAAgP,cAAc,CAACtP,IAAI,I,cAA9BC,mBAAA,CAEM,OAFNkQ,YAEM,EAAA/M,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAACtP,IAAI,oB,uCAK5BqB,mBAAA,yBAA4B,EAC5BC,mBAAA,CA4BM,OA5BN8O,YA4BM,GA3BJ9O,mBAAA,CAYM,OAZN+O,YAYM,G,8BAXJ/O,mBAAA,CAAuD;IAAhDmI,GAAG,EAAC,WAAW;IAAC5J,KAAK,EAAC;KAAa,OAAK,sB,gBAC/CyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,OAAO;IACZ7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAgP,cAAc,CAAC7H;IAAK;IAF5C3H,EAAE,EAAC,WAAW;iEACLQ,KAAA,CAAA4O,YAAY,CAACzH,KAAK,GAAAxC,MAAA;0CAAlB3E,KAAA,CAAA4O,YAAY,CAACzH,KAAK,E,GAGlBnH,KAAA,CAAAgP,cAAc,CAAC7H,KAAK,I,cAA/BxH,mBAAA,CAEM,OAFNqQ,YAEM,EAAAlN,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAAC7H,KAAK,oB,qCAG3BnG,mBAAA,CAaM,OAbNiP,YAaM,G,8BAZJjP,mBAAA,CAAgE;IAAzDmI,GAAG,EAAC,WAAW;IAAC5J,KAAK,EAAC;KAAa,gBAAc,sB,gBACxDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,KAAK;IACV7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAgP,cAAc,CAACjE;IAAY;IAFnDvL,EAAE,EAAC,WAAW;iEACLQ,KAAA,CAAA4O,YAAY,CAAC7D,YAAY,GAAApG,MAAA;IAElC2E,QAAQ,EAAR;0CAFStJ,KAAA,CAAA4O,YAAY,CAAC7D,YAAY,E,GAIzB/K,KAAA,CAAAgP,cAAc,CAACjE,YAAY,I,cAAtCpL,mBAAA,CAEM,OAFNuQ,YAEM,EAAApN,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAACjE,YAAY,oB,uCAKpChK,mBAAA,2BAA8B,EACnBf,KAAA,CAAA4O,YAAY,CAAClP,IAAI,gB,cAA5BC,mBAAA,CAiDM,OAAAwQ,YAAA,G,8BAhDJnP,mBAAA,CAAI,sC,8BACJA,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAmB,IAC3ByB,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,I,iBAAK,6BAEzC,E,sBACAyB,mBAAA,CAqBM,OArBNoP,YAqBM,GApBJpP,mBAAA,CASM,OATNqP,YASM,G,8BARJrP,mBAAA,CAAkE;IAA3DmI,GAAG,EAAC,gBAAgB;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBAC1DyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,gBAAgB;iEACVQ,KAAA,CAAA4O,YAAY,CAACxD,WAAW,GAAAzG,MAAA;IACjCU,WAAW,EAAC;iDADHrF,KAAA,CAAA4O,YAAY,CAACxD,WAAW,E,KAIrCpK,mBAAA,CASM,OATNsP,YASM,G,8BARJtP,mBAAA,CAA6D;IAAtDmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,UAAQ,sB,gBACrDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAA4O,YAAY,CAACtD,QAAQ,GAAA3G,MAAA;IAC9BU,WAAW,EAAC;iDADHrF,KAAA,CAAA4O,YAAY,CAACtD,QAAQ,E,OAKpCtK,mBAAA,CAoBM,OApBNuP,YAoBM,GAnBJvP,mBAAA,CASM,OATNwP,YASM,G,8BARJxP,mBAAA,CAAiE;IAA1DmI,GAAG,EAAC,gBAAgB;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACzDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,gBAAgB;iEACVQ,KAAA,CAAA4O,YAAY,CAACnD,UAAU,GAAA9G,MAAA;IAChCU,WAAW,EAAC;iDADHrF,KAAA,CAAA4O,YAAY,CAACnD,UAAU,E,KAIpCzK,mBAAA,CAQM,OARNyP,YAQM,G,8BAPJzP,mBAAA,CAA8D;IAAvDmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,WAAS,sB,gBACtDyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAA4O,YAAY,CAACjD,SAAS,GAAAhH,MAAA;iDAAtB3E,KAAA,CAAA4O,YAAY,CAACjD,SAAS,E,8CAMvC5K,mBAAA,oDAAuD,EAC5Cf,KAAA,CAAA4O,YAAY,CAAClP,IAAI,iB,cAA5BC,mBAAA,CAyCM,OAAA+Q,YAAA,G,8BAxCJ1P,mBAAA,CAAI,sC,8BACJA,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAgB,IACxByB,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,I,iBAAK,sBAElC,E,oDACAyB,mBAAA,CAGM;IAHDzB,KAAK,EAAC;EAAkB,IAC3ByB,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,I,iBAAK,wFAEzC,E,sBACAyB,mBAAA,CAmBM,OAnBN2P,YAmBM,GAlBJ3P,mBAAA,CAQM,OARN4P,YAQM,G,8BAPJ5P,mBAAA,CAA4C;IAArCzB,KAAK,EAAC;EAAY,GAAC,YAAU,sB,gBACpCyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;iEACXS,KAAA,CAAA4O,YAAY,CAAC7C,UAAU,GAAApH,MAAA;IAChCkM,QAAQ,EAAR;iDADS7Q,KAAA,CAAA4O,YAAY,CAAC7C,UAAU,E,KAIpC/K,mBAAA,CAQM,OARN8P,YAQM,G,8BAPJ9P,mBAAA,CAAwC;IAAjCzB,KAAK,EAAC;EAAY,GAAC,QAAM,sBAChCyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACnBwF,KAAK,EAAE/E,KAAA,CAAA4O,YAAY,CAAC1C,MAAM,GAAGlM,KAAA,CAAA4O,YAAY,CAAC1C,MAAM,CAAC6E,MAAM,IAAIC,WAAW,KAAKhR,KAAA,CAAA4O,YAAY,CAAC1C,MAAM,CAAC+E,KAAK;IACrGJ,QAAQ,EAAR;6CAIN7P,mBAAA,CAUM,OAVNkQ,YAUM,GATJlQ,mBAAA,CAQM,OARNmQ,YAQM,G,8BAPJnQ,mBAAA,CAAyC;IAAlCzB,KAAK,EAAC;EAAY,GAAC,SAAO,sBACjCyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACnBwF,KAAK,EAAEyF,IAAA,CAAA4G,cAAc,CAACpR,KAAA,CAAA4O,YAAY;IACnCiC,QAAQ,EAAR;oFAMR9P,mBAAA,4BAA+B,E,8BAC/BC,mBAAA,CAAI,sCACJA,mBAAA,CAcM,OAdNqQ,YAcM,GAbJrQ,mBAAA,CAYM,OAZNsQ,YAYM,GAXJtQ,mBAAA,CAUM,OAVNuQ,YAUM,G,gBATJvQ,mBAAA,CAKC;IAJCzB,KAAK,EAAC,kBAAkB;IACxB6F,IAAI,EAAC,UAAU;IACf5F,EAAE,EAAC,eAAe;iEACTQ,KAAA,CAAA4O,YAAY,CAAC4C,aAAa,GAAA7M,MAAA;qDAA1B3E,KAAA,CAAA4O,YAAY,CAAC4C,aAAa,E,iCAErCxQ,mBAAA,CAEQ;IAFDzB,KAAK,EAAC,kBAAkB;IAAC4J,GAAG,EAAC;KAAgB,uBAEpD,qB,OAKKnJ,KAAA,CAAA4O,YAAY,CAAC4C,aAAa,I,cAArC7R,mBAAA,CA6BM,OA7BN8R,YA6BM,GA5BJzQ,mBAAA,CAaM,OAbN0Q,YAaM,G,8BAZJ1Q,mBAAA,CAAsE;IAA/DmI,GAAG,EAAC,iBAAiB;IAAC5J,KAAK,EAAC;KAAa,gBAAc,sB,gBAC9DyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,UAAU;IACf7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAgP,cAAc,CAAC2C;IAAW;IAFlDnS,EAAE,EAAC,iBAAiB;iEACXQ,KAAA,CAAA4O,YAAY,CAAC+C,WAAW,GAAAhN,MAAA;IAEjC+E,SAAS,EAAC;0CAFD1J,KAAA,CAAA4O,YAAY,CAAC+C,WAAW,E,GAIxB3R,KAAA,CAAAgP,cAAc,CAAC2C,WAAW,I,cAArChS,mBAAA,CAEM,OAFNiS,YAEM,EAAA9O,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAAC2C,WAAW,oB,qCAGjC3Q,mBAAA,CAaM,OAbN6Q,YAaM,G,8BAZJ7Q,mBAAA,CAA8E;IAAvEmI,GAAG,EAAC,qBAAqB;IAAC5J,KAAK,EAAC;KAAa,oBAAkB,sB,gBACtEyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,UAAU;IACf7F,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAgP,cAAc,CAAC8C;IAAe;IAFtDtS,EAAE,EAAC,qBAAqB;iEACfQ,KAAA,CAAA4O,YAAY,CAACkD,eAAe,GAAAnN,MAAA;IAErC+E,SAAS,EAAC;0CAFD1J,KAAA,CAAA4O,YAAY,CAACkD,eAAe,E,GAI5B9R,KAAA,CAAAgP,cAAc,CAAC8C,eAAe,I,cAAzCnS,mBAAA,CAEM,OAFNoS,YAEM,EAAAjP,gBAAA,CADD9C,KAAA,CAAAgP,cAAc,CAAC8C,eAAe,oB,8IAM3C9Q,mBAAA,CAOM,OAPNgR,YAOM,G,8BANJhR,mBAAA,CAAuF;IAA/EoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,mBAAmB;IAAC,iBAAe,EAAC;KAAQ,QAAM,sBAC9EyB,mBAAA,CAIS;IAJDoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAuO,cAAA,IAAAvO,QAAA,CAAAuO,cAAA,IAAAvN,IAAA,CAAc;IAAGc,QAAQ,EAAEpC,KAAA,CAAAiS;MACnEjS,KAAA,CAAAiS,eAAe,I,cAA3BtS,mBAAA,CAAgG,QAAhGuS,YAAgG,M,cAChGvS,mBAAA,CAAuC,KAAvCwS,YAAuC,I,iBAAA,GACvC,GAAArP,gBAAA,CAAG9C,KAAA,CAAAiS,eAAe,iD,wCAO5BlR,mBAAA,qBAAwB,EACxBC,mBAAA,CAiEM,OAjENoR,YAiEM,GAhEJpR,mBAAA,CA+DM,OA/DNqR,YA+DM,GA9DJrR,mBAAA,CA6DM,OA7DNsR,YA6DM,G,8BA5DJtR,mBAAA,CAMM;IANDzB,KAAK,EAAC;EAAc,IACvByB,mBAAA,CAGK;IAHDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC;MACzBwB,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,I,iBAAK,gBAElC,E,GACAyB,mBAAA,CAA4F;IAApFoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,WAAW;IAAC,iBAAe,EAAC,OAAO;IAAC,YAAU,EAAC;2BAE/CS,KAAA,CAAAuS,YAAY,I,cAA1C5S,mBAAA,CA6CM,OA7CN6S,YA6CM,GA5CJxR,mBAAA,CA2CM,OA3CNyR,YA2CM,GA1CJzR,mBAAA,CAWM,OAXN0R,YAWM,GAVJ1R,mBAAA,CAKM,OALN2R,YAKM,GAJO3S,KAAA,CAAAuS,YAAY,CAAC7L,eAAe,I,cAAvC/G,mBAAA,CAAkI;;IAAxFgH,GAAG,EAAE3G,KAAA,CAAAuS,YAAY,CAAC7L,eAAe;IAAGE,GAAG,EAAE5G,KAAA,CAAAuS,YAAY,CAAC1L,SAAS;IAAEtH,KAAK,EAAC;0DACjHI,mBAAA,CAEM,OAFNiT,YAEM,EAAA9P,gBAAA,CADDxC,QAAA,CAAAyG,WAAW,CAAC/G,KAAA,CAAAuS,YAAY,CAAC1L,SAAS,mB,GAGzC7F,mBAAA,CAAqC,YAAA8B,gBAAA,CAA9B9C,KAAA,CAAAuS,YAAY,CAAC1L,SAAS,kBAC7B7F,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASX,QAAA,CAAA8G,mBAAmB,CAACpH,KAAA,CAAAuS,YAAY,CAAClL,MAAM;sBAC9D/G,QAAA,CAAAgH,YAAY,CAACtH,KAAA,CAAAuS,YAAY,CAAClL,MAAM,yB,GAGvCrG,mBAAA,CA6BM,OA7BN6R,YA6BM,GA5BJ7R,mBAAA,CAGM,OAHN8R,YAGM,G,8BAFJ9R,mBAAA,CAAsD;IAAjDzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAA0B,gBAAlB,WAAS,E,sBACvCA,mBAAA,CAAuD,OAAvD+R,YAAuD,EAAAjQ,gBAAA,CAA9B9C,KAAA,CAAAuS,YAAY,CAACrL,QAAQ,iB,GAEhDlG,mBAAA,CAGM,OAHNgS,YAGM,G,8BAFJhS,mBAAA,CAAmD;IAA9CzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAAuB,gBAAf,QAAM,E,sBACpCA,mBAAA,CAAoD,OAApDiS,YAAoD,EAAAnQ,gBAAA,CAA3B9C,KAAA,CAAAuS,YAAY,CAACpL,KAAK,iB,GAE7CnG,mBAAA,CAOM,OAPNkS,YAOM,G,8BANJlS,mBAAA,CAAkD;IAA7CzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAAsB,gBAAd,OAAK,E,sBACnCA,mBAAA,CAIM,OAJNmS,YAIM,GAHJnS,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASjB,KAAA,CAAAuS,YAAY,CAACnN,IAAI;sBACxCpF,KAAA,CAAAuS,YAAY,CAACnN,IAAI,iE,KAI1BpE,mBAAA,CAGM,OAHNoS,YAGM,G,8BAFJpS,mBAAA,CAAmD;IAA9CzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAAuB,gBAAf,QAAM,E,sBACpCA,mBAAA,CAAoE,OAApEqS,YAAoE,EAAAvQ,gBAAA,CAA3C9C,KAAA,CAAAuS,YAAY,CAACxH,YAAY,0B,GAEpD/J,mBAAA,CAGM,OAHNsS,YAGM,G,8BAFJtS,mBAAA,CAAwD;IAAnDzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAA4B,gBAApB,aAAW,E,sBACzCA,mBAAA,CAAqE,OAArEuS,YAAqE,EAAAzQ,gBAAA,CAA5CxC,QAAA,CAAAiH,UAAU,CAACvH,KAAA,CAAAuS,YAAY,CAAC/K,UAAU,kB,GAE7DxG,mBAAA,CAGM,OAHNwS,YAGM,G,8BAFJxS,mBAAA,CAAwD;IAAnDzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAA4B,gBAApB,aAAW,E,sBACzCA,mBAAA,CAAyG,OAAzGyS,YAAyG,EAAA3Q,gBAAA,CAAhF9C,KAAA,CAAAuS,YAAY,CAACmB,UAAU,GAAGpT,QAAA,CAAAiH,UAAU,CAACvH,KAAA,CAAAuS,YAAY,CAACmB,UAAU,4B,8CAK7F1S,mBAAA,CAMM,OANN2S,YAMM,G,8BALJ3S,mBAAA,CAAsF;IAA9EoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,mBAAmB;IAAC,iBAAe,EAAC;KAAQ,OAAK,sBAC7EyB,mBAAA,CAGS;IAHDoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAsD,MAAA,IAAErE,QAAA,CAAAsH,QAAQ,CAAC5H,KAAA,CAAAuS,YAAY;IAAG,iBAAe,EAAC;oCAC5FvR,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,4B,iBAAK,aAElC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}