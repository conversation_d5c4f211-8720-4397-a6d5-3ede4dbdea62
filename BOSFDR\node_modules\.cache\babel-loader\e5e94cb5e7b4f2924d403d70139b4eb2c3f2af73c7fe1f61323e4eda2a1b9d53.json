{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport clientAuthService from '@/services/clientAuthService';\nimport ClientSidebar from '../ClientSidebar.vue';\nimport ClientHeader from '../ClientHeader.vue';\nexport default {\n  name: 'ClientDashboard',\n  components: {\n    ClientSidebar,\n    ClientHeader\n  },\n  data() {\n    return {\n      clientData: null,\n      recentActivity: [],\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      showNotifications: false,\n      activeMenu: 'dashboard',\n      // Dashboard Stats\n      totalRequests: 0,\n      pendingRequests: 0,\n      completedRequests: 0,\n      totalServices: 8,\n      notificationCount: 0,\n      // Performance optimization\n      resizeTimeout: null,\n      isMobile: false\n    };\n  },\n  async mounted() {\n    // Check if user is logged in\n    if (!clientAuthService.isLoggedIn()) {\n      this.$router.push('/client/login');\n      return;\n    }\n\n    // Get client data from localStorage\n    this.clientData = clientAuthService.getClientData();\n\n    // Set initial sidebar state based on screen size\n    this.handleResize();\n\n    // Load dashboard data\n    this.loadDashboardData();\n\n    // Setup event listeners\n    this.setupEventListeners();\n  },\n  beforeUnmount() {\n    // Clean up event listeners\n    this.removeEventListeners();\n  },\n  methods: {\n    // Setup event listeners\n    setupEventListeners() {\n      document.addEventListener('click', this.handleOutsideClick);\n      window.addEventListener('resize', this.throttledResize);\n    },\n    // Remove event listeners\n    removeEventListeners() {\n      document.removeEventListener('click', this.handleOutsideClick);\n      window.removeEventListener('resize', this.throttledResize);\n      if (this.resizeTimeout) {\n        clearTimeout(this.resizeTimeout);\n      }\n    },\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      if (!event.target.closest('.user-dropdown')) {\n        this.showUserDropdown = false;\n      }\n      if (!event.target.closest('.notification-dropdown')) {\n        this.showNotifications = false;\n      }\n    },\n    // Throttled resize handler for better performance\n    throttledResize() {\n      if (this.resizeTimeout) {\n        clearTimeout(this.resizeTimeout);\n      }\n      this.resizeTimeout = setTimeout(() => {\n        this.handleResize();\n      }, 150);\n    },\n    // Handle window resize\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      } else if (window.innerWidth <= 992) {\n        // Auto-collapse on tablet sizes for better space usage\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Toggle sidebar\n    toggleSidebar() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n    // Toggle user dropdown\n    toggleUserDropdown() {\n      this.showUserDropdown = !this.showUserDropdown;\n      this.showNotifications = false;\n    },\n    // Toggle notifications\n    toggleNotifications() {\n      this.showNotifications = !this.showNotifications;\n      this.showUserDropdown = false;\n    },\n    // Set active menu\n    setActiveMenu(menu) {\n      this.activeMenu = menu;\n\n      // Close sidebar on mobile after selection\n      if (window.innerWidth <= 768) {\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Navigate to specific page/section\n    navigateTo(page) {\n      switch (page) {\n        case 'profile':\n          this.setActiveMenu('profile');\n          break;\n        case 'settings':\n          this.setActiveMenu('settings');\n          break;\n        case 'new-request':\n          // Navigate to new document request page\n          this.$router.push({\n            name: 'NewDocumentRequest'\n          });\n          break;\n        case 'track-request':\n          // Navigate to my requests page\n          this.$router.push({\n            name: 'MyRequests'\n          });\n          break;\n        case 'payments':\n          this.setActiveMenu('payments');\n          break;\n        case 'help':\n          this.setActiveMenu('help');\n          break;\n        default:\n          this.setActiveMenu('dashboard');\n      }\n\n      // Close dropdowns\n      this.showUserDropdown = false;\n      this.showNotifications = false;\n    },\n    // Get full name\n    getFullName() {\n      if (this.clientData?.profile) {\n        const {\n          first_name,\n          middle_name,\n          last_name\n        } = this.clientData.profile;\n        let fullName = first_name;\n        if (middle_name) fullName += ` ${middle_name}`;\n        fullName += ` ${last_name}`;\n        return fullName;\n      }\n      return this.clientData?.username || 'User';\n    },\n    // Get status badge class\n    getStatusBadgeClass() {\n      const status = this.clientData?.status;\n      switch (status) {\n        case 'active':\n          return 'status-active';\n        case 'pending_verification':\n          return 'status-pending';\n        case 'suspended':\n          return 'status-suspended';\n        case 'inactive':\n          return 'status-inactive';\n        default:\n          return 'status-unknown';\n      }\n    },\n    // Get status text\n    getStatusText() {\n      const status = this.clientData?.status;\n      switch (status) {\n        case 'active':\n          return 'Active';\n        case 'pending_verification':\n          return 'Pending Verification';\n        case 'suspended':\n          return 'Suspended';\n        case 'inactive':\n          return 'Inactive';\n        default:\n          return 'Unknown';\n      }\n    },\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        dashboard: 'Dashboard',\n        services: 'Document Services',\n        requests: 'My Requests',\n        payments: 'Payments',\n        history: 'History',\n        profile: 'My Profile',\n        help: 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n    // Get page description based on active menu\n    getPageDescription() {\n      const descriptions = {\n        dashboard: 'Overview of your account and recent activities',\n        services: 'Apply for barangay documents and certificates',\n        requests: 'Track and manage your document requests',\n        payments: 'View and manage your payment history',\n        history: 'View your complete transaction history',\n        profile: 'Manage your personal information and settings',\n        help: 'Get assistance and support for your account'\n      };\n      return descriptions[this.activeMenu] || '';\n    },\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    },\n    // Load dashboard data\n    async loadDashboardData() {\n      try {\n        // This would typically fetch from APIs\n        // For now, using placeholder data\n        this.loadStats();\n        this.loadRecentActivity();\n      } catch (error) {\n        console.error('Error loading dashboard data:', error);\n      }\n    },\n    // Load dashboard statistics\n    loadStats() {\n      // Placeholder data - would come from API\n      this.totalRequests = 12;\n      this.pendingRequests = 3;\n      this.completedRequests = 9;\n      this.totalPaid = 450;\n      this.totalServices = 8;\n      this.notificationCount = 2;\n    },\n    // Load recent activity\n    loadRecentActivity() {\n      // Placeholder data - would come from API\n      this.recentActivity = [{\n        id: 1,\n        title: 'Barangay Clearance Approved',\n        description: 'Your barangay clearance request has been approved and is ready for pickup.',\n        time: '2 hours ago',\n        icon: 'fas fa-check-circle text-success'\n      }, {\n        id: 2,\n        title: 'Payment Received',\n        description: 'Payment of ₱50 for Certificate of Residency has been received.',\n        time: '1 day ago',\n        icon: 'fas fa-credit-card text-primary'\n      }, {\n        id: 3,\n        title: 'New Request Submitted',\n        description: 'Your Certificate of Indigency request has been submitted for review.',\n        time: '3 days ago',\n        icon: 'fas fa-file-alt text-info'\n      }];\n    },\n    // Handle menu actions from header\n    handleMenuAction(action) {\n      this.navigateTo(action);\n    },\n    // Handle view all notifications\n    handleViewAllNotifications() {\n      this.setActiveMenu('notifications');\n      this.showNotifications = false;\n    },\n    // Logout\n    logout() {\n      if (confirm('Are you sure you want to logout?')) {\n        clientAuthService.logout();\n        this.$router.push('/client/login');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["clientAuthService", "ClientSidebar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "components", "data", "clientData", "recentActivity", "sidebarCollapsed", "showUserDropdown", "showNotifications", "activeMenu", "totalRequests", "pendingRequests", "completedRequests", "totalServices", "notificationCount", "resizeTimeout", "isMobile", "mounted", "isLoggedIn", "$router", "push", "getClientData", "handleResize", "loadDashboardData", "setupEventListeners", "beforeUnmount", "removeEventListeners", "methods", "document", "addEventListener", "handleOutsideClick", "window", "throttledResize", "removeEventListener", "clearTimeout", "event", "target", "closest", "setTimeout", "innerWidth", "closeMobileSidebar", "toggleSidebar", "toggleUserDropdown", "toggleNotifications", "setActiveMenu", "menu", "navigateTo", "page", "getFullName", "profile", "first_name", "middle_name", "last_name", "fullName", "username", "getStatusBadgeClass", "status", "getStatusText", "getPageTitle", "titles", "dashboard", "services", "requests", "payments", "history", "help", "getPageDescription", "descriptions", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "loadStats", "loadRecentActivity", "error", "console", "totalPaid", "id", "title", "description", "time", "icon", "handleMenuAction", "action", "handleViewAllNotifications", "logout", "confirm"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/components/client/js/clientDashboard.js"], "sourcesContent": ["import clientAuthService from '@/services/clientAuthService';\nimport Client<PERSON>idebar from '../ClientSidebar.vue';\nimport ClientHeader from '../ClientHeader.vue';\n\nexport default {\n  name: 'ClientDashboard',\n  components: {\n    ClientSidebar,\n    ClientHeader\n  },\n  data() {\n    return {\n      clientData: null,\n      recentActivity: [],\n\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      showNotifications: false,\n      activeMenu: 'dashboard',\n\n      // Dashboard Stats\n      totalRequests: 0,\n      pendingRequests: 0,\n      completedRequests: 0,\n      totalServices: 8,\n      notificationCount: 0,\n\n      // Performance optimization\n      resizeTimeout: null,\n      isMobile: false\n    };\n  },\n\n  async mounted() {\n    // Check if user is logged in\n    if (!clientAuthService.isLoggedIn()) {\n      this.$router.push('/client/login');\n      return;\n    }\n\n    // Get client data from localStorage\n    this.clientData = clientAuthService.getClientData();\n\n    // Set initial sidebar state based on screen size\n    this.handleResize();\n\n    // Load dashboard data\n    this.loadDashboardData();\n\n    // Setup event listeners\n    this.setupEventListeners();\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    this.removeEventListeners();\n  },\n\n  methods: {\n    // Setup event listeners\n    setupEventListeners() {\n      document.addEventListener('click', this.handleOutsideClick);\n      window.addEventListener('resize', this.throttledResize);\n    },\n\n    // Remove event listeners\n    removeEventListeners() {\n      document.removeEventListener('click', this.handleOutsideClick);\n      window.removeEventListener('resize', this.throttledResize);\n      if (this.resizeTimeout) {\n        clearTimeout(this.resizeTimeout);\n      }\n    },\n\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      if (!event.target.closest('.user-dropdown')) {\n        this.showUserDropdown = false;\n      }\n      if (!event.target.closest('.notification-dropdown')) {\n        this.showNotifications = false;\n      }\n    },\n\n    // Throttled resize handler for better performance\n    throttledResize() {\n      if (this.resizeTimeout) {\n        clearTimeout(this.resizeTimeout);\n      }\n      this.resizeTimeout = setTimeout(() => {\n        this.handleResize();\n      }, 150);\n    },\n\n    // Handle window resize\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      } else if (window.innerWidth <= 992) {\n        // Auto-collapse on tablet sizes for better space usage\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Toggle sidebar\n    toggleSidebar() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n    },\n\n    // Toggle user dropdown\n    toggleUserDropdown() {\n      this.showUserDropdown = !this.showUserDropdown;\n      this.showNotifications = false;\n    },\n\n    // Toggle notifications\n    toggleNotifications() {\n      this.showNotifications = !this.showNotifications;\n      this.showUserDropdown = false;\n    },\n\n    // Set active menu\n    setActiveMenu(menu) {\n      this.activeMenu = menu;\n\n      // Close sidebar on mobile after selection\n      if (window.innerWidth <= 768) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Navigate to specific page/section\n    navigateTo(page) {\n      switch (page) {\n        case 'profile':\n          this.setActiveMenu('profile');\n          break;\n        case 'settings':\n          this.setActiveMenu('settings');\n          break;\n        case 'new-request':\n          // Navigate to new document request page\n          this.$router.push({ name: 'NewDocumentRequest' });\n          break;\n        case 'track-request':\n          // Navigate to my requests page\n          this.$router.push({ name: 'MyRequests' });\n          break;\n        case 'payments':\n          this.setActiveMenu('payments');\n          break;\n        case 'help':\n          this.setActiveMenu('help');\n          break;\n        default:\n          this.setActiveMenu('dashboard');\n      }\n\n      // Close dropdowns\n      this.showUserDropdown = false;\n      this.showNotifications = false;\n    },\n\n    // Get full name\n    getFullName() {\n      if (this.clientData?.profile) {\n        const { first_name, middle_name, last_name } = this.clientData.profile;\n        let fullName = first_name;\n        if (middle_name) fullName += ` ${middle_name}`;\n        fullName += ` ${last_name}`;\n        return fullName;\n      }\n      return this.clientData?.username || 'User';\n    },\n\n    // Get status badge class\n    getStatusBadgeClass() {\n      const status = this.clientData?.status;\n      switch (status) {\n        case 'active': return 'status-active';\n        case 'pending_verification': return 'status-pending';\n        case 'suspended': return 'status-suspended';\n        case 'inactive': return 'status-inactive';\n        default: return 'status-unknown';\n      }\n    },\n\n    // Get status text\n    getStatusText() {\n      const status = this.clientData?.status;\n      switch (status) {\n        case 'active': return 'Active';\n        case 'pending_verification': return 'Pending Verification';\n        case 'suspended': return 'Suspended';\n        case 'inactive': return 'Inactive';\n        default: return 'Unknown';\n      }\n    },\n\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        dashboard: 'Dashboard',\n        services: 'Document Services',\n        requests: 'My Requests',\n        payments: 'Payments',\n        history: 'History',\n        profile: 'My Profile',\n        help: 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n\n    // Get page description based on active menu\n    getPageDescription() {\n      const descriptions = {\n        dashboard: 'Overview of your account and recent activities',\n        services: 'Apply for barangay documents and certificates',\n        requests: 'Track and manage your document requests',\n        payments: 'View and manage your payment history',\n        history: 'View your complete transaction history',\n        profile: 'Manage your personal information and settings',\n        help: 'Get assistance and support for your account'\n      };\n      return descriptions[this.activeMenu] || '';\n    },\n\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    },\n\n    // Load dashboard data\n    async loadDashboardData() {\n      try {\n        // This would typically fetch from APIs\n        // For now, using placeholder data\n        this.loadStats();\n        this.loadRecentActivity();\n      } catch (error) {\n        console.error('Error loading dashboard data:', error);\n      }\n    },\n\n    // Load dashboard statistics\n    loadStats() {\n      // Placeholder data - would come from API\n      this.totalRequests = 12;\n      this.pendingRequests = 3;\n      this.completedRequests = 9;\n      this.totalPaid = 450;\n      this.totalServices = 8;\n      this.notificationCount = 2;\n    },\n\n    // Load recent activity\n    loadRecentActivity() {\n      // Placeholder data - would come from API\n      this.recentActivity = [\n        {\n          id: 1,\n          title: 'Barangay Clearance Approved',\n          description: 'Your barangay clearance request has been approved and is ready for pickup.',\n          time: '2 hours ago',\n          icon: 'fas fa-check-circle text-success'\n        },\n        {\n          id: 2,\n          title: 'Payment Received',\n          description: 'Payment of ₱50 for Certificate of Residency has been received.',\n          time: '1 day ago',\n          icon: 'fas fa-credit-card text-primary'\n        },\n        {\n          id: 3,\n          title: 'New Request Submitted',\n          description: 'Your Certificate of Indigency request has been submitted for review.',\n          time: '3 days ago',\n          icon: 'fas fa-file-alt text-info'\n        }\n      ];\n    },\n\n    // Handle menu actions from header\n    handleMenuAction(action) {\n      this.navigateTo(action);\n    },\n\n    // Handle view all notifications\n    handleViewAllNotifications() {\n      this.setActiveMenu('notifications');\n      this.showNotifications = false;\n    },\n\n    // Logout\n    logout() {\n      if (confirm('Are you sure you want to logout?')) {\n        clientAuthService.logout();\n        this.$router.push('/client/login');\n      }\n    }\n  }\n};\n"], "mappings": ";AAAA,OAAOA,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,YAAY,MAAM,qBAAqB;AAE9C,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE;IACVH,aAAa;IACbC;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE,EAAE;MAElB;MACAC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,iBAAiB,EAAE,KAAK;MACxBC,UAAU,EAAE,WAAW;MAEvB;MACAC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE,CAAC;MAClBC,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE,CAAC;MAChBC,iBAAiB,EAAE,CAAC;MAEpB;MACAC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAED,MAAMC,OAAOA,CAAA,EAAG;IACd;IACA,IAAI,CAACnB,iBAAiB,CAACoB,UAAU,CAAC,CAAC,EAAE;MACnC,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,eAAe,CAAC;MAClC;IACF;;IAEA;IACA,IAAI,CAAChB,UAAU,GAAGN,iBAAiB,CAACuB,aAAa,CAAC,CAAC;;IAEnD;IACA,IAAI,CAACC,YAAY,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC;;IAExB;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC7B,CAAC;EAEDC,OAAO,EAAE;IACP;IACAH,mBAAmBA,CAAA,EAAG;MACpBI,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,kBAAkB,CAAC;MAC3DC,MAAM,CAACF,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACG,eAAe,CAAC;IACzD,CAAC;IAED;IACAN,oBAAoBA,CAAA,EAAG;MACrBE,QAAQ,CAACK,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACH,kBAAkB,CAAC;MAC9DC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACD,eAAe,CAAC;MAC1D,IAAI,IAAI,CAACjB,aAAa,EAAE;QACtBmB,YAAY,CAAC,IAAI,CAACnB,aAAa,CAAC;MAClC;IACF,CAAC;IAED;IACAe,kBAAkBA,CAACK,KAAK,EAAE;MACxB,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC3C,IAAI,CAAC9B,gBAAgB,GAAG,KAAK;MAC/B;MACA,IAAI,CAAC4B,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;QACnD,IAAI,CAAC7B,iBAAiB,GAAG,KAAK;MAChC;IACF,CAAC;IAED;IACAwB,eAAeA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACjB,aAAa,EAAE;QACtBmB,YAAY,CAAC,IAAI,CAACnB,aAAa,CAAC;MAClC;MACA,IAAI,CAACA,aAAa,GAAGuB,UAAU,CAAC,MAAM;QACpC,IAAI,CAAChB,YAAY,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IAED;IACAA,YAAYA,CAAA,EAAG;MACb,IAAI,CAACN,QAAQ,GAAGe,MAAM,CAACQ,UAAU,IAAI,GAAG;MAExC,IAAI,IAAI,CAACvB,QAAQ,EAAE;QACjB,IAAI,CAACV,gBAAgB,GAAG,IAAI;MAC9B,CAAC,MAAM,IAAIyB,MAAM,CAACQ,UAAU,IAAI,GAAG,EAAE;QACnC;QACA,IAAI,CAACjC,gBAAgB,GAAG,IAAI;MAC9B;IACF,CAAC;IAED;IACAkC,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACxB,QAAQ,EAAE;QACjB,IAAI,CAACV,gBAAgB,GAAG,IAAI;MAC9B;IACF,CAAC;IAED;IACAmC,aAAaA,CAAA,EAAG;MACd,IAAI,CAACnC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED;IACAoC,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACnC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;MAC9C,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAChC,CAAC;IAED;IACAmC,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACnC,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;MAChD,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC/B,CAAC;IAED;IACAqC,aAAaA,CAACC,IAAI,EAAE;MAClB,IAAI,CAACpC,UAAU,GAAGoC,IAAI;;MAEtB;MACA,IAAId,MAAM,CAACQ,UAAU,IAAI,GAAG,EAAE;QAC5B,IAAI,CAACjC,gBAAgB,GAAG,IAAI;MAC9B;IACF,CAAC;IAED;IACAwC,UAAUA,CAACC,IAAI,EAAE;MACf,QAAQA,IAAI;QACV,KAAK,SAAS;UACZ,IAAI,CAACH,aAAa,CAAC,SAAS,CAAC;UAC7B;QACF,KAAK,UAAU;UACb,IAAI,CAACA,aAAa,CAAC,UAAU,CAAC;UAC9B;QACF,KAAK,aAAa;UAChB;UACA,IAAI,CAACzB,OAAO,CAACC,IAAI,CAAC;YAAEnB,IAAI,EAAE;UAAqB,CAAC,CAAC;UACjD;QACF,KAAK,eAAe;UAClB;UACA,IAAI,CAACkB,OAAO,CAACC,IAAI,CAAC;YAAEnB,IAAI,EAAE;UAAa,CAAC,CAAC;UACzC;QACF,KAAK,UAAU;UACb,IAAI,CAAC2C,aAAa,CAAC,UAAU,CAAC;UAC9B;QACF,KAAK,MAAM;UACT,IAAI,CAACA,aAAa,CAAC,MAAM,CAAC;UAC1B;QACF;UACE,IAAI,CAACA,aAAa,CAAC,WAAW,CAAC;MACnC;;MAEA;MACA,IAAI,CAACrC,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAChC,CAAC;IAED;IACAwC,WAAWA,CAAA,EAAG;MACZ,IAAI,IAAI,CAAC5C,UAAU,EAAE6C,OAAO,EAAE;QAC5B,MAAM;UAAEC,UAAU;UAAEC,WAAW;UAAEC;QAAU,CAAC,GAAG,IAAI,CAAChD,UAAU,CAAC6C,OAAO;QACtE,IAAII,QAAQ,GAAGH,UAAU;QACzB,IAAIC,WAAW,EAAEE,QAAQ,IAAI,IAAIF,WAAW,EAAE;QAC9CE,QAAQ,IAAI,IAAID,SAAS,EAAE;QAC3B,OAAOC,QAAQ;MACjB;MACA,OAAO,IAAI,CAACjD,UAAU,EAAEkD,QAAQ,IAAI,MAAM;IAC5C,CAAC;IAED;IACAC,mBAAmBA,CAAA,EAAG;MACpB,MAAMC,MAAM,GAAG,IAAI,CAACpD,UAAU,EAAEoD,MAAM;MACtC,QAAQA,MAAM;QACZ,KAAK,QAAQ;UAAE,OAAO,eAAe;QACrC,KAAK,sBAAsB;UAAE,OAAO,gBAAgB;QACpD,KAAK,WAAW;UAAE,OAAO,kBAAkB;QAC3C,KAAK,UAAU;UAAE,OAAO,iBAAiB;QACzC;UAAS,OAAO,gBAAgB;MAClC;IACF,CAAC;IAED;IACAC,aAAaA,CAAA,EAAG;MACd,MAAMD,MAAM,GAAG,IAAI,CAACpD,UAAU,EAAEoD,MAAM;MACtC,QAAQA,MAAM;QACZ,KAAK,QAAQ;UAAE,OAAO,QAAQ;QAC9B,KAAK,sBAAsB;UAAE,OAAO,sBAAsB;QAC1D,KAAK,WAAW;UAAE,OAAO,WAAW;QACpC,KAAK,UAAU;UAAE,OAAO,UAAU;QAClC;UAAS,OAAO,SAAS;MAC3B;IACF,CAAC;IAED;IACAE,YAAYA,CAAA,EAAG;MACb,MAAMC,MAAM,GAAG;QACbC,SAAS,EAAE,WAAW;QACtBC,QAAQ,EAAE,mBAAmB;QAC7BC,QAAQ,EAAE,aAAa;QACvBC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,SAAS;QAClBf,OAAO,EAAE,YAAY;QACrBgB,IAAI,EAAE;MACR,CAAC;MACD,OAAON,MAAM,CAAC,IAAI,CAAClD,UAAU,CAAC,IAAI,WAAW;IAC/C,CAAC;IAED;IACAyD,kBAAkBA,CAAA,EAAG;MACnB,MAAMC,YAAY,GAAG;QACnBP,SAAS,EAAE,gDAAgD;QAC3DC,QAAQ,EAAE,+CAA+C;QACzDC,QAAQ,EAAE,yCAAyC;QACnDC,QAAQ,EAAE,sCAAsC;QAChDC,OAAO,EAAE,wCAAwC;QACjDf,OAAO,EAAE,+CAA+C;QACxDgB,IAAI,EAAE;MACR,CAAC;MACD,OAAOE,YAAY,CAAC,IAAI,CAAC1D,UAAU,CAAC,IAAI,EAAE;IAC5C,CAAC;IAED;IACA2D,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC;IAED;IACA,MAAMnD,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF;QACA;QACA,IAAI,CAACoD,SAAS,CAAC,CAAC;QAChB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC;IAED;IACAF,SAASA,CAAA,EAAG;MACV;MACA,IAAI,CAACjE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,CAAC;MACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACmE,SAAS,GAAG,GAAG;MACpB,IAAI,CAAClE,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC5B,CAAC;IAED;IACA8D,kBAAkBA,CAAA,EAAG;MACnB;MACA,IAAI,CAACvE,cAAc,GAAG,CACpB;QACE2E,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,6BAA6B;QACpCC,WAAW,EAAE,4EAA4E;QACzFC,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAE;MACR,CAAC,EACD;QACEJ,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE,gEAAgE;QAC7EC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE;MACR,CAAC,EACD;QACEJ,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE,sEAAsE;QACnFC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE;MACR,CAAC,CACF;IACH,CAAC;IAED;IACAC,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAI,CAACxC,UAAU,CAACwC,MAAM,CAAC;IACzB,CAAC;IAED;IACAC,0BAA0BA,CAAA,EAAG;MAC3B,IAAI,CAAC3C,aAAa,CAAC,eAAe,CAAC;MACnC,IAAI,CAACpC,iBAAiB,GAAG,KAAK;IAChC,CAAC;IAED;IACAgF,MAAMA,CAAA,EAAG;MACP,IAAIC,OAAO,CAAC,kCAAkC,CAAC,EAAE;QAC/C3F,iBAAiB,CAAC0F,MAAM,CAAC,CAAC;QAC1B,IAAI,CAACrE,OAAO,CAACC,IAAI,CAAC,eAAe,CAAC;MACpC;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}