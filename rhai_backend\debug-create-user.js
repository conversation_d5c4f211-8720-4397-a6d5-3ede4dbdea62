const UserServiceNew = require('./src/services/userServiceNew');

async function debugCreateUser() {
  console.log('🔍 Debugging Create User...');
  
  try {
    const clientUserData = {
      username: `testclient_${Date.now()}`,
      email: `testclient_${Date.now()}@example.com`,
      password: 'TestPassword123!',
      first_name: 'Test',
      middle_name: 'Middle',
      last_name: 'Client',
      suffix: 'Jr',
      role: 'client',
      phone_number: '09123456789',
      birth_date: '1990-01-01',
      gender: 'male',
      civil_status_id: 1,
      nationality: 'Filipino',
      house_number: '123',
      street: 'Test Street',
      subdivision: 'Test Subdivision',
      barangay: 'Test Barangay',
      city_municipality: 'Test City',
      province: 'Test Province',
      postal_code: '1234'
    };
    
    console.log('Input data:', JSON.stringify(clientUserData, null, 2));
    
    const result = await UserServiceNew.createUser(clientUserData);
    console.log('✅ Success:', result);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
  
  process.exit(0);
}

debugCreateUser();
