<template>
  <div class="admin-users">
    <AdminHeader
      :userName="adminData?.first_name || 'Admin'"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
    />

    <!-- Mobile Overlay -->
    <div
      class="mobile-overlay"
      :class="{ active: !sidebarCollapsed && isMobile }"
      @click="closeMobileSidebar"
    ></div>

    <div class="dashboard-container">
      <AdminSidebar
        :collapsed="sidebarCollapsed"
        :activeMenu="activeMenu"
        @menu-change="handleMenuChange"
        @logout="handleLogout"
        @toggle-sidebar="handleSidebarToggle"
      />

      <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <div class="container-fluid p-4">
          <!-- Page Header -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="d-flex justify-content-between align-items-center flex-wrap">

                <div class="d-flex gap-2">
                  <button class="btn btn-outline-success btn-sm" @click="loadUsers" :disabled="loading">
                    <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': loading }"></i>
                    Refresh
                  </button>
                  <button class="btn btn-success btn-sm" @click="showAddUserModal">
                    <i class="fas fa-user-plus me-1"></i>
                    Add User
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- User Statistics -->
          <div class="row mb-4">
            <div class="col-md-3 mb-3">
              <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                  <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                      <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                        Total Users
                      </div>
                      <div class="h5 mb-0 font-weight-bold text-gray-800">{{ userStats.total || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                  <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                      <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                        Active Users
                      </div>
                      <div class="h5 mb-0 font-weight-bold text-gray-800">{{ userStats.active || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                  <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                      <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                        Pending Verification
                      </div>
                      <div class="h5 mb-0 font-weight-bold text-gray-800">{{ userStats.pending || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                  <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                      <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        Admin Users
                      </div>
                      <div class="h5 mb-0 font-weight-bold text-gray-800">{{ userStats.admins || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Users Table -->
          <div class="row">
            <div class="col-12">
              <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                  <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users me-2"></i>
                    User List
                  </h6>
                  <div class="d-flex gap-2">
                    <select class="form-select form-select-sm" v-model="filterStatus" @change="filterUsers">
                      <option value="">All Status</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="pending">Pending</option>
                      <option value="suspended">Suspended</option>
                    </select>
                    <select class="form-select form-select-sm" v-model="filterType" @change="filterUsers">
                      <option value="">All Types</option>
                      <option value="client">Clients</option>
                      <option value="admin">Admins</option>
                    </select>
                  </div>
                </div>
                <div class="card-body">
                  <!-- Search Bar -->
                  <div class="row mb-3">
                    <div class="col-md-6">
                      <div class="input-group">
                        <span class="input-group-text">
                          <i class="fas fa-search"></i>
                        </span>
                        <input
                          type="text"
                          class="form-control"
                          placeholder="Search users by name, email, or username..."
                          v-model="searchQuery"
                          @input="searchUsers"
                        >
                      </div>
                    </div>
                    <div class="col-md-6 text-end">
                      <span class="text-muted">
                        Showing {{ filteredUsers.length }} of {{ users.length }} users
                      </span>
                    </div>
                  </div>

                  <!-- Loading State -->
                  <div v-if="loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="text-muted mt-2">Loading users...</p>
                  </div>

                  <!-- Empty State -->
                  <div v-else-if="filteredUsers.length === 0" class="text-center py-5">
                    <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No users found</h5>
                    <p class="text-muted">
                      {{ searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.' }}
                    </p>
                  </div>

                  <!-- Users Table -->
                  <div v-else class="table-responsive">
                    <table class="table table-hover">
                      <thead class="table-light">
                        <tr>
                          <th>User</th>
                          <th>Email</th>
                          <th>Type</th>
                          <th>Status</th>
                          <th>Registered</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="user in paginatedUsers" :key="user.id">
                          <td>
                            <div class="d-flex align-items-center">
                              <div class="user-avatar me-3">
                                <img v-if="user.profile_picture" :src="user.profile_picture" :alt="user.full_name" class="rounded-circle">
                                <div v-else class="avatar-placeholder rounded-circle">
                                  {{ getInitials(user.full_name) }}
                                </div>
                              </div>
                              <div>
                                <div class="fw-bold">{{ user.full_name }}</div>
                                <div class="text-muted small">@{{ user.username }}</div>
                              </div>
                            </div>
                          </td>
                          <td>{{ user.email }}</td>
                          <td>
                            <span class="badge" :class="user.type === 'admin' ? 'bg-primary' : 'bg-info'">
                              {{ user.type === 'admin' ? 'Admin' : 'Client' }}
                            </span>
                          </td>
                          <td>
                            <span class="badge" :class="getStatusBadgeClass(user.status)">
                              {{ formatStatus(user.status) }}
                            </span>
                          </td>
                          <td>{{ formatDate(user.created_at) }}</td>
                          <td>
                            <div class="btn-group btn-group-sm">
                              <button class="btn btn-outline-primary" @click="viewUser(user)" title="View Details">
                                <i class="fas fa-eye"></i>
                              </button>
                              <button class="btn btn-outline-warning" @click="editUser(user)" title="Edit User">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button
                                class="btn"
                                :class="user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'"
                                @click="toggleUserStatus(user)"
                                :title="user.status === 'active' ? 'Suspend User' : 'Activate User'"
                              >
                                <i :class="user.status === 'active' ? 'fas fa-pause' : 'fas fa-play'"></i>
                              </button>
                              <button class="btn btn-outline-danger" @click="deleteUser(user)" title="Delete User">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- Pagination -->
                  <div v-if="totalPages > 1" class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                      Page {{ currentPage }} of {{ totalPages }}
                    </div>
                    <nav>
                      <ul class="pagination pagination-sm mb-0">
                        <li class="page-item" :class="{ disabled: currentPage === 1 }">
                          <button class="page-link" @click="changePage(currentPage - 1)" :disabled="currentPage === 1">
                            Previous
                          </button>
                        </li>
                        <li
                          v-for="page in visiblePages"
                          :key="page"
                          class="page-item"
                          :class="{ active: page === currentPage }"
                        >
                          <button class="page-link" @click="changePage(page)">{{ page }}</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                          <button class="page-link" @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">
                            Next
                          </button>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="addUserModalLabel">
              <i class="fas fa-user-plus me-2"></i>
              Add New User
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitAddUser">
              <!-- Basic Information -->
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="addUsername" class="form-label">Username *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="addUsername"
                    v-model="addUserForm.username"
                    :class="{ 'is-invalid': formErrors.username }"
                    required
                  >
                  <div v-if="formErrors.username" class="invalid-feedback">
                    {{ formErrors.username }}
                  </div>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="addPassword" class="form-label">Password *</label>
                  <input
                    type="password"
                    class="form-control"
                    id="addPassword"
                    v-model="addUserForm.password"
                    :class="{ 'is-invalid': formErrors.password }"
                    required
                    minlength="6"
                  >
                  <div v-if="formErrors.password" class="invalid-feedback">
                    {{ formErrors.password }}
                  </div>
                </div>
              </div>

              <!-- Personal Information -->
              <div class="row">
                <div class="col-md-4 mb-3">
                  <label for="addFirstName" class="form-label">First Name *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="addFirstName"
                    v-model="addUserForm.first_name"
                    :class="{ 'is-invalid': formErrors.first_name }"
                    required
                  >
                  <div v-if="formErrors.first_name" class="invalid-feedback">
                    {{ formErrors.first_name }}
                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <label for="addMiddleName" class="form-label">Middle Name</label>
                  <input
                    type="text"
                    class="form-control"
                    id="addMiddleName"
                    v-model="addUserForm.middle_name"
                  >
                </div>
                <div class="col-md-4 mb-3">
                  <label for="addLastName" class="form-label">Last Name *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="addLastName"
                    v-model="addUserForm.last_name"
                    :class="{ 'is-invalid': formErrors.last_name }"
                    required
                  >
                  <div v-if="formErrors.last_name" class="invalid-feedback">
                    {{ formErrors.last_name }}
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="addSuffix" class="form-label">Suffix</label>
                  <input
                    type="text"
                    class="form-control"
                    id="addSuffix"
                    v-model="addUserForm.suffix"
                    placeholder="Jr, Sr, III, etc."
                  >
                </div>
                <div class="col-md-6 mb-3">
                  <label for="addRole" class="form-label">User Type *</label>
                  <select
                    class="form-select"
                    id="addRole"
                    v-model="addUserForm.role"
                    :class="{ 'is-invalid': formErrors.role }"
                    required
                    @change="clearRoleSpecificFields"
                  >
                    <option value="">Select User Type</option>
                    <option value="admin">Administrator</option>
                    <option value="client">Client</option>
                  </select>
                  <div v-if="formErrors.role" class="invalid-feedback">
                    {{ formErrors.role }}
                  </div>
                </div>
              </div>

              <!-- Contact Information -->
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="addEmail" class="form-label">Email</label>
                  <input
                    type="email"
                    class="form-control"
                    id="addEmail"
                    v-model="addUserForm.email"
                    :class="{ 'is-invalid': formErrors.email }"
                  >
                  <div v-if="formErrors.email" class="invalid-feedback">
                    {{ formErrors.email }}
                  </div>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="addPhone" class="form-label">Phone Number *</label>
                  <input
                    type="tel"
                    class="form-control"
                    id="addPhone"
                    v-model="addUserForm.phone_number"
                    :class="{ 'is-invalid': formErrors.phone_number }"
                    required
                  >
                  <div v-if="formErrors.phone_number" class="invalid-feedback">
                    {{ formErrors.phone_number }}
                  </div>
                </div>
              </div>

              <!-- Admin-specific fields -->
              <div v-if="addUserForm.role === 'admin'">
                <hr>
                <h6 class="text-primary mb-3">
                  <i class="fas fa-user-shield me-2"></i>
                  Administrator Information
                </h6>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="addEmployeeId" class="form-label">Employee ID</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addEmployeeId"
                      v-model="addUserForm.employee_id"
                      placeholder="e.g., EMP001"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="addPosition" class="form-label">Position</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addPosition"
                      v-model="addUserForm.position"
                      placeholder="e.g., Barangay Secretary"
                    >
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="addDepartment" class="form-label">Department</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addDepartment"
                      v-model="addUserForm.department"
                      placeholder="e.g., Administration"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="addHireDate" class="form-label">Hire Date</label>
                    <input
                      type="date"
                      class="form-control"
                      id="addHireDate"
                      v-model="addUserForm.hire_date"
                    >
                  </div>
                </div>
              </div>

              <!-- Client-specific fields -->
              <div v-if="addUserForm.role === 'client'">
                <hr>
                <h6 class="text-info mb-3">
                  <i class="fas fa-user me-2"></i>
                  Client Information
                </h6>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="addBirthDate" class="form-label">Birth Date *</label>
                    <input
                      type="date"
                      class="form-control"
                      id="addBirthDate"
                      v-model="addUserForm.birth_date"
                      :class="{ 'is-invalid': formErrors.birth_date }"
                      required
                    >
                    <div v-if="formErrors.birth_date" class="invalid-feedback">
                      {{ formErrors.birth_date }}
                    </div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="addGender" class="form-label">Gender *</label>
                    <select
                      class="form-select"
                      id="addGender"
                      v-model="addUserForm.gender"
                      :class="{ 'is-invalid': formErrors.gender }"
                      required
                    >
                      <option value="">Select Gender</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                    </select>
                    <div v-if="formErrors.gender" class="invalid-feedback">
                      {{ formErrors.gender }}
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="addCivilStatus" class="form-label">Civil Status *</label>
                    <select
                      class="form-select"
                      id="addCivilStatus"
                      v-model="addUserForm.civil_status_id"
                      required
                    >
                      <option value="1">Single</option>
                      <option value="2">Married</option>
                      <option value="3">Widowed</option>
                      <option value="4">Divorced</option>
                      <option value="5">Separated</option>
                    </select>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="addNationality" class="form-label">Nationality</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addNationality"
                      v-model="addUserForm.nationality"
                      placeholder="Filipino"
                    >
                  </div>
                </div>

                <!-- Address Information -->
                <h6 class="text-secondary mb-3 mt-4">
                  <i class="fas fa-map-marker-alt me-2"></i>
                  Address Information
                </h6>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="addHouseNumber" class="form-label">House Number</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addHouseNumber"
                      v-model="addUserForm.house_number"
                      placeholder="e.g., 123"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="addStreet" class="form-label">Street</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addStreet"
                      v-model="addUserForm.street"
                      placeholder="e.g., Main Street"
                    >
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="addSubdivision" class="form-label">Subdivision</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addSubdivision"
                      v-model="addUserForm.subdivision"
                      placeholder="e.g., Greenfield Village"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="addBarangay" class="form-label">Barangay *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addBarangay"
                      v-model="addUserForm.barangay"
                      :class="{ 'is-invalid': formErrors.barangay }"
                      required
                      placeholder="e.g., Barangay Bula"
                    >
                    <div v-if="formErrors.barangay" class="invalid-feedback">
                      {{ formErrors.barangay }}
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-4 mb-3">
                    <label for="addCity" class="form-label">City/Municipality *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addCity"
                      v-model="addUserForm.city_municipality"
                      :class="{ 'is-invalid': formErrors.city_municipality }"
                      required
                      placeholder="e.g., Camarines Sur"
                    >
                    <div v-if="formErrors.city_municipality" class="invalid-feedback">
                      {{ formErrors.city_municipality }}
                    </div>
                  </div>
                  <div class="col-md-4 mb-3">
                    <label for="addProvince" class="form-label">Province *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addProvince"
                      v-model="addUserForm.province"
                      :class="{ 'is-invalid': formErrors.province }"
                      required
                      placeholder="e.g., Camarines Sur"
                    >
                    <div v-if="formErrors.province" class="invalid-feedback">
                      {{ formErrors.province }}
                    </div>
                  </div>
                  <div class="col-md-4 mb-3">
                    <label for="addPostalCode" class="form-label">Postal Code</label>
                    <input
                      type="text"
                      class="form-control"
                      id="addPostalCode"
                      v-model="addUserForm.postal_code"
                      placeholder="e.g., 4422"
                    >
                  </div>
                </div>

                <!-- Residency Information -->
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="addYearsResidency" class="form-label">Years of Residency</label>
                    <input
                      type="number"
                      class="form-control"
                      id="addYearsResidency"
                      v-model="addUserForm.years_of_residency"
                      min="0"
                      placeholder="e.g., 5"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="addMonthsResidency" class="form-label">Additional Months</label>
                    <input
                      type="number"
                      class="form-control"
                      id="addMonthsResidency"
                      v-model="addUserForm.months_of_residency"
                      min="0"
                      max="11"
                      placeholder="e.g., 6"
                    >
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" @click="submitAddUser" :disabled="addUserLoading">
              <span v-if="addUserLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i v-else class="fas fa-plus me-2"></i>
              {{ addUserLoading ? 'Creating...' : 'Create User' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="editUserModalLabel">
              <i class="fas fa-user-edit me-2"></i>
              Edit User
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitEditUser" v-if="editUserForm.id">
              <!-- Basic Information -->
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="editUsername" class="form-label">Username *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="editUsername"
                    v-model="editUserForm.username"
                    :class="{ 'is-invalid': editFormErrors.username }"
                    required
                  >
                  <div v-if="editFormErrors.username" class="invalid-feedback">
                    {{ editFormErrors.username }}
                  </div>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="editStatus" class="form-label">Status *</label>
                  <select
                    class="form-select"
                    id="editStatus"
                    v-model="editUserForm.status"
                    :class="{ 'is-invalid': editFormErrors.status }"
                    required
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="suspended">Suspended</option>
                    <option value="pending_verification">Pending Verification</option>
                  </select>
                  <div v-if="editFormErrors.status" class="invalid-feedback">
                    {{ editFormErrors.status }}
                  </div>
                </div>
              </div>

              <!-- Personal Information -->
              <div class="row">
                <div class="col-md-4 mb-3">
                  <label for="editFirstName" class="form-label">First Name *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="editFirstName"
                    v-model="editUserForm.first_name"
                    :class="{ 'is-invalid': editFormErrors.first_name }"
                    required
                  >
                  <div v-if="editFormErrors.first_name" class="invalid-feedback">
                    {{ editFormErrors.first_name }}
                  </div>
                </div>
                <div class="col-md-4 mb-3">
                  <label for="editMiddleName" class="form-label">Middle Name</label>
                  <input
                    type="text"
                    class="form-control"
                    id="editMiddleName"
                    v-model="editUserForm.middle_name"
                  >
                </div>
                <div class="col-md-4 mb-3">
                  <label for="editLastName" class="form-label">Last Name *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="editLastName"
                    v-model="editUserForm.last_name"
                    :class="{ 'is-invalid': editFormErrors.last_name }"
                    required
                  >
                  <div v-if="editFormErrors.last_name" class="invalid-feedback">
                    {{ editFormErrors.last_name }}
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="editSuffix" class="form-label">Suffix</label>
                  <input
                    type="text"
                    class="form-control"
                    id="editSuffix"
                    v-model="editUserForm.suffix"
                    placeholder="Jr, Sr, III, etc."
                  >
                </div>
                <div class="col-md-6 mb-3">
                  <label for="editRole" class="form-label">User Type *</label>
                  <select
                    class="form-select"
                    id="editRole"
                    v-model="editUserForm.role"
                    :class="{ 'is-invalid': editFormErrors.role }"
                    required
                    disabled
                  >
                    <option value="admin">Administrator</option>
                    <option value="client">Client</option>
                  </select>
                  <small class="text-muted">User type cannot be changed after creation</small>
                  <div v-if="editFormErrors.role" class="invalid-feedback">
                    {{ editFormErrors.role }}
                  </div>
                </div>
              </div>

              <!-- Contact Information -->
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="editEmail" class="form-label">Email</label>
                  <input
                    type="email"
                    class="form-control"
                    id="editEmail"
                    v-model="editUserForm.email"
                    :class="{ 'is-invalid': editFormErrors.email }"
                  >
                  <div v-if="editFormErrors.email" class="invalid-feedback">
                    {{ editFormErrors.email }}
                  </div>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="editPhone" class="form-label">Phone Number *</label>
                  <input
                    type="tel"
                    class="form-control"
                    id="editPhone"
                    v-model="editUserForm.phone_number"
                    :class="{ 'is-invalid': editFormErrors.phone_number }"
                    required
                  >
                  <div v-if="editFormErrors.phone_number" class="invalid-feedback">
                    {{ editFormErrors.phone_number }}
                  </div>
                </div>
              </div>

              <!-- Admin-specific fields -->
              <div v-if="editUserForm.role === 'admin'">
                <hr>
                <h6 class="text-primary mb-3">
                  <i class="fas fa-user-shield me-2"></i>
                  Administrator Information
                </h6>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="editEmployeeId" class="form-label">Employee ID</label>
                    <input
                      type="text"
                      class="form-control"
                      id="editEmployeeId"
                      v-model="editUserForm.employee_id"
                      placeholder="e.g., EMP001"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="editPosition" class="form-label">Position</label>
                    <input
                      type="text"
                      class="form-control"
                      id="editPosition"
                      v-model="editUserForm.position"
                      placeholder="e.g., Barangay Secretary"
                    >
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="editDepartment" class="form-label">Department</label>
                    <input
                      type="text"
                      class="form-control"
                      id="editDepartment"
                      v-model="editUserForm.department"
                      placeholder="e.g., Administration"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="editHireDate" class="form-label">Hire Date</label>
                    <input
                      type="date"
                      class="form-control"
                      id="editHireDate"
                      v-model="editUserForm.hire_date"
                    >
                  </div>
                </div>
              </div>

              <!-- Client-specific fields (read-only for editing) -->
              <div v-if="editUserForm.role === 'client'">
                <hr>
                <h6 class="text-info mb-3">
                  <i class="fas fa-user me-2"></i>
                  Client Information
                </h6>
                <div class="alert alert-info">
                  <i class="fas fa-info-circle me-2"></i>
                  Client-specific information can only be updated by the client through their profile.
                </div>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Birth Date</label>
                    <input
                      type="date"
                      class="form-control"
                      v-model="editUserForm.birth_date"
                      readonly
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Gender</label>
                    <input
                      type="text"
                      class="form-control"
                      :value="editUserForm.gender ? editUserForm.gender.charAt(0).toUpperCase() + editUserForm.gender.slice(1) : ''"
                      readonly
                    >
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-12 mb-3">
                    <label class="form-label">Address</label>
                    <input
                      type="text"
                      class="form-control"
                      :value="getFullAddress(editUserForm)"
                      readonly
                    >
                  </div>
                </div>
              </div>

              <!-- Password Reset Section -->
              <hr>
              <div class="row">
                <div class="col-12 mb-3">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="resetPassword"
                      v-model="editUserForm.resetPassword"
                    >
                    <label class="form-check-label" for="resetPassword">
                      Reset user password
                    </label>
                  </div>
                </div>
              </div>

              <div v-if="editUserForm.resetPassword" class="row">
                <div class="col-md-6 mb-3">
                  <label for="editNewPassword" class="form-label">New Password *</label>
                  <input
                    type="password"
                    class="form-control"
                    id="editNewPassword"
                    v-model="editUserForm.newPassword"
                    :class="{ 'is-invalid': editFormErrors.newPassword }"
                    minlength="6"
                  >
                  <div v-if="editFormErrors.newPassword" class="invalid-feedback">
                    {{ editFormErrors.newPassword }}
                  </div>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="editConfirmPassword" class="form-label">Confirm Password *</label>
                  <input
                    type="password"
                    class="form-control"
                    id="editConfirmPassword"
                    v-model="editUserForm.confirmPassword"
                    :class="{ 'is-invalid': editFormErrors.confirmPassword }"
                    minlength="6"
                  >
                  <div v-if="editFormErrors.confirmPassword" class="invalid-feedback">
                    {{ editFormErrors.confirmPassword }}
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" @click="submitEditUser" :disabled="editUserLoading">
              <span v-if="editUserLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i v-else class="fas fa-save me-2"></i>
              {{ editUserLoading ? 'Updating...' : 'Update User' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- View User Modal -->
    <div class="modal fade" id="viewUserModal" tabindex="-1" aria-labelledby="viewUserModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="viewUserModalLabel">
              <i class="fas fa-user me-2"></i>
              User Details
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" v-if="viewUserData">
            <div class="row">
              <div class="col-md-4 text-center mb-4">
                <div class="user-avatar-large mx-auto mb-3">
                  <img v-if="viewUserData.profile_picture" :src="viewUserData.profile_picture" :alt="viewUserData.full_name" class="rounded-circle">
                  <div v-else class="avatar-placeholder-large rounded-circle">
                    {{ getInitials(viewUserData.full_name) }}
                  </div>
                </div>
                <h5>{{ viewUserData.full_name }}</h5>
                <span class="badge" :class="getStatusBadgeClass(viewUserData.status)">
                  {{ formatStatus(viewUserData.status) }}
                </span>
                <div class="mt-2">
                  <span class="badge" :class="viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'">
                    {{ viewUserData.type === 'admin' ? 'Administrator' : 'Client' }}
                  </span>
                </div>
              </div>
              <div class="col-md-8">
                <!-- Basic Information -->
                <h6 class="text-primary mb-3">
                  <i class="fas fa-user me-2"></i>
                  Basic Information
                </h6>
                <div class="row mb-2">
                  <div class="col-sm-4"><strong>Username:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.username }}</div>
                </div>
                <div class="row mb-2">
                  <div class="col-sm-4"><strong>Email:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.email || 'Not provided' }}</div>
                </div>
                <div class="row mb-2">
                  <div class="col-sm-4"><strong>Phone:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.phone_number || 'Not provided' }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Status:</strong></div>
                  <div class="col-sm-8">
                    <span class="badge" :class="getStatusBadgeClass(viewUserData.status)">
                      {{ formatStatus(viewUserData.status) }}
                    </span>
                  </div>
                </div>

                <!-- Admin-specific Information -->
                <div v-if="viewUserData.type === 'admin'">
                  <h6 class="text-primary mb-3">
                    <i class="fas fa-user-shield me-2"></i>
                    Administrator Details
                  </h6>
                  <div class="row mb-2">
                    <div class="col-sm-4"><strong>Employee ID:</strong></div>
                    <div class="col-sm-8">{{ viewUserData.employee_id || 'Not assigned' }}</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-sm-4"><strong>Position:</strong></div>
                    <div class="col-sm-8">{{ viewUserData.position || 'Not specified' }}</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-sm-4"><strong>Department:</strong></div>
                    <div class="col-sm-8">{{ viewUserData.department || 'Not specified' }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4"><strong>Hire Date:</strong></div>
                    <div class="col-sm-8">{{ viewUserData.hire_date ? formatDate(viewUserData.hire_date) : 'Not specified' }}</div>
                  </div>
                </div>

                <!-- Client-specific Information -->
                <div v-if="viewUserData.type === 'client'">
                  <h6 class="text-info mb-3">
                    <i class="fas fa-user me-2"></i>
                    Client Details
                  </h6>
                  <div class="row mb-2">
                    <div class="col-sm-4"><strong>Birth Date:</strong></div>
                    <div class="col-sm-8">{{ viewUserData.birth_date ? formatDate(viewUserData.birth_date) : 'Not provided' }}</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-sm-4"><strong>Gender:</strong></div>
                    <div class="col-sm-8">{{ viewUserData.gender ? viewUserData.gender.charAt(0).toUpperCase() + viewUserData.gender.slice(1) : 'Not specified' }}</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-sm-4"><strong>Nationality:</strong></div>
                    <div class="col-sm-8">{{ viewUserData.nationality || 'Not specified' }}</div>
                  </div>
                  <div class="row mb-3">
                    <div class="col-sm-4"><strong>Address:</strong></div>
                    <div class="col-sm-8">{{ getFullAddress(viewUserData) }}</div>
                  </div>
                  <div v-if="viewUserData.years_of_residency || viewUserData.months_of_residency" class="row mb-3">
                    <div class="col-sm-4"><strong>Residency:</strong></div>
                    <div class="col-sm-8">
                      {{ viewUserData.years_of_residency || 0 }} years, {{ viewUserData.months_of_residency || 0 }} months
                    </div>
                  </div>
                </div>

                <!-- Account Information -->
                <h6 class="text-secondary mb-3">
                  <i class="fas fa-clock me-2"></i>
                  Account Information
                </h6>
                <div class="row mb-2">
                  <div class="col-sm-4"><strong>Registered:</strong></div>
                  <div class="col-sm-8">{{ formatDate(viewUserData.created_at) }}</div>
                </div>
                <div class="row mb-2">
                  <div class="col-sm-4"><strong>Last Updated:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.updated_at ? formatDate(viewUserData.updated_at) : 'Never' }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Last Login:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.last_login ? formatDate(viewUserData.last_login) : 'Never' }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" @click="editUser(viewUserData)" data-bs-dismiss="modal">
              <i class="fas fa-edit me-2"></i>
              Edit User
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AdminHeader from './AdminHeader.vue';
import AdminSidebar from './AdminSidebar.vue';
import adminAuthService from '@/services/adminAuthService';
import userManagementService from '@/services/userManagementService';
import { Modal } from 'bootstrap';

export default {
  name: 'AdminUsers',
  components: {
    AdminHeader,
    AdminSidebar
  },

  data() {
    return {
      // UI State
      sidebarCollapsed: false,
      showUserDropdown: false,
      isMobile: false,
      adminData: null,
      // Component Data
      users: [],
      filteredUsers: [],
      searchQuery: '',
      filterStatus: '',
      filterType: '',
      currentPage: 1,
      itemsPerPage: 10,
      loading: false,
      userStats: {
        total: 0,
        active: 0,
        pending: 0,
        admins: 0
      },

      // Modal data
      viewUserData: null,
      addUserLoading: false,
      editUserLoading: false,

      // Form validation errors
      formErrors: {},
      editFormErrors: {},

      // Add user form
      addUserForm: {
        username: '',
        email: '',
        password: '',
        first_name: '',
        middle_name: '',
        last_name: '',
        suffix: '',
        role: '',
        phone_number: '',
        // Admin specific fields
        position: '',
        department: '',
        employee_id: '',
        hire_date: '',
        // Client specific fields
        birth_date: '',
        gender: '',
        civil_status_id: 1,
        nationality: 'Filipino',
        house_number: '',
        street: '',
        subdivision: '',
        barangay: '',
        city_municipality: '',
        province: '',
        postal_code: '',
        years_of_residency: null,
        months_of_residency: null
      },

      // Edit user form
      editUserForm: {
        id: null,
        username: '',
        email: '',
        first_name: '',
        middle_name: '',
        last_name: '',
        suffix: '',
        role: '',
        status: '',
        phone_number: '',
        // Admin specific fields
        position: '',
        department: '',
        employee_id: '',
        hire_date: '',
        // Client specific fields
        birth_date: '',
        gender: '',
        civil_status_id: 1,
        nationality: 'Filipino',
        house_number: '',
        street: '',
        subdivision: '',
        barangay: '',
        city_municipality: '',
        province: '',
        postal_code: '',
        years_of_residency: null,
        months_of_residency: null,
        // Password reset fields
        resetPassword: false,
        newPassword: '',
        confirmPassword: ''
      },

      // Available options
      genderOptions: [
        { value: 'male', label: 'Male' },
        { value: 'female', label: 'Female' }
      ],

      statusOptions: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'suspended', label: 'Suspended' },
        { value: 'pending_verification', label: 'Pending Verification' }
      ]
    };
  },

  computed: {
    activeMenu() {
      const path = this.$route.path;
      if (path.includes('/admin/users')) return 'users';
      if (path.includes('/admin/requests')) return 'requests';
      if (path.includes('/admin/reports')) return 'reports';
      if (path.includes('/admin/settings')) return 'settings';
      if (path.includes('/admin/activity-logs')) return 'activity';
      if (path.includes('/admin/profile')) return 'profile';
      return 'dashboard';
    },

    paginatedUsers() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredUsers.slice(start, end);
    },

    totalPages() {
      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);
    },

    visiblePages() {
      const pages = [];
      const start = Math.max(1, this.currentPage - 2);
      const end = Math.min(this.totalPages, this.currentPage + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },

  async mounted() {
    // Check authentication
    if (!adminAuthService.isLoggedIn()) {
      this.$router.push('/admin/login');
      return;
    }

    // Initialize UI state
    this.initializeUI();

    // Make bootstrap available globally for this component
    this.$bootstrap = { Modal };

    // Load component data
    await this.loadAdminProfile();
    await this.loadUserStats();
    await this.loadUsers();
  },

  beforeUnmount() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },

  methods: {
    // Initialize UI state
    initializeUI() {
      this.isMobile = window.innerWidth <= 768;

      // Load saved sidebar state (only on desktop)
      if (!this.isMobile) {
        const saved = localStorage.getItem('adminSidebarCollapsed');
        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
      } else {
        this.sidebarCollapsed = true; // Always collapsed on mobile
      }

      // Setup resize listener
      this.handleResize = () => {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        if (this.isMobile && !wasMobile) {
          this.sidebarCollapsed = true; // Collapse when switching to mobile
        } else if (!this.isMobile && wasMobile) {
          // Restore saved state when switching to desktop
          const saved = localStorage.getItem('adminSidebarCollapsed');
          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
        }
      };
      window.addEventListener('resize', this.handleResize);
    },

    // Sidebar toggle
    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));
    },

    // Menu navigation
    handleMenuChange(menu) {
      const routes = {
        'dashboard': '/admin/dashboard',
        'users': '/admin/users',
        'requests': '/admin/requests',
        'reports': '/admin/reports',
        'settings': '/admin/settings',
        'activity': '/admin/activity-logs',
        'profile': '/admin/profile'
      };

      // Close sidebar on mobile after navigation
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }

      if (routes[menu]) {
        this.$router.push(routes[menu]);
      }
    },

    // User dropdown toggle
    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    // Menu actions
    handleMenuAction(action) {
      if (action === 'profile') {
        this.$router.push('/admin/profile');
      } else if (action === 'settings') {
        this.$router.push('/admin/settings');
      }
      this.showUserDropdown = false;
    },

    // Close mobile sidebar
    closeMobileSidebar() {
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }
    },

    // Logout
    handleLogout() {
      adminAuthService.logout();
      this.$router.push('/admin/login');
    },

    // Load admin profile data
    async loadAdminProfile() {
      try {
        const response = await adminAuthService.getProfile();
        if (response.success) {
          this.adminData = response.data;
        }
      } catch (error) {
        console.error('Failed to load admin data:', error);
        this.adminData = adminAuthService.getAdminData();
      }
    },

    // Load user statistics
    async loadUserStats() {
      try {
        const response = await userManagementService.getUserStats();

        if (response.success) {
          this.userStats = response.data;
        } else {
          throw new Error(response.message || 'Failed to load user statistics');
        }
      } catch (error) {
        console.error('Failed to load user statistics:', error);
        // Fallback stats based on current users
        this.calculateStats();
      }
    },

    // Load users data
    async loadUsers() {
      this.loading = true;
      try {
        const params = {
          page: this.currentPage,
          limit: 50, // Load more for client-side filtering
          search: this.searchQuery || undefined,
          role: this.filterType || undefined,
          is_active: this.filterStatus === 'active' ? true :
                     this.filterStatus === 'inactive' ? false : undefined
        };

        const response = await userManagementService.getUsers(params);

        if (response.success) {
          // Format users for display
          this.users = response.data.users.map(user =>
            userManagementService.formatUserData(user)
          );

          this.filteredUsers = [...this.users];
          this.calculateStats();
        } else {
          throw new Error(response.message || 'Failed to load users');
        }
      } catch (error) {
        console.error('Failed to load users:', error);
        this.$toast?.error?.(error.message || 'Failed to load users');

        // Fallback to mock data for development
        this.users = [
          {
            id: 1,
            username: 'admin12345',
            full_name: 'System Administrator',
            email: '<EMAIL>',
            type: 'admin',
            status: 'active',
            created_at: new Date().toISOString(),
            last_login: new Date().toISOString()
          },
          {
            id: 2,
            username: 'testapi',
            full_name: 'Test User',
            email: '<EMAIL>',
            type: 'client',
            status: 'active',
            created_at: new Date().toISOString(),
            last_login: null
          }
        ];

        this.filteredUsers = [...this.users];
        this.calculateStats();
      } finally {
        this.loading = false;
      }
    },

    // Calculate user statistics
    calculateStats() {
      this.userStats = {
        total: this.users.length,
        active: this.users.filter(u => u.status === 'active').length,
        pending: this.users.filter(u => u.status === 'pending').length,
        admins: this.users.filter(u => u.type === 'admin').length
      };
    },

    // Search users
    searchUsers() {
      this.filterUsers();
    },

    // Filter users based on search and filters
    filterUsers() {
      let filtered = [...this.users];

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(user =>
          user.full_name.toLowerCase().includes(query) ||
          user.email.toLowerCase().includes(query) ||
          user.username.toLowerCase().includes(query)
        );
      }

      // Apply status filter
      if (this.filterStatus) {
        filtered = filtered.filter(user => user.status === this.filterStatus);
      }

      // Apply type filter
      if (this.filterType) {
        filtered = filtered.filter(user => user.type === this.filterType);
      }

      this.filteredUsers = filtered;
      this.currentPage = 1; // Reset to first page
    },

    // Change page
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
      }
    },

    // Get user initials for avatar
    getInitials(fullName) {
      if (!fullName) return '?';
      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);
    },

    // Get status badge class
    getStatusBadgeClass(status) {
      const classes = {
        'active': 'bg-success',
        'inactive': 'bg-secondary',
        'pending': 'bg-warning',
        'suspended': 'bg-danger'
      };
      return classes[status] || 'bg-secondary';
    },

    // Format status text
    formatStatus(status) {
      return status.charAt(0).toUpperCase() + status.slice(1);
    },

    // Format date
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },

    // User actions


    async toggleUserStatus(user) {
      try {
        const newStatus = user.status === 'active' ? 'suspended' : 'active';
        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;

        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);

        if (response.success) {
          // Update local data
          user.status = newStatus;
          this.calculateStats();

          const statusText = newStatus === 'active' ? 'activated' : 'suspended';
          this.showToast('success', `User ${user.full_name} has been ${statusText}.`);
        } else {
          throw new Error(response.message || 'Failed to update user status');
        }
      } catch (error) {
        console.error('Failed to update user status:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to update user status. Please try again.';
        this.showToast('error', errorMessage);
      }
    },

    async deleteUser(user) {
      const confirmMessage = `Are you sure you want to delete user "${user.full_name}"?\n\nThis will:\n- Deactivate the user account\n- Prevent future logins\n- Preserve data for audit purposes\n\nThis action can be reversed by reactivating the user.`;

      if (!confirm(confirmMessage)) {
        return;
      }

      try {
        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;
        const response = await userManagementService.deleteUser(user.id, reason);

        if (response.success) {
          this.showToast('success', `User ${user.full_name} has been deleted successfully.`);

          // Reload data to reflect changes
          await this.loadUsers();
          await this.loadUserStats();
        } else {
          throw new Error(response.message || 'Failed to delete user');
        }
      } catch (error) {
        console.error('Failed to delete user:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to delete user. Please try again.';
        this.showToast('error', errorMessage);
      }
    },

    // Toast notification helper
    showToast(type, message) {
      if (this.$toast && this.$toast[type]) {
        this.$toast[type](message);
      } else {
        // Fallback to console if toast is not available
        console.log(`${type.toUpperCase()}: ${message}`);

        // You can also use browser alert as fallback
        if (type === 'error') {
          alert(`Error: ${message}`);
        } else if (type === 'success') {
          alert(`Success: ${message}`);
        }
      }
    },



    // Modal methods
    showAddUserModal() {
      this.resetAddUserForm();
      try {
        const modalElement = document.getElementById('addUserModal');
        if (modalElement) {
          const modal = new Modal(modalElement);
          modal.show();
        }
      } catch (error) {
        console.error('Error showing add user modal:', error);
        this.$toast?.error?.('Failed to open add user modal');
      }
    },

    resetAddUserForm() {
      this.addUserForm = {
        username: '',
        email: '',
        password: '',
        first_name: '',
        middle_name: '',
        last_name: '',
        suffix: '',
        role: '',
        phone_number: '',
        // Admin specific fields
        position: '',
        department: '',
        employee_id: '',
        hire_date: '',
        // Client specific fields
        birth_date: '',
        gender: '',
        civil_status_id: 1,
        nationality: 'Filipino',
        house_number: '',
        street: '',
        subdivision: '',
        barangay: '',
        city_municipality: '',
        province: '',
        postal_code: '',
        years_of_residency: null,
        months_of_residency: null
      };
      this.formErrors = {};
    },

    clearRoleSpecificFields() {
      if (this.addUserForm.role === 'admin') {
        // Clear client-specific fields
        this.addUserForm.birth_date = '';
        this.addUserForm.gender = '';
        this.addUserForm.civil_status_id = 1;
        this.addUserForm.nationality = 'Filipino';
        this.addUserForm.house_number = '';
        this.addUserForm.street = '';
        this.addUserForm.subdivision = '';
        this.addUserForm.barangay = '';
        this.addUserForm.city_municipality = '';
        this.addUserForm.province = '';
        this.addUserForm.postal_code = '';
        this.addUserForm.years_of_residency = null;
        this.addUserForm.months_of_residency = null;
      } else if (this.addUserForm.role === 'client') {
        // Clear admin-specific fields
        this.addUserForm.position = '';
        this.addUserForm.department = '';
        this.addUserForm.employee_id = '';
        this.addUserForm.hire_date = '';
      }
    },

    validateAddUserForm() {
      const errors = {};

      // Basic validation
      if (!this.addUserForm.username || this.addUserForm.username.length < 3) {
        errors.username = 'Username must be at least 3 characters long';
      }

      if (!this.addUserForm.password || this.addUserForm.password.length < 6) {
        errors.password = 'Password must be at least 6 characters long';
      }

      if (!this.addUserForm.first_name || this.addUserForm.first_name.trim().length < 2) {
        errors.first_name = 'First name must be at least 2 characters long';
      }

      if (!this.addUserForm.last_name || this.addUserForm.last_name.trim().length < 2) {
        errors.last_name = 'Last name must be at least 2 characters long';
      }

      if (!this.addUserForm.role) {
        errors.role = 'Please select a user type';
      }

      if (!this.addUserForm.phone_number || this.addUserForm.phone_number.trim().length < 10) {
        errors.phone_number = 'Please provide a valid phone number';
      }

      if (this.addUserForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.addUserForm.email)) {
        errors.email = 'Please provide a valid email address';
      }

      // Role-specific validation
      if (this.addUserForm.role === 'client') {
        if (!this.addUserForm.birth_date) {
          errors.birth_date = 'Birth date is required for clients';
        }

        if (!this.addUserForm.gender) {
          errors.gender = 'Gender is required for clients';
        }

        if (!this.addUserForm.barangay || this.addUserForm.barangay.trim().length < 2) {
          errors.barangay = 'Barangay is required for clients';
        }

        if (!this.addUserForm.city_municipality || this.addUserForm.city_municipality.trim().length < 2) {
          errors.city_municipality = 'City/Municipality is required for clients';
        }

        if (!this.addUserForm.province || this.addUserForm.province.trim().length < 2) {
          errors.province = 'Province is required for clients';
        }
      }

      this.formErrors = errors;
      return Object.keys(errors).length === 0;
    },

    validateEditUserForm() {
      const errors = {};

      // Basic validation
      if (!this.editUserForm.username || this.editUserForm.username.length < 3) {
        errors.username = 'Username must be at least 3 characters long';
      }

      if (!this.editUserForm.first_name || this.editUserForm.first_name.trim().length < 2) {
        errors.first_name = 'First name must be at least 2 characters long';
      }

      if (!this.editUserForm.last_name || this.editUserForm.last_name.trim().length < 2) {
        errors.last_name = 'Last name must be at least 2 characters long';
      }

      if (!this.editUserForm.phone_number || this.editUserForm.phone_number.trim().length < 10) {
        errors.phone_number = 'Please provide a valid phone number';
      }

      if (this.editUserForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.editUserForm.email)) {
        errors.email = 'Please provide a valid email address';
      }

      // Password reset validation
      if (this.editUserForm.resetPassword) {
        if (!this.editUserForm.newPassword || this.editUserForm.newPassword.length < 6) {
          errors.newPassword = 'New password must be at least 6 characters long';
        }

        if (this.editUserForm.newPassword !== this.editUserForm.confirmPassword) {
          errors.confirmPassword = 'Passwords do not match';
        }
      }

      this.editFormErrors = errors;
      return Object.keys(errors).length === 0;
    },

    getFullAddress(user) {
      const parts = [];
      if (user.house_number) parts.push(user.house_number);
      if (user.street) parts.push(user.street);
      if (user.subdivision) parts.push(user.subdivision);
      if (user.barangay) parts.push(user.barangay);
      if (user.city_municipality) parts.push(user.city_municipality);
      if (user.province) parts.push(user.province);
      return parts.join(', ') || 'No address provided';
    },

    async submitAddUser() {
      try {
        this.addUserLoading = true;

        // Validate form
        if (!this.validateAddUserForm()) {
          this.showToast('error', 'Please fix the validation errors before submitting.');
          return;
        }

        // Prepare user data
        const userData = { ...this.addUserForm };

        // Convert numeric fields
        if (userData.years_of_residency) {
          userData.years_of_residency = parseInt(userData.years_of_residency);
        }
        if (userData.months_of_residency) {
          userData.months_of_residency = parseInt(userData.months_of_residency);
        }
        if (userData.civil_status_id) {
          userData.civil_status_id = parseInt(userData.civil_status_id);
        }

        console.log('Creating user with data:', userData);

        const response = await userManagementService.createUser(userData);

        if (response.success) {
          this.showToast('success', 'User created successfully');

          // Close modal
          try {
            const modal = Modal.getInstance(document.getElementById('addUserModal'));
            if (modal) modal.hide();
          } catch (error) {
            console.error('Error closing modal:', error);
          }

          // Reset form and reload data
          this.resetAddUserForm();
          await this.loadUsers();
          await this.loadUserStats();
        } else {
          throw new Error(response.message || 'Failed to create user');
        }
      } catch (error) {
        console.error('Failed to create user:', error);

        // Handle validation errors from server
        if (error.response?.data?.details) {
          const serverErrors = {};
          error.response.data.details.forEach(detail => {
            if (detail.path) {
              serverErrors[detail.path] = detail.msg;
            }
          });
          this.formErrors = { ...this.formErrors, ...serverErrors };
        }

        const errorMessage = error.response?.data?.message || error.message || 'Failed to create user';
        this.showToast('error', errorMessage);
      } finally {
        this.addUserLoading = false;
      }
    },

    editUser(user) {
      this.editUserForm = {
        id: user.id,
        username: user.username,
        email: user.email || '',
        first_name: user.first_name || '',
        middle_name: user.middle_name || '',
        last_name: user.last_name || '',
        suffix: user.suffix || '',
        role: user.type,
        status: user.status,
        phone_number: user.phone_number || '',
        // Admin specific fields
        position: user.position || '',
        department: user.department || '',
        employee_id: user.employee_id || '',
        hire_date: user.hire_date ? user.hire_date.split('T')[0] : '',
        // Client specific fields
        birth_date: user.birth_date ? user.birth_date.split('T')[0] : '',
        gender: user.gender || '',
        civil_status_id: user.civil_status_id || 1,
        nationality: user.nationality || 'Filipino',
        house_number: user.house_number || '',
        street: user.street || '',
        subdivision: user.subdivision || '',
        barangay: user.barangay || '',
        city_municipality: user.city_municipality || '',
        province: user.province || '',
        postal_code: user.postal_code || '',
        years_of_residency: user.years_of_residency || null,
        months_of_residency: user.months_of_residency || null,
        // Password reset fields
        resetPassword: false,
        newPassword: '',
        confirmPassword: ''
      };

      this.editFormErrors = {};

      try {
        const modalElement = document.getElementById('editUserModal');
        if (modalElement) {
          const modal = new Modal(modalElement);
          modal.show();
        }
      } catch (error) {
        console.error('Error showing edit user modal:', error);
        this.showToast('error', 'Failed to open edit user modal');
      }
    },

    async submitEditUser() {
      try {
        this.editUserLoading = true;

        // Validate form
        if (!this.validateEditUserForm()) {
          this.showToast('error', 'Please fix the validation errors before submitting.');
          return;
        }

        // Prepare update data
        const updateData = {
          username: this.editUserForm.username,
          email: this.editUserForm.email,
          first_name: this.editUserForm.first_name,
          middle_name: this.editUserForm.middle_name,
          last_name: this.editUserForm.last_name,
          suffix: this.editUserForm.suffix,
          status: this.editUserForm.status,
          phone_number: this.editUserForm.phone_number
        };

        // Add role-specific fields
        if (this.editUserForm.role === 'admin') {
          updateData.position = this.editUserForm.position;
          updateData.department = this.editUserForm.department;
          updateData.employee_id = this.editUserForm.employee_id;
          updateData.hire_date = this.editUserForm.hire_date;
        }

        // Add password reset if requested
        if (this.editUserForm.resetPassword && this.editUserForm.newPassword) {
          updateData.password = this.editUserForm.newPassword;
        }

        console.log('Updating user with data:', updateData);

        const response = await userManagementService.updateUser(this.editUserForm.id, updateData);

        if (response.success) {
          this.showToast('success', 'User updated successfully');

          // Close modal
          try {
            const modal = Modal.getInstance(document.getElementById('editUserModal'));
            if (modal) modal.hide();
          } catch (error) {
            console.error('Error closing modal:', error);
          }

          // Reload data to get fresh information
          await this.loadUsers();
          await this.loadUserStats();
        } else {
          throw new Error(response.message || 'Failed to update user');
        }
      } catch (error) {
        console.error('Failed to update user:', error);

        // Handle validation errors from server
        if (error.response?.data?.details) {
          const serverErrors = {};
          error.response.data.details.forEach(detail => {
            if (detail.path) {
              serverErrors[detail.path] = detail.msg;
            }
          });
          this.editFormErrors = { ...this.editFormErrors, ...serverErrors };
        }

        const errorMessage = error.response?.data?.message || error.message || 'Failed to update user';
        this.showToast('error', errorMessage);
      } finally {
        this.editUserLoading = false;
      }
    },

    async viewUser(user) {
      try {
        const response = await userManagementService.getUser(user.id);

        if (response.success) {
          this.viewUserData = userManagementService.formatUserData(response.data);
          try {
            const modalElement = document.getElementById('viewUserModal');
            if (modalElement) {
              const modal = new Modal(modalElement);
              modal.show();
            }
          } catch (error) {
            console.error('Error showing view user modal:', error);
            this.showToast('error', 'Failed to open user details modal');
          }
        } else {
          throw new Error(response.message || 'Failed to load user details');
        }
      } catch (error) {
        console.error('Failed to load user details:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to load user details';
        this.showToast('error', errorMessage);
      }
    },

    // Additional user-specific methods can be added here
    // Navigation handlers are now provided by the mixin
  }
};
</script>

<style scoped>
@import './css/adminDashboard.css';

/* User avatar styles */
.user-avatar {
  width: 40px;
  height: 40px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  border-radius: 50%;
}

.user-avatar-large {
  width: 80px;
  height: 80px;
}

.avatar-placeholder-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 24px;
  border-radius: 50%;
}

/* Modal styles */
.modal-lg {
  max-width: 800px;
}

/* Form styles */
.form-label {
  font-weight: 600;
  color: #495057;
}

.form-control:focus,
.form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Loading states */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Table improvements */
.table th {
  border-top: none;
  font-weight: 600;
  color: #5a5c69;
  font-size: 0.875rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.875rem;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Button group improvements */
.btn-group-sm .btn {
  padding: 0.375rem 0.5rem;
  font-size: 0.75rem;
}

/* Search and filter improvements */
.form-select-sm {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

/* Pagination improvements */
.pagination-sm .page-link {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

/* Badge improvements */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.8rem;
  }

  .btn-group-sm .btn {
    padding: 0.25rem 0.375rem;
    font-size: 0.7rem;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .avatar-placeholder {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}

@media (max-width: 576px) {
  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }
}
</style>
