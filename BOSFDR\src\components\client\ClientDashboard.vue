<template>
  <div class="client-dashboard">
    <!-- Header Component -->
    <ClientHeader
      :user-name="getFullName()"
      :notification-count="notificationCount"
      :show-notifications="showNotifications"
      :show-user-dropdown="showUserDropdown"
      :sidebar-collapsed="sidebarCollapsed"
      :active-menu="activeMenu"
      @sidebar-toggle="toggleSidebar"
      @notification-toggle="toggleNotifications"
      @user-dropdown-toggle="toggleUserDropdown"
      @menu-action="handleMenuAction"
      @logout="logout"
      @view-all-notifications="handleViewAllNotifications"
    />

    <!-- Mobile Overlay -->
    <div
      class="mobile-overlay"
      :class="{ active: !sidebarCollapsed && isMobile }"
      @click="closeMobileSidebar"
    ></div>

    <!-- Layout Container -->
    <div class="layout-container">
      <!-- Sidebar -->
      <ClientSidebar
        :collapsed="sidebarCollapsed"
        :active-menu="activeMenu"
        :pending-requests="pendingRequests"
        :completed-requests="completedRequests"
        :total-services="totalServices"
        @menu-change="setActiveMenu"
        @toggle-sidebar="toggleSidebar"
        @logout="logout"
      />

      <!-- Main Content -->
      <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <div class="content-wrapper">
          <!-- Dashboard Content -->
          <div v-if="activeMenu === 'dashboard'" class="dashboard-content">
          <!-- Welcome Section -->
          <div class="welcome-section">
            <div class="welcome-card">
              <div class="welcome-content">
                <h2 class="welcome-title">Welcome back, {{ getFullName() }}!</h2>
                <p class="welcome-text">
                  Access barangay services and manage your requests from your dashboard.
                </p>
                <div class="account-status">
                  <span class="status-label">Account Status:</span>
                  <span class="status-badge" :class="getStatusBadgeClass()">
                    {{ getStatusText() }}
                  </span>
                </div>
              </div>
              <div class="welcome-avatar">
                <i class="fas fa-user-circle"></i>
              </div>
            </div>
          </div>

          <!-- Stats Cards -->
          <div class="stats-section">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                  <h3 class="stat-number">{{ totalRequests }}</h3>
                  <p class="stat-label">Total Requests</p>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon pending">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                  <h3 class="stat-number">{{ pendingRequests }}</h3>
                  <p class="stat-label">Pending</p>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon completed">
                  <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                  <h3 class="stat-number">{{ completedRequests }}</h3>
                  <p class="stat-label">Completed</p>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon">
                  <i class="fas fa-peso-sign"></i>
                </div>
                <div class="stat-content">
                  <h3 class="stat-number">₱{{ totalPaid }}</h3>
                  <p class="stat-label">Total Paid</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="quick-actions-section">
            <h3 class="section-title">Quick Actions</h3>
            <div class="actions-grid">
              <div class="action-card" @click="navigateTo('new-request')">
                <div class="action-icon">
                  <i class="fas fa-plus"></i>
                </div>
                <div class="action-content">
                  <h4 class="action-title">New Request</h4>
                  <p class="action-description">Apply for documents and certificates</p>
                </div>
              </div>

              <div class="action-card" @click="navigateTo('track-request')">
                <div class="action-icon">
                  <i class="fas fa-search"></i>
                </div>
                <div class="action-content">
                  <h4 class="action-title">Track Request</h4>
                  <p class="action-description">Check your application status</p>
                </div>
              </div>

              <div class="action-card" @click="navigateTo('payments')">
                <div class="action-icon">
                  <i class="fas fa-credit-card"></i>
                </div>
                <div class="action-content">
                  <h4 class="action-title">Make Payment</h4>
                  <p class="action-description">Pay for your requests online</p>
                </div>
              </div>

              <div class="action-card" @click="navigateTo('help')">
                <div class="action-icon">
                  <i class="fas fa-headset"></i>
                </div>
                <div class="action-content">
                  <h4 class="action-title">Get Help</h4>
                  <p class="action-description">Contact support for assistance</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="activity-section">
            <div class="activity-content">
              <div class="activity-main">
                <h3 class="section-title">Recent Activity</h3>
                <div v-if="recentActivity.length === 0" class="empty-state">
                  <i class="fas fa-inbox"></i>
                  <h4>No recent activity</h4>
                  <p>Your recent requests and updates will appear here</p>
                </div>

                <div v-else class="activity-list">
                  <div v-for="activity in recentActivity" :key="activity.id" class="activity-item">
                    <div class="activity-icon">
                      <i :class="activity.icon"></i>
                    </div>
                    <div class="activity-details">
                      <h4 class="activity-title">{{ activity.title }}</h4>
                      <p class="activity-description">{{ activity.description }}</p>
                      <span class="activity-time">{{ activity.time }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="account-info">
                <h3 class="section-title">Account Information</h3>
                <div class="info-list">
                  <div class="info-item">
                    <span class="info-label">Username</span>
                    <span class="info-value">{{ clientData?.username }}</span>
                  </div>

                  <div class="info-item">
                    <span class="info-label">Email</span>
                    <div class="info-value-with-status">
                      <span>{{ clientData?.profile?.email || 'Not provided' }}</span>
                      <i v-if="clientData?.email_verified"
                         class="fas fa-check-circle verified"
                         title="Verified"></i>
                      <i v-else
                         class="fas fa-exclamation-circle unverified"
                         title="Not verified"></i>
                    </div>
                  </div>

                  <div class="info-item">
                    <span class="info-label">Phone</span>
                    <div class="info-value-with-status">
                      <span>{{ clientData?.profile?.phone_number || 'Not provided' }}</span>
                      <i v-if="clientData?.phone_verified"
                         class="fas fa-check-circle verified"
                         title="Verified"></i>
                      <i v-else
                         class="fas fa-exclamation-circle unverified"
                         title="Not verified"></i>
                    </div>
                  </div>

                  <div class="info-item">
                    <span class="info-label">Member Since</span>
                    <span class="info-value">{{ formatDate(clientData?.created_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Help & Support Content -->
        <div v-else-if="activeMenu === 'help'" class="help-content">
          <HelpSupport />
        </div>

        <!-- Other Menu Content Placeholders -->
        <div v-else class="page-content">
          <div class="page-header">
            <h2 class="page-title">{{ getPageTitle() }}</h2>
            <p class="page-description">{{ getPageDescription() }}</p>
          </div>

          <div class="page-body">
            <div class="coming-soon">
              <i class="fas fa-tools"></i>
              <h3>Coming Soon</h3>
              <p>This feature is under development and will be available soon.</p>
            </div>
          </div>
        </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script src="./js/clientDashboard.js"></script>

<style scoped src="./css/clientDashboard.css"></style>
<style src="./css/mobileUtilities.css"></style>
<style scoped>
.help-content {
  padding: 0;
  margin: 0;
}
</style>
