{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\nimport { Modal } from 'bootstrap';\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      },\n      // Modal data\n      viewUserData: null,\n      addUserLoading: false,\n      editUserLoading: false,\n      // Form validation errors\n      formErrors: {},\n      editFormErrors: {},\n      // Add user form\n      addUserForm: {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null\n      },\n      // Edit user form\n      editUserForm: {\n        id: null,\n        username: '',\n        email: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        status: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null,\n        // Password reset fields\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      },\n      // Available options\n      genderOptions: [{\n        value: 'male',\n        label: 'Male'\n      }, {\n        value: 'female',\n        label: 'Female'\n      }],\n      statusOptions: [{\n        value: 'active',\n        label: 'Active'\n      }, {\n        value: 'inactive',\n        label: 'Inactive'\n      }, {\n        value: 'suspended',\n        label: 'Suspended'\n      }, {\n        value: 'pending_verification',\n        label: 'Pending Verification'\n      }]\n    };\n  },\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Make bootstrap available globally for this component\n    this.$bootstrap = {\n      Modal\n    };\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUserStats();\n    await this.loadUsers();\n  },\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n    // Load user statistics\n    async loadUserStats() {\n      try {\n        const response = await userManagementService.getUserStats();\n        if (response.success) {\n          this.userStats = response.data;\n        } else {\n          throw new Error(response.message || 'Failed to load user statistics');\n        }\n      } catch (error) {\n        console.error('Failed to load user statistics:', error);\n        // Fallback stats based on current users\n        this.calculateStats();\n      }\n    },\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50,\n          // Load more for client-side filtering\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true : this.filterStatus === 'inactive' ? false : undefined\n        };\n        const response = await userManagementService.getUsers(params);\n        if (response.success) {\n          // Format users for display\n          this.users = response.data.users.map(user => userManagementService.formatUserData(user));\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.$toast?.error?.(error.message || 'Failed to load users');\n\n        // Fallback to mock data for development\n        this.users = [{\n          id: 1,\n          username: 'admin12345',\n          full_name: 'System Administrator',\n          email: '<EMAIL>',\n          type: 'admin',\n          status: 'active',\n          created_at: new Date().toISOString(),\n          last_login: new Date().toISOString()\n        }, {\n          id: 2,\n          username: 'testapi',\n          full_name: 'Test User',\n          email: '<EMAIL>',\n          type: 'client',\n          status: 'active',\n          created_at: new Date().toISOString(),\n          last_login: null\n        }];\n        this.filteredUsers = [...this.users];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user => user.full_name.toLowerCase().includes(query) || user.email.toLowerCase().includes(query) || user.username.toLowerCase().includes(query));\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n    // User actions\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n          this.$toast?.success?.(`User ${user.full_name} has been ${newStatus === 'active' ? 'activated' : 'suspended'}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user status. Please try again.');\n      }\n    },\n    async deleteUser(user) {\n      if (!confirm(`Are you sure you want to delete user \"${user.full_name}\"? This action cannot be undone.`)) {\n        return;\n      }\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n        if (response.success) {\n          // Remove from local data\n          const index = this.users.findIndex(u => u.id === user.id);\n          if (index > -1) {\n            this.users.splice(index, 1);\n            this.filterUsers();\n            this.calculateStats();\n          }\n          this.$toast?.success?.(`User ${user.full_name} has been deleted.`);\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        this.$toast?.error?.(error.message || 'Failed to delete user. Please try again.');\n      }\n    },\n    // Modal methods\n    showAddUserModal() {\n      this.resetAddUserForm();\n      try {\n        const modalElement = document.getElementById('addUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing add user modal:', error);\n        this.$toast?.error?.('Failed to open add user modal');\n      }\n    },\n    resetAddUserForm() {\n      this.addUserForm = {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null\n      };\n      this.formErrors = {};\n    },\n    clearRoleSpecificFields() {\n      if (this.addUserForm.role === 'admin') {\n        // Clear client-specific fields\n        this.addUserForm.birth_date = '';\n        this.addUserForm.gender = '';\n        this.addUserForm.civil_status_id = 1;\n        this.addUserForm.nationality = 'Filipino';\n        this.addUserForm.house_number = '';\n        this.addUserForm.street = '';\n        this.addUserForm.subdivision = '';\n        this.addUserForm.barangay = '';\n        this.addUserForm.city_municipality = '';\n        this.addUserForm.province = '';\n        this.addUserForm.postal_code = '';\n        this.addUserForm.years_of_residency = null;\n        this.addUserForm.months_of_residency = null;\n      } else if (this.addUserForm.role === 'client') {\n        // Clear admin-specific fields\n        this.addUserForm.position = '';\n        this.addUserForm.department = '';\n        this.addUserForm.employee_id = '';\n        this.addUserForm.hire_date = '';\n      }\n    },\n    validateAddUserForm() {\n      const errors = {};\n\n      // Basic validation\n      if (!this.addUserForm.username || this.addUserForm.username.length < 3) {\n        errors.username = 'Username must be at least 3 characters long';\n      }\n      if (!this.addUserForm.password || this.addUserForm.password.length < 6) {\n        errors.password = 'Password must be at least 6 characters long';\n      }\n      if (!this.addUserForm.first_name || this.addUserForm.first_name.trim().length < 2) {\n        errors.first_name = 'First name must be at least 2 characters long';\n      }\n      if (!this.addUserForm.last_name || this.addUserForm.last_name.trim().length < 2) {\n        errors.last_name = 'Last name must be at least 2 characters long';\n      }\n      if (!this.addUserForm.role) {\n        errors.role = 'Please select a user type';\n      }\n      if (!this.addUserForm.phone_number || this.addUserForm.phone_number.trim().length < 10) {\n        errors.phone_number = 'Please provide a valid phone number';\n      }\n      if (this.addUserForm.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(this.addUserForm.email)) {\n        errors.email = 'Please provide a valid email address';\n      }\n\n      // Role-specific validation\n      if (this.addUserForm.role === 'client') {\n        if (!this.addUserForm.birth_date) {\n          errors.birth_date = 'Birth date is required for clients';\n        }\n        if (!this.addUserForm.gender) {\n          errors.gender = 'Gender is required for clients';\n        }\n        if (!this.addUserForm.barangay || this.addUserForm.barangay.trim().length < 2) {\n          errors.barangay = 'Barangay is required for clients';\n        }\n        if (!this.addUserForm.city_municipality || this.addUserForm.city_municipality.trim().length < 2) {\n          errors.city_municipality = 'City/Municipality is required for clients';\n        }\n        if (!this.addUserForm.province || this.addUserForm.province.trim().length < 2) {\n          errors.province = 'Province is required for clients';\n        }\n      }\n      this.formErrors = errors;\n      return Object.keys(errors).length === 0;\n    },\n    validateEditUserForm() {\n      const errors = {};\n\n      // Basic validation\n      if (!this.editUserForm.username || this.editUserForm.username.length < 3) {\n        errors.username = 'Username must be at least 3 characters long';\n      }\n      if (!this.editUserForm.first_name || this.editUserForm.first_name.trim().length < 2) {\n        errors.first_name = 'First name must be at least 2 characters long';\n      }\n      if (!this.editUserForm.last_name || this.editUserForm.last_name.trim().length < 2) {\n        errors.last_name = 'Last name must be at least 2 characters long';\n      }\n      if (!this.editUserForm.phone_number || this.editUserForm.phone_number.trim().length < 10) {\n        errors.phone_number = 'Please provide a valid phone number';\n      }\n      if (this.editUserForm.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(this.editUserForm.email)) {\n        errors.email = 'Please provide a valid email address';\n      }\n\n      // Password reset validation\n      if (this.editUserForm.resetPassword) {\n        if (!this.editUserForm.newPassword || this.editUserForm.newPassword.length < 6) {\n          errors.newPassword = 'New password must be at least 6 characters long';\n        }\n        if (this.editUserForm.newPassword !== this.editUserForm.confirmPassword) {\n          errors.confirmPassword = 'Passwords do not match';\n        }\n      }\n      this.editFormErrors = errors;\n      return Object.keys(errors).length === 0;\n    },\n    getFullAddress(user) {\n      const parts = [];\n      if (user.house_number) parts.push(user.house_number);\n      if (user.street) parts.push(user.street);\n      if (user.subdivision) parts.push(user.subdivision);\n      if (user.barangay) parts.push(user.barangay);\n      if (user.city_municipality) parts.push(user.city_municipality);\n      if (user.province) parts.push(user.province);\n      return parts.join(', ') || 'No address provided';\n    },\n    async submitAddUser() {\n      try {\n        this.addUserLoading = true;\n\n        // Validate form\n        if (!this.validateAddUserForm()) {\n          this.showToast('error', 'Please fix the validation errors before submitting.');\n          return;\n        }\n\n        // Prepare user data\n        const userData = {\n          ...this.addUserForm\n        };\n\n        // Convert numeric fields\n        if (userData.years_of_residency) {\n          userData.years_of_residency = parseInt(userData.years_of_residency);\n        }\n        if (userData.months_of_residency) {\n          userData.months_of_residency = parseInt(userData.months_of_residency);\n        }\n        if (userData.civil_status_id) {\n          userData.civil_status_id = parseInt(userData.civil_status_id);\n        }\n        console.log('Creating user with data:', userData);\n        const response = await userManagementService.createUser(userData);\n        if (response.success) {\n          this.showToast('success', 'User created successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('addUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Reset form and reload data\n          this.resetAddUserForm();\n          await this.loadUsers();\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to create user');\n        }\n      } catch (error) {\n        console.error('Failed to create user:', error);\n\n        // Handle validation errors from server\n        if (error.response?.data?.details) {\n          const serverErrors = {};\n          error.response.data.details.forEach(detail => {\n            if (detail.path) {\n              serverErrors[detail.path] = detail.msg;\n            }\n          });\n          this.formErrors = {\n            ...this.formErrors,\n            ...serverErrors\n          };\n        }\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to create user';\n        this.showToast('error', errorMessage);\n      } finally {\n        this.addUserLoading = false;\n      }\n    },\n    editUser(user) {\n      this.editUserForm = {\n        id: user.id,\n        username: user.username,\n        email: user.email || '',\n        first_name: user.first_name || '',\n        middle_name: user.middle_name || '',\n        last_name: user.last_name || '',\n        suffix: user.suffix || '',\n        role: user.type,\n        status: user.status,\n        phone_number: user.phone_number || '',\n        // Admin specific fields\n        position: user.position || '',\n        department: user.department || '',\n        employee_id: user.employee_id || '',\n        hire_date: user.hire_date ? user.hire_date.split('T')[0] : '',\n        // Client specific fields\n        birth_date: user.birth_date ? user.birth_date.split('T')[0] : '',\n        gender: user.gender || '',\n        civil_status_id: user.civil_status_id || 1,\n        nationality: user.nationality || 'Filipino',\n        house_number: user.house_number || '',\n        street: user.street || '',\n        subdivision: user.subdivision || '',\n        barangay: user.barangay || '',\n        city_municipality: user.city_municipality || '',\n        province: user.province || '',\n        postal_code: user.postal_code || '',\n        years_of_residency: user.years_of_residency || null,\n        months_of_residency: user.months_of_residency || null,\n        // Password reset fields\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      };\n      this.editFormErrors = {};\n      try {\n        const modalElement = document.getElementById('editUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing edit user modal:', error);\n        this.showToast('error', 'Failed to open edit user modal');\n      }\n    },\n    async submitEditUser() {\n      try {\n        this.editUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.editUserForm, true);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n        const response = await userManagementService.updateUser(this.editUserForm.id, this.editUserForm);\n        if (response.success) {\n          this.$toast?.success?.('User updated successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('editUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Update local data\n          const userIndex = this.users.findIndex(u => u.id === this.editUserForm.id);\n          if (userIndex > -1) {\n            this.users[userIndex] = {\n              ...this.users[userIndex],\n              ...this.editUserForm\n            };\n            this.filterUsers();\n          }\n\n          // Reload stats\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to update user');\n        }\n      } catch (error) {\n        console.error('Failed to update user:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user');\n      } finally {\n        this.editUserLoading = false;\n      }\n    },\n    async viewUser(user) {\n      try {\n        const response = await userManagementService.getUser(user.id);\n        if (response.success) {\n          this.viewUserData = response.data;\n          try {\n            const modalElement = document.getElementById('viewUserModal');\n            if (modalElement) {\n              const modal = new Modal(modalElement);\n              modal.show();\n            }\n          } catch (error) {\n            console.error('Error showing view user modal:', error);\n            this.$toast?.error?.('Failed to open user details modal');\n          }\n        } else {\n          throw new Error(response.message || 'Failed to load user details');\n        }\n      } catch (error) {\n        console.error('Failed to load user details:', error);\n        this.$toast?.error?.(error.message || 'Failed to load user details');\n      }\n    }\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};", "map": {"version": 3, "names": ["Ad<PERSON><PERSON><PERSON><PERSON>", "AdminSidebar", "adminAuthService", "userManagementService", "Modal", "name", "components", "data", "sidebarCollapsed", "showUserDropdown", "isMobile", "adminData", "users", "filteredUsers", "searchQuery", "filterStatus", "filterType", "currentPage", "itemsPerPage", "loading", "userStats", "total", "active", "pending", "admins", "viewUserData", "addUserLoading", "editUserLoading", "formErrors", "editFormErrors", "addUserForm", "username", "email", "password", "first_name", "middle_name", "last_name", "suffix", "role", "phone_number", "position", "department", "employee_id", "hire_date", "birth_date", "gender", "civil_status_id", "nationality", "house_number", "street", "subdivision", "barangay", "city_municipality", "province", "postal_code", "years_of_residency", "months_of_residency", "editUserForm", "id", "status", "resetPassword", "newPassword", "confirmPassword", "genderOptions", "value", "label", "statusOptions", "computed", "activeMenu", "path", "$route", "includes", "paginatedUsers", "start", "end", "slice", "totalPages", "Math", "ceil", "length", "visiblePages", "pages", "max", "min", "i", "push", "mounted", "isLoggedIn", "$router", "initializeUI", "$bootstrap", "loadAdminProfile", "loadUserStats", "loadUsers", "beforeUnmount", "handleResize", "window", "removeEventListener", "methods", "innerWidth", "saved", "localStorage", "getItem", "JSON", "parse", "was<PERSON><PERSON><PERSON>", "addEventListener", "handleSidebarToggle", "setItem", "stringify", "handleMenuChange", "menu", "routes", "handleUserDropdownToggle", "handleMenuAction", "action", "closeMobileSidebar", "handleLogout", "logout", "response", "getProfile", "success", "error", "console", "getAdminData", "getUserStats", "Error", "message", "calculateStats", "params", "page", "limit", "search", "undefined", "is_active", "getUsers", "map", "user", "formatUserData", "$toast", "full_name", "type", "created_at", "Date", "toISOString", "last_login", "filter", "u", "searchUsers", "filterUsers", "filtered", "query", "toLowerCase", "changePage", "getInitials", "fullName", "split", "char<PERSON>t", "join", "toUpperCase", "getStatusBadgeClass", "classes", "formatStatus", "formatDate", "dateString", "date", "toLocaleDateString", "year", "month", "day", "toggleUserStatus", "newStatus", "reason", "updateUserStatus", "deleteUser", "confirm", "index", "findIndex", "splice", "showAddUserModal", "resetAddUserForm", "modalElement", "document", "getElementById", "modal", "show", "clearRoleSpecificFields", "validateAddUserForm", "errors", "trim", "test", "Object", "keys", "validateEditUserForm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parts", "submitAddUser", "showToast", "userData", "parseInt", "log", "createUser", "getInstance", "hide", "details", "serverErrors", "for<PERSON>ach", "detail", "msg", "errorMessage", "editUser", "submitEditUser", "validation", "validateUserData", "<PERSON><PERSON><PERSON><PERSON>", "updateUser", "userIndex", "viewUser", "getUser"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminUsers.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-users\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"container-fluid p-4\">\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-outline-success btn-sm\" @click=\"loadUsers\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                  <button class=\"btn btn-success btn-sm\" @click=\"showAddUserModal\">\n                    <i class=\"fas fa-user-plus me-1\"></i>\n                    Add User\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- User Statistics -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-primary shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">\n                        Total Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.total || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-users fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-success shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">\n                        Active Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.active || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-check fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-warning shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">\n                        Pending Verification\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.pending || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-clock fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-info shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">\n                        Admin Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.admins || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-shield fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Users Table -->\n          <div class=\"row\">\n            <div class=\"col-12\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex justify-content-between align-items-center\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-users me-2\"></i>\n                    User List\n                  </h6>\n                  <div class=\"d-flex gap-2\">\n                    <select class=\"form-select form-select-sm\" v-model=\"filterStatus\" @change=\"filterUsers\">\n                      <option value=\"\">All Status</option>\n                      <option value=\"active\">Active</option>\n                      <option value=\"inactive\">Inactive</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"suspended\">Suspended</option>\n                    </select>\n                    <select class=\"form-select form-select-sm\" v-model=\"filterType\" @change=\"filterUsers\">\n                      <option value=\"\">All Types</option>\n                      <option value=\"client\">Clients</option>\n                      <option value=\"admin\">Admins</option>\n                    </select>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <!-- Search Bar -->\n                  <div class=\"row mb-3\">\n                    <div class=\"col-md-6\">\n                      <div class=\"input-group\">\n                        <span class=\"input-group-text\">\n                          <i class=\"fas fa-search\"></i>\n                        </span>\n                        <input\n                          type=\"text\"\n                          class=\"form-control\"\n                          placeholder=\"Search users by name, email, or username...\"\n                          v-model=\"searchQuery\"\n                          @input=\"searchUsers\"\n                        >\n                      </div>\n                    </div>\n                    <div class=\"col-md-6 text-end\">\n                      <span class=\"text-muted\">\n                        Showing {{ filteredUsers.length }} of {{ users.length }} users\n                      </span>\n                    </div>\n                  </div>\n\n                  <!-- Loading State -->\n                  <div v-if=\"loading\" class=\"text-center py-4\">\n                    <div class=\"spinner-border text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Loading...</span>\n                    </div>\n                    <p class=\"text-muted mt-2\">Loading users...</p>\n                  </div>\n\n                  <!-- Empty State -->\n                  <div v-else-if=\"filteredUsers.length === 0\" class=\"text-center py-5\">\n                    <i class=\"fas fa-users fa-3x text-gray-300 mb-3\"></i>\n                    <h5 class=\"text-gray-600\">No users found</h5>\n                    <p class=\"text-muted\">\n                      {{ searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.' }}\n                    </p>\n                  </div>\n\n                  <!-- Users Table -->\n                  <div v-else class=\"table-responsive\">\n                    <table class=\"table table-hover\">\n                      <thead class=\"table-light\">\n                        <tr>\n                          <th>User</th>\n                          <th>Email</th>\n                          <th>Type</th>\n                          <th>Status</th>\n                          <th>Registered</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        <tr v-for=\"user in paginatedUsers\" :key=\"user.id\">\n                          <td>\n                            <div class=\"d-flex align-items-center\">\n                              <div class=\"user-avatar me-3\">\n                                <img v-if=\"user.profile_picture\" :src=\"user.profile_picture\" :alt=\"user.full_name\" class=\"rounded-circle\">\n                                <div v-else class=\"avatar-placeholder rounded-circle\">\n                                  {{ getInitials(user.full_name) }}\n                                </div>\n                              </div>\n                              <div>\n                                <div class=\"fw-bold\">{{ user.full_name }}</div>\n                                <div class=\"text-muted small\">@{{ user.username }}</div>\n                              </div>\n                            </div>\n                          </td>\n                          <td>{{ user.email }}</td>\n                          <td>\n                            <span class=\"badge\" :class=\"user.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                              {{ user.type === 'admin' ? 'Admin' : 'Client' }}\n                            </span>\n                          </td>\n                          <td>\n                            <span class=\"badge\" :class=\"getStatusBadgeClass(user.status)\">\n                              {{ formatStatus(user.status) }}\n                            </span>\n                          </td>\n                          <td>{{ formatDate(user.created_at) }}</td>\n                          <td>\n                            <div class=\"btn-group btn-group-sm\">\n                              <button class=\"btn btn-outline-primary\" @click=\"viewUser(user)\" title=\"View Details\">\n                                <i class=\"fas fa-eye\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-warning\" @click=\"editUser(user)\" title=\"Edit User\">\n                                <i class=\"fas fa-edit\"></i>\n                              </button>\n                              <button\n                                class=\"btn\"\n                                :class=\"user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'\"\n                                @click=\"toggleUserStatus(user)\"\n                                :title=\"user.status === 'active' ? 'Suspend User' : 'Activate User'\"\n                              >\n                                <i :class=\"user.status === 'active' ? 'fas fa-pause' : 'fas fa-play'\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-danger\" @click=\"deleteUser(user)\" title=\"Delete User\">\n                                <i class=\"fas fa-trash\"></i>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      </tbody>\n                    </table>\n                  </div>\n\n                  <!-- Pagination -->\n                  <div v-if=\"totalPages > 1\" class=\"d-flex justify-content-between align-items-center mt-3\">\n                    <div class=\"text-muted\">\n                      Page {{ currentPage }} of {{ totalPages }}\n                    </div>\n                    <nav>\n                      <ul class=\"pagination pagination-sm mb-0\">\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === 1 }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage - 1)\" :disabled=\"currentPage === 1\">\n                            Previous\n                          </button>\n                        </li>\n                        <li\n                          v-for=\"page in visiblePages\"\n                          :key=\"page\"\n                          class=\"page-item\"\n                          :class=\"{ active: page === currentPage }\"\n                        >\n                          <button class=\"page-link\" @click=\"changePage(page)\">{{ page }}</button>\n                        </li>\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === totalPages }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage + 1)\" :disabled=\"currentPage === totalPages\">\n                            Next\n                          </button>\n                        </li>\n                      </ul>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n\n    <!-- Add User Modal -->\n    <div class=\"modal fade\" id=\"addUserModal\" tabindex=\"-1\" aria-labelledby=\"addUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"addUserModalLabel\">\n              <i class=\"fas fa-user-plus me-2\"></i>\n              Add New User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitAddUser\">\n              <!-- Basic Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addUsername\"\n                    v-model=\"addUserForm.username\"\n                    :class=\"{ 'is-invalid': formErrors.username }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.username\" class=\"invalid-feedback\">\n                    {{ formErrors.username }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addPassword\" class=\"form-label\">Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"addPassword\"\n                    v-model=\"addUserForm.password\"\n                    :class=\"{ 'is-invalid': formErrors.password }\"\n                    required\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"formErrors.password\" class=\"invalid-feedback\">\n                    {{ formErrors.password }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Personal Information -->\n              <div class=\"row\">\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addFirstName\"\n                    v-model=\"addUserForm.first_name\"\n                    :class=\"{ 'is-invalid': formErrors.first_name }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.first_name\" class=\"invalid-feedback\">\n                    {{ formErrors.first_name }}\n                  </div>\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addMiddleName\" class=\"form-label\">Middle Name</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addMiddleName\"\n                    v-model=\"addUserForm.middle_name\"\n                  >\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"addLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addLastName\"\n                    v-model=\"addUserForm.last_name\"\n                    :class=\"{ 'is-invalid': formErrors.last_name }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.last_name\" class=\"invalid-feedback\">\n                    {{ formErrors.last_name }}\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addSuffix\" class=\"form-label\">Suffix</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addSuffix\"\n                    v-model=\"addUserForm.suffix\"\n                    placeholder=\"Jr, Sr, III, etc.\"\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addRole\" class=\"form-label\">User Type *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"addRole\"\n                    v-model=\"addUserForm.role\"\n                    :class=\"{ 'is-invalid': formErrors.role }\"\n                    required\n                    @change=\"clearRoleSpecificFields\"\n                  >\n                    <option value=\"\">Select User Type</option>\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                  <div v-if=\"formErrors.role\" class=\"invalid-feedback\">\n                    {{ formErrors.role }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Contact Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addEmail\" class=\"form-label\">Email</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"addEmail\"\n                    v-model=\"addUserForm.email\"\n                    :class=\"{ 'is-invalid': formErrors.email }\"\n                  >\n                  <div v-if=\"formErrors.email\" class=\"invalid-feedback\">\n                    {{ formErrors.email }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addPhone\" class=\"form-label\">Phone Number *</label>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"addPhone\"\n                    v-model=\"addUserForm.phone_number\"\n                    :class=\"{ 'is-invalid': formErrors.phone_number }\"\n                    required\n                  >\n                  <div v-if=\"formErrors.phone_number\" class=\"invalid-feedback\">\n                    {{ formErrors.phone_number }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Admin-specific fields -->\n              <div v-if=\"addUserForm.role === 'admin'\">\n                <hr>\n                <h6 class=\"text-primary mb-3\">\n                  <i class=\"fas fa-user-shield me-2\"></i>\n                  Administrator Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addEmployeeId\" class=\"form-label\">Employee ID</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addEmployeeId\"\n                      v-model=\"addUserForm.employee_id\"\n                      placeholder=\"e.g., EMP001\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addPosition\" class=\"form-label\">Position</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addPosition\"\n                      v-model=\"addUserForm.position\"\n                      placeholder=\"e.g., Barangay Secretary\"\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addDepartment\" class=\"form-label\">Department</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addDepartment\"\n                      v-model=\"addUserForm.department\"\n                      placeholder=\"e.g., Administration\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addHireDate\" class=\"form-label\">Hire Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"addHireDate\"\n                      v-model=\"addUserForm.hire_date\"\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Client-specific fields -->\n              <div v-if=\"addUserForm.role === 'client'\">\n                <hr>\n                <h6 class=\"text-info mb-3\">\n                  <i class=\"fas fa-user me-2\"></i>\n                  Client Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addBirthDate\" class=\"form-label\">Birth Date *</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"addBirthDate\"\n                      v-model=\"addUserForm.birth_date\"\n                      :class=\"{ 'is-invalid': formErrors.birth_date }\"\n                      required\n                    >\n                    <div v-if=\"formErrors.birth_date\" class=\"invalid-feedback\">\n                      {{ formErrors.birth_date }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addGender\" class=\"form-label\">Gender *</label>\n                    <select\n                      class=\"form-select\"\n                      id=\"addGender\"\n                      v-model=\"addUserForm.gender\"\n                      :class=\"{ 'is-invalid': formErrors.gender }\"\n                      required\n                    >\n                      <option value=\"\">Select Gender</option>\n                      <option value=\"male\">Male</option>\n                      <option value=\"female\">Female</option>\n                    </select>\n                    <div v-if=\"formErrors.gender\" class=\"invalid-feedback\">\n                      {{ formErrors.gender }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addCivilStatus\" class=\"form-label\">Civil Status *</label>\n                    <select\n                      class=\"form-select\"\n                      id=\"addCivilStatus\"\n                      v-model=\"addUserForm.civil_status_id\"\n                      required\n                    >\n                      <option value=\"1\">Single</option>\n                      <option value=\"2\">Married</option>\n                      <option value=\"3\">Widowed</option>\n                      <option value=\"4\">Divorced</option>\n                      <option value=\"5\">Separated</option>\n                    </select>\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addNationality\" class=\"form-label\">Nationality</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addNationality\"\n                      v-model=\"addUserForm.nationality\"\n                      placeholder=\"Filipino\"\n                    >\n                  </div>\n                </div>\n\n                <!-- Address Information -->\n                <h6 class=\"text-secondary mb-3 mt-4\">\n                  <i class=\"fas fa-map-marker-alt me-2\"></i>\n                  Address Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addHouseNumber\" class=\"form-label\">House Number</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addHouseNumber\"\n                      v-model=\"addUserForm.house_number\"\n                      placeholder=\"e.g., 123\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addStreet\" class=\"form-label\">Street</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addStreet\"\n                      v-model=\"addUserForm.street\"\n                      placeholder=\"e.g., Main Street\"\n                    >\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addSubdivision\" class=\"form-label\">Subdivision</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addSubdivision\"\n                      v-model=\"addUserForm.subdivision\"\n                      placeholder=\"e.g., Greenfield Village\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addBarangay\" class=\"form-label\">Barangay *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addBarangay\"\n                      v-model=\"addUserForm.barangay\"\n                      :class=\"{ 'is-invalid': formErrors.barangay }\"\n                      required\n                      placeholder=\"e.g., Barangay Bula\"\n                    >\n                    <div v-if=\"formErrors.barangay\" class=\"invalid-feedback\">\n                      {{ formErrors.barangay }}\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"row\">\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addCity\" class=\"form-label\">City/Municipality *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addCity\"\n                      v-model=\"addUserForm.city_municipality\"\n                      :class=\"{ 'is-invalid': formErrors.city_municipality }\"\n                      required\n                      placeholder=\"e.g., Camarines Sur\"\n                    >\n                    <div v-if=\"formErrors.city_municipality\" class=\"invalid-feedback\">\n                      {{ formErrors.city_municipality }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addProvince\" class=\"form-label\">Province *</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addProvince\"\n                      v-model=\"addUserForm.province\"\n                      :class=\"{ 'is-invalid': formErrors.province }\"\n                      required\n                      placeholder=\"e.g., Camarines Sur\"\n                    >\n                    <div v-if=\"formErrors.province\" class=\"invalid-feedback\">\n                      {{ formErrors.province }}\n                    </div>\n                  </div>\n                  <div class=\"col-md-4 mb-3\">\n                    <label for=\"addPostalCode\" class=\"form-label\">Postal Code</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"addPostalCode\"\n                      v-model=\"addUserForm.postal_code\"\n                      placeholder=\"e.g., 4422\"\n                    >\n                  </div>\n                </div>\n\n                <!-- Residency Information -->\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addYearsResidency\" class=\"form-label\">Years of Residency</label>\n                    <input\n                      type=\"number\"\n                      class=\"form-control\"\n                      id=\"addYearsResidency\"\n                      v-model=\"addUserForm.years_of_residency\"\n                      min=\"0\"\n                      placeholder=\"e.g., 5\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"addMonthsResidency\" class=\"form-label\">Additional Months</label>\n                    <input\n                      type=\"number\"\n                      class=\"form-control\"\n                      id=\"addMonthsResidency\"\n                      v-model=\"addUserForm.months_of_residency\"\n                      min=\"0\"\n                      max=\"11\"\n                      placeholder=\"e.g., 6\"\n                    >\n                  </div>\n                </div>\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitAddUser\" :disabled=\"addUserLoading\">\n              <span v-if=\"addUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-plus me-2\"></i>\n              {{ addUserLoading ? 'Creating...' : 'Create User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Edit User Modal -->\n    <div class=\"modal fade\" id=\"editUserModal\" tabindex=\"-1\" aria-labelledby=\"editUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"editUserModalLabel\">\n              <i class=\"fas fa-user-edit me-2\"></i>\n              Edit User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitEditUser\" v-if=\"editUserForm.id\">\n              <!-- Basic Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editUsername\"\n                    v-model=\"editUserForm.username\"\n                    :class=\"{ 'is-invalid': editFormErrors.username }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.username\" class=\"invalid-feedback\">\n                    {{ editFormErrors.username }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editStatus\" class=\"form-label\">Status *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"editStatus\"\n                    v-model=\"editUserForm.status\"\n                    :class=\"{ 'is-invalid': editFormErrors.status }\"\n                    required\n                  >\n                    <option value=\"active\">Active</option>\n                    <option value=\"inactive\">Inactive</option>\n                    <option value=\"suspended\">Suspended</option>\n                    <option value=\"pending_verification\">Pending Verification</option>\n                  </select>\n                  <div v-if=\"editFormErrors.status\" class=\"invalid-feedback\">\n                    {{ editFormErrors.status }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Personal Information -->\n              <div class=\"row\">\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editFirstName\"\n                    v-model=\"editUserForm.first_name\"\n                    :class=\"{ 'is-invalid': editFormErrors.first_name }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.first_name\" class=\"invalid-feedback\">\n                    {{ editFormErrors.first_name }}\n                  </div>\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editMiddleName\" class=\"form-label\">Middle Name</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editMiddleName\"\n                    v-model=\"editUserForm.middle_name\"\n                  >\n                </div>\n                <div class=\"col-md-4 mb-3\">\n                  <label for=\"editLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editLastName\"\n                    v-model=\"editUserForm.last_name\"\n                    :class=\"{ 'is-invalid': editFormErrors.last_name }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.last_name\" class=\"invalid-feedback\">\n                    {{ editFormErrors.last_name }}\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editSuffix\" class=\"form-label\">Suffix</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editSuffix\"\n                    v-model=\"editUserForm.suffix\"\n                    placeholder=\"Jr, Sr, III, etc.\"\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editRole\" class=\"form-label\">User Type *</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"editRole\"\n                    v-model=\"editUserForm.role\"\n                    :class=\"{ 'is-invalid': editFormErrors.role }\"\n                    required\n                    disabled\n                  >\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                  <small class=\"text-muted\">User type cannot be changed after creation</small>\n                  <div v-if=\"editFormErrors.role\" class=\"invalid-feedback\">\n                    {{ editFormErrors.role }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Contact Information -->\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editEmail\" class=\"form-label\">Email</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"editEmail\"\n                    v-model=\"editUserForm.email\"\n                    :class=\"{ 'is-invalid': editFormErrors.email }\"\n                  >\n                  <div v-if=\"editFormErrors.email\" class=\"invalid-feedback\">\n                    {{ editFormErrors.email }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editPhone\" class=\"form-label\">Phone Number *</label>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"editPhone\"\n                    v-model=\"editUserForm.phone_number\"\n                    :class=\"{ 'is-invalid': editFormErrors.phone_number }\"\n                    required\n                  >\n                  <div v-if=\"editFormErrors.phone_number\" class=\"invalid-feedback\">\n                    {{ editFormErrors.phone_number }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Admin-specific fields -->\n              <div v-if=\"editUserForm.role === 'admin'\">\n                <hr>\n                <h6 class=\"text-primary mb-3\">\n                  <i class=\"fas fa-user-shield me-2\"></i>\n                  Administrator Information\n                </h6>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editEmployeeId\" class=\"form-label\">Employee ID</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editEmployeeId\"\n                      v-model=\"editUserForm.employee_id\"\n                      placeholder=\"e.g., EMP001\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editPosition\" class=\"form-label\">Position</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editPosition\"\n                      v-model=\"editUserForm.position\"\n                      placeholder=\"e.g., Barangay Secretary\"\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editDepartment\" class=\"form-label\">Department</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      id=\"editDepartment\"\n                      v-model=\"editUserForm.department\"\n                      placeholder=\"e.g., Administration\"\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label for=\"editHireDate\" class=\"form-label\">Hire Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      id=\"editHireDate\"\n                      v-model=\"editUserForm.hire_date\"\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Client-specific fields (read-only for editing) -->\n              <div v-if=\"editUserForm.role === 'client'\">\n                <hr>\n                <h6 class=\"text-info mb-3\">\n                  <i class=\"fas fa-user me-2\"></i>\n                  Client Information\n                </h6>\n                <div class=\"alert alert-info\">\n                  <i class=\"fas fa-info-circle me-2\"></i>\n                  Client-specific information can only be updated by the client through their profile.\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-6 mb-3\">\n                    <label class=\"form-label\">Birth Date</label>\n                    <input\n                      type=\"date\"\n                      class=\"form-control\"\n                      v-model=\"editUserForm.birth_date\"\n                      readonly\n                    >\n                  </div>\n                  <div class=\"col-md-6 mb-3\">\n                    <label class=\"form-label\">Gender</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      :value=\"editUserForm.gender ? editUserForm.gender.charAt(0).toUpperCase() + editUserForm.gender.slice(1) : ''\"\n                      readonly\n                    >\n                  </div>\n                </div>\n                <div class=\"row\">\n                  <div class=\"col-md-12 mb-3\">\n                    <label class=\"form-label\">Address</label>\n                    <input\n                      type=\"text\"\n                      class=\"form-control\"\n                      :value=\"getFullAddress(editUserForm)\"\n                      readonly\n                    >\n                  </div>\n                </div>\n              </div>\n\n              <!-- Password Reset Section -->\n              <hr>\n              <div class=\"row\">\n                <div class=\"col-12 mb-3\">\n                  <div class=\"form-check\">\n                    <input\n                      class=\"form-check-input\"\n                      type=\"checkbox\"\n                      id=\"resetPassword\"\n                      v-model=\"editUserForm.resetPassword\"\n                    >\n                    <label class=\"form-check-label\" for=\"resetPassword\">\n                      Reset user password\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <div v-if=\"editUserForm.resetPassword\" class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editNewPassword\" class=\"form-label\">New Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"editNewPassword\"\n                    v-model=\"editUserForm.newPassword\"\n                    :class=\"{ 'is-invalid': editFormErrors.newPassword }\"\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"editFormErrors.newPassword\" class=\"invalid-feedback\">\n                    {{ editFormErrors.newPassword }}\n                  </div>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editConfirmPassword\" class=\"form-label\">Confirm Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"editConfirmPassword\"\n                    v-model=\"editUserForm.confirmPassword\"\n                    :class=\"{ 'is-invalid': editFormErrors.confirmPassword }\"\n                    minlength=\"6\"\n                  >\n                  <div v-if=\"editFormErrors.confirmPassword\" class=\"invalid-feedback\">\n                    {{ editFormErrors.confirmPassword }}\n                  </div>\n                </div>\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitEditUser\" :disabled=\"editUserLoading\">\n              <span v-if=\"editUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-save me-2\"></i>\n              {{ editUserLoading ? 'Updating...' : 'Update User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- View User Modal -->\n    <div class=\"modal fade\" id=\"viewUserModal\" tabindex=\"-1\" aria-labelledby=\"viewUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"viewUserModalLabel\">\n              <i class=\"fas fa-user me-2\"></i>\n              User Details\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\" v-if=\"viewUserData\">\n            <div class=\"row\">\n              <div class=\"col-md-4 text-center mb-4\">\n                <div class=\"user-avatar-large mx-auto mb-3\">\n                  <img v-if=\"viewUserData.profile_picture\" :src=\"viewUserData.profile_picture\" :alt=\"viewUserData.full_name\" class=\"rounded-circle\">\n                  <div v-else class=\"avatar-placeholder-large rounded-circle\">\n                    {{ getInitials(viewUserData.full_name) }}\n                  </div>\n                </div>\n                <h5>{{ viewUserData.full_name }}</h5>\n                <span class=\"badge\" :class=\"getStatusBadgeClass(viewUserData.status)\">\n                  {{ formatStatus(viewUserData.status) }}\n                </span>\n              </div>\n              <div class=\"col-md-8\">\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Username:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.username }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Email:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.email }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Type:</strong></div>\n                  <div class=\"col-sm-8\">\n                    <span class=\"badge\" :class=\"viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                      {{ viewUserData.type === 'admin' ? 'Administrator' : 'Client' }}\n                    </span>\n                  </div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Phone:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.phone_number || 'N/A' }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Registered:</strong></div>\n                  <div class=\"col-sm-8\">{{ formatDate(viewUserData.created_at) }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Last Login:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.last_login ? formatDate(viewUserData.last_login) : 'Never' }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Close</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"editUser(viewUserData)\" data-bs-dismiss=\"modal\">\n              <i class=\"fas fa-edit me-2\"></i>\n              Edit User\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\nimport { Modal } from 'bootstrap';\n\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      },\n\n      // Modal data\n      viewUserData: null,\n      addUserLoading: false,\n      editUserLoading: false,\n\n      // Form validation errors\n      formErrors: {},\n      editFormErrors: {},\n\n      // Add user form\n      addUserForm: {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null\n      },\n\n      // Edit user form\n      editUserForm: {\n        id: null,\n        username: '',\n        email: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        status: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null,\n        // Password reset fields\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      },\n\n      // Available options\n      genderOptions: [\n        { value: 'male', label: 'Male' },\n        { value: 'female', label: 'Female' }\n      ],\n\n      statusOptions: [\n        { value: 'active', label: 'Active' },\n        { value: 'inactive', label: 'Inactive' },\n        { value: 'suspended', label: 'Suspended' },\n        { value: 'pending_verification', label: 'Pending Verification' }\n      ]\n    };\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Make bootstrap available globally for this component\n    this.$bootstrap = { Modal };\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUserStats();\n    await this.loadUsers();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load user statistics\n    async loadUserStats() {\n      try {\n        const response = await userManagementService.getUserStats();\n\n        if (response.success) {\n          this.userStats = response.data;\n        } else {\n          throw new Error(response.message || 'Failed to load user statistics');\n        }\n      } catch (error) {\n        console.error('Failed to load user statistics:', error);\n        // Fallback stats based on current users\n        this.calculateStats();\n      }\n    },\n\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50, // Load more for client-side filtering\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true :\n                     this.filterStatus === 'inactive' ? false : undefined\n        };\n\n        const response = await userManagementService.getUsers(params);\n\n        if (response.success) {\n          // Format users for display\n          this.users = response.data.users.map(user =>\n            userManagementService.formatUserData(user)\n          );\n\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.$toast?.error?.(error.message || 'Failed to load users');\n\n        // Fallback to mock data for development\n        this.users = [\n          {\n            id: 1,\n            username: 'admin12345',\n            full_name: 'System Administrator',\n            email: '<EMAIL>',\n            type: 'admin',\n            status: 'active',\n            created_at: new Date().toISOString(),\n            last_login: new Date().toISOString()\n          },\n          {\n            id: 2,\n            username: 'testapi',\n            full_name: 'Test User',\n            email: '<EMAIL>',\n            type: 'client',\n            status: 'active',\n            created_at: new Date().toISOString(),\n            last_login: null\n          }\n        ];\n\n        this.filteredUsers = [...this.users];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user =>\n          user.full_name.toLowerCase().includes(query) ||\n          user.email.toLowerCase().includes(query) ||\n          user.username.toLowerCase().includes(query)\n        );\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    // User actions\n\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n\n          this.$toast?.success?.(`User ${user.full_name} has been ${newStatus === 'active' ? 'activated' : 'suspended'}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user status. Please try again.');\n      }\n    },\n\n    async deleteUser(user) {\n      if (!confirm(`Are you sure you want to delete user \"${user.full_name}\"? This action cannot be undone.`)) {\n        return;\n      }\n\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n\n        if (response.success) {\n          // Remove from local data\n          const index = this.users.findIndex(u => u.id === user.id);\n          if (index > -1) {\n            this.users.splice(index, 1);\n            this.filterUsers();\n            this.calculateStats();\n          }\n\n          this.$toast?.success?.(`User ${user.full_name} has been deleted.`);\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        this.$toast?.error?.(error.message || 'Failed to delete user. Please try again.');\n      }\n    },\n\n\n\n    // Modal methods\n    showAddUserModal() {\n      this.resetAddUserForm();\n      try {\n        const modalElement = document.getElementById('addUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing add user modal:', error);\n        this.$toast?.error?.('Failed to open add user modal');\n      }\n    },\n\n    resetAddUserForm() {\n      this.addUserForm = {\n        username: '',\n        email: '',\n        password: '',\n        first_name: '',\n        middle_name: '',\n        last_name: '',\n        suffix: '',\n        role: '',\n        phone_number: '',\n        // Admin specific fields\n        position: '',\n        department: '',\n        employee_id: '',\n        hire_date: '',\n        // Client specific fields\n        birth_date: '',\n        gender: '',\n        civil_status_id: 1,\n        nationality: 'Filipino',\n        house_number: '',\n        street: '',\n        subdivision: '',\n        barangay: '',\n        city_municipality: '',\n        province: '',\n        postal_code: '',\n        years_of_residency: null,\n        months_of_residency: null\n      };\n      this.formErrors = {};\n    },\n\n    clearRoleSpecificFields() {\n      if (this.addUserForm.role === 'admin') {\n        // Clear client-specific fields\n        this.addUserForm.birth_date = '';\n        this.addUserForm.gender = '';\n        this.addUserForm.civil_status_id = 1;\n        this.addUserForm.nationality = 'Filipino';\n        this.addUserForm.house_number = '';\n        this.addUserForm.street = '';\n        this.addUserForm.subdivision = '';\n        this.addUserForm.barangay = '';\n        this.addUserForm.city_municipality = '';\n        this.addUserForm.province = '';\n        this.addUserForm.postal_code = '';\n        this.addUserForm.years_of_residency = null;\n        this.addUserForm.months_of_residency = null;\n      } else if (this.addUserForm.role === 'client') {\n        // Clear admin-specific fields\n        this.addUserForm.position = '';\n        this.addUserForm.department = '';\n        this.addUserForm.employee_id = '';\n        this.addUserForm.hire_date = '';\n      }\n    },\n\n    validateAddUserForm() {\n      const errors = {};\n\n      // Basic validation\n      if (!this.addUserForm.username || this.addUserForm.username.length < 3) {\n        errors.username = 'Username must be at least 3 characters long';\n      }\n\n      if (!this.addUserForm.password || this.addUserForm.password.length < 6) {\n        errors.password = 'Password must be at least 6 characters long';\n      }\n\n      if (!this.addUserForm.first_name || this.addUserForm.first_name.trim().length < 2) {\n        errors.first_name = 'First name must be at least 2 characters long';\n      }\n\n      if (!this.addUserForm.last_name || this.addUserForm.last_name.trim().length < 2) {\n        errors.last_name = 'Last name must be at least 2 characters long';\n      }\n\n      if (!this.addUserForm.role) {\n        errors.role = 'Please select a user type';\n      }\n\n      if (!this.addUserForm.phone_number || this.addUserForm.phone_number.trim().length < 10) {\n        errors.phone_number = 'Please provide a valid phone number';\n      }\n\n      if (this.addUserForm.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(this.addUserForm.email)) {\n        errors.email = 'Please provide a valid email address';\n      }\n\n      // Role-specific validation\n      if (this.addUserForm.role === 'client') {\n        if (!this.addUserForm.birth_date) {\n          errors.birth_date = 'Birth date is required for clients';\n        }\n\n        if (!this.addUserForm.gender) {\n          errors.gender = 'Gender is required for clients';\n        }\n\n        if (!this.addUserForm.barangay || this.addUserForm.barangay.trim().length < 2) {\n          errors.barangay = 'Barangay is required for clients';\n        }\n\n        if (!this.addUserForm.city_municipality || this.addUserForm.city_municipality.trim().length < 2) {\n          errors.city_municipality = 'City/Municipality is required for clients';\n        }\n\n        if (!this.addUserForm.province || this.addUserForm.province.trim().length < 2) {\n          errors.province = 'Province is required for clients';\n        }\n      }\n\n      this.formErrors = errors;\n      return Object.keys(errors).length === 0;\n    },\n\n    validateEditUserForm() {\n      const errors = {};\n\n      // Basic validation\n      if (!this.editUserForm.username || this.editUserForm.username.length < 3) {\n        errors.username = 'Username must be at least 3 characters long';\n      }\n\n      if (!this.editUserForm.first_name || this.editUserForm.first_name.trim().length < 2) {\n        errors.first_name = 'First name must be at least 2 characters long';\n      }\n\n      if (!this.editUserForm.last_name || this.editUserForm.last_name.trim().length < 2) {\n        errors.last_name = 'Last name must be at least 2 characters long';\n      }\n\n      if (!this.editUserForm.phone_number || this.editUserForm.phone_number.trim().length < 10) {\n        errors.phone_number = 'Please provide a valid phone number';\n      }\n\n      if (this.editUserForm.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(this.editUserForm.email)) {\n        errors.email = 'Please provide a valid email address';\n      }\n\n      // Password reset validation\n      if (this.editUserForm.resetPassword) {\n        if (!this.editUserForm.newPassword || this.editUserForm.newPassword.length < 6) {\n          errors.newPassword = 'New password must be at least 6 characters long';\n        }\n\n        if (this.editUserForm.newPassword !== this.editUserForm.confirmPassword) {\n          errors.confirmPassword = 'Passwords do not match';\n        }\n      }\n\n      this.editFormErrors = errors;\n      return Object.keys(errors).length === 0;\n    },\n\n    getFullAddress(user) {\n      const parts = [];\n      if (user.house_number) parts.push(user.house_number);\n      if (user.street) parts.push(user.street);\n      if (user.subdivision) parts.push(user.subdivision);\n      if (user.barangay) parts.push(user.barangay);\n      if (user.city_municipality) parts.push(user.city_municipality);\n      if (user.province) parts.push(user.province);\n      return parts.join(', ') || 'No address provided';\n    },\n\n    async submitAddUser() {\n      try {\n        this.addUserLoading = true;\n\n        // Validate form\n        if (!this.validateAddUserForm()) {\n          this.showToast('error', 'Please fix the validation errors before submitting.');\n          return;\n        }\n\n        // Prepare user data\n        const userData = { ...this.addUserForm };\n\n        // Convert numeric fields\n        if (userData.years_of_residency) {\n          userData.years_of_residency = parseInt(userData.years_of_residency);\n        }\n        if (userData.months_of_residency) {\n          userData.months_of_residency = parseInt(userData.months_of_residency);\n        }\n        if (userData.civil_status_id) {\n          userData.civil_status_id = parseInt(userData.civil_status_id);\n        }\n\n        console.log('Creating user with data:', userData);\n\n        const response = await userManagementService.createUser(userData);\n\n        if (response.success) {\n          this.showToast('success', 'User created successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('addUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Reset form and reload data\n          this.resetAddUserForm();\n          await this.loadUsers();\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to create user');\n        }\n      } catch (error) {\n        console.error('Failed to create user:', error);\n\n        // Handle validation errors from server\n        if (error.response?.data?.details) {\n          const serverErrors = {};\n          error.response.data.details.forEach(detail => {\n            if (detail.path) {\n              serverErrors[detail.path] = detail.msg;\n            }\n          });\n          this.formErrors = { ...this.formErrors, ...serverErrors };\n        }\n\n        const errorMessage = error.response?.data?.message || error.message || 'Failed to create user';\n        this.showToast('error', errorMessage);\n      } finally {\n        this.addUserLoading = false;\n      }\n    },\n\n    editUser(user) {\n      this.editUserForm = {\n        id: user.id,\n        username: user.username,\n        email: user.email || '',\n        first_name: user.first_name || '',\n        middle_name: user.middle_name || '',\n        last_name: user.last_name || '',\n        suffix: user.suffix || '',\n        role: user.type,\n        status: user.status,\n        phone_number: user.phone_number || '',\n        // Admin specific fields\n        position: user.position || '',\n        department: user.department || '',\n        employee_id: user.employee_id || '',\n        hire_date: user.hire_date ? user.hire_date.split('T')[0] : '',\n        // Client specific fields\n        birth_date: user.birth_date ? user.birth_date.split('T')[0] : '',\n        gender: user.gender || '',\n        civil_status_id: user.civil_status_id || 1,\n        nationality: user.nationality || 'Filipino',\n        house_number: user.house_number || '',\n        street: user.street || '',\n        subdivision: user.subdivision || '',\n        barangay: user.barangay || '',\n        city_municipality: user.city_municipality || '',\n        province: user.province || '',\n        postal_code: user.postal_code || '',\n        years_of_residency: user.years_of_residency || null,\n        months_of_residency: user.months_of_residency || null,\n        // Password reset fields\n        resetPassword: false,\n        newPassword: '',\n        confirmPassword: ''\n      };\n\n      this.editFormErrors = {};\n\n      try {\n        const modalElement = document.getElementById('editUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing edit user modal:', error);\n        this.showToast('error', 'Failed to open edit user modal');\n      }\n    },\n\n    async submitEditUser() {\n      try {\n        this.editUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.editUserForm, true);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n\n        const response = await userManagementService.updateUser(this.editUserForm.id, this.editUserForm);\n\n        if (response.success) {\n          this.$toast?.success?.('User updated successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('editUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Update local data\n          const userIndex = this.users.findIndex(u => u.id === this.editUserForm.id);\n          if (userIndex > -1) {\n            this.users[userIndex] = { ...this.users[userIndex], ...this.editUserForm };\n            this.filterUsers();\n          }\n\n          // Reload stats\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to update user');\n        }\n      } catch (error) {\n        console.error('Failed to update user:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user');\n      } finally {\n        this.editUserLoading = false;\n      }\n    },\n\n    async viewUser(user) {\n      try {\n        const response = await userManagementService.getUser(user.id);\n\n        if (response.success) {\n          this.viewUserData = response.data;\n          try {\n            const modalElement = document.getElementById('viewUserModal');\n            if (modalElement) {\n              const modal = new Modal(modalElement);\n              modal.show();\n            }\n          } catch (error) {\n            console.error('Error showing view user modal:', error);\n            this.$toast?.error?.('Failed to open user details modal');\n          }\n        } else {\n          throw new Error(response.message || 'Failed to load user details');\n        }\n      } catch (error) {\n        console.error('Failed to load user details:', error);\n        this.$toast?.error?.(error.message || 'Failed to load user details');\n      }\n    },\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* User avatar styles */\n.user-avatar {\n  width: 40px;\n  height: 40px;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-placeholder {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  border-radius: 50%;\n}\n\n.user-avatar-large {\n  width: 80px;\n  height: 80px;\n}\n\n.avatar-placeholder-large {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 24px;\n  border-radius: 50%;\n}\n\n/* Modal styles */\n.modal-lg {\n  max-width: 800px;\n}\n\n/* Form styles */\n.form-label {\n  font-weight: 600;\n  color: #495057;\n}\n\n.form-control:focus,\n.form-select:focus {\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n/* Loading states */\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Table improvements */\n.table th {\n  border-top: none;\n  font-weight: 600;\n  color: #5a5c69;\n  font-size: 0.875rem;\n}\n\n.table td {\n  vertical-align: middle;\n  font-size: 0.875rem;\n}\n\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 123, 255, 0.05);\n}\n\n/* Button group improvements */\n.btn-group-sm .btn {\n  padding: 0.375rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n/* Search and filter improvements */\n.form-select-sm {\n  font-size: 0.875rem;\n  padding: 0.375rem 0.75rem;\n}\n\n/* Pagination improvements */\n.pagination-sm .page-link {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n}\n\n/* Badge improvements */\n.badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 0.5rem;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .table-responsive {\n    font-size: 0.8rem;\n  }\n\n  .btn-group-sm .btn {\n    padding: 0.25rem 0.375rem;\n    font-size: 0.7rem;\n  }\n\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n  }\n\n  .avatar-placeholder {\n    width: 32px;\n    height: 32px;\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    gap: 0.5rem !important;\n  }\n\n  .btn-group {\n    flex-direction: column;\n  }\n\n  .btn-group .btn {\n    border-radius: 0.375rem !important;\n    margin-bottom: 0.25rem;\n  }\n}\n</style>\n"], "mappings": ";;;;;AAyjCA,OAAOA,WAAU,MAAO,mBAAmB;AAC3C,OAAOC,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,gBAAe,MAAO,6BAA6B;AAC1D,OAAOC,qBAAoB,MAAO,kCAAkC;AACpE,SAASC,KAAI,QAAS,WAAW;AAEjC,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVN,WAAW;IACXC;EACF,CAAC;EAEDM,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACf;MACAC,KAAK,EAAE,EAAE;MACTC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,MAAM,EAAE;MACV,CAAC;MAED;MACAC,YAAY,EAAE,IAAI;MAClBC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,KAAK;MAEtB;MACAC,UAAU,EAAE,CAAC,CAAC;MACdC,cAAc,EAAE,CAAC,CAAC;MAElB;MACAC,WAAW,EAAE;QACXC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE,EAAE;QAChB;QACAC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACb;QACAC,UAAU,EAAE,EAAE;QACdC,MAAM,EAAE,EAAE;QACVC,eAAe,EAAE,CAAC;QAClBC,WAAW,EAAE,UAAU;QACvBC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,EAAE;QACVC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE;MACvB,CAAC;MAED;MACAC,YAAY,EAAE;QACZC,EAAE,EAAE,IAAI;QACR3B,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTE,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRqB,MAAM,EAAE,EAAE;QACVpB,YAAY,EAAE,EAAE;QAChB;QACAC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACb;QACAC,UAAU,EAAE,EAAE;QACdC,MAAM,EAAE,EAAE;QACVC,eAAe,EAAE,CAAC;QAClBC,WAAW,EAAE,UAAU;QACvBC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,EAAE;QACVC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE,IAAI;QACzB;QACAI,aAAa,EAAE,KAAK;QACpBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC;MAED;MACAC,aAAa,EAAE,CACb;QAAEC,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAO,CAAC,EAChC;QAAED,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAS,EACpC;MAEDC,aAAa,EAAE,CACb;QAAEF,KAAK,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAS,CAAC,EACpC;QAAED,KAAK,EAAE,UAAU;QAAEC,KAAK,EAAE;MAAW,CAAC,EACxC;QAAED,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAY,CAAC,EAC1C;QAAED,KAAK,EAAE,sBAAsB;QAAEC,KAAK,EAAE;MAAuB;IAEnE,CAAC;EACH,CAAC;EAEDE,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;MAC7B,IAAIA,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,OAAO;MACjD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,sBAAsB,CAAC,EAAE,OAAO,UAAU;MAC5D,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,OAAO,WAAW;IACpB,CAAC;IAEDC,cAAcA,CAAA,EAAG;MACf,MAAMC,KAAI,GAAI,CAAC,IAAI,CAACxD,WAAU,GAAI,CAAC,IAAI,IAAI,CAACC,YAAY;MACxD,MAAMwD,GAAE,GAAID,KAAI,GAAI,IAAI,CAACvD,YAAY;MACrC,OAAO,IAAI,CAACL,aAAa,CAAC8D,KAAK,CAACF,KAAK,EAAEC,GAAG,CAAC;IAC7C,CAAC;IAEDE,UAAUA,CAAA,EAAG;MACX,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACjE,aAAa,CAACkE,MAAK,GAAI,IAAI,CAAC7D,YAAY,CAAC;IACjE,CAAC;IAED8D,YAAYA,CAAA,EAAG;MACb,MAAMC,KAAI,GAAI,EAAE;MAChB,MAAMR,KAAI,GAAII,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjE,WAAU,GAAI,CAAC,CAAC;MAC/C,MAAMyD,GAAE,GAAIG,IAAI,CAACM,GAAG,CAAC,IAAI,CAACP,UAAU,EAAE,IAAI,CAAC3D,WAAU,GAAI,CAAC,CAAC;MAE3D,KAAK,IAAImE,CAAA,GAAIX,KAAK,EAAEW,CAAA,IAAKV,GAAG,EAAEU,CAAC,EAAE,EAAE;QACjCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;MACf;MACA,OAAOH,KAAK;IACd;EACF,CAAC;EAED,MAAMK,OAAOA,CAAA,EAAG;IACd;IACA,IAAI,CAACpF,gBAAgB,CAACqF,UAAU,CAAC,CAAC,EAAE;MAClC,IAAI,CAACC,OAAO,CAACH,IAAI,CAAC,cAAc,CAAC;MACjC;IACF;;IAEA;IACA,IAAI,CAACI,YAAY,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACC,UAAS,GAAI;MAAEtF;IAAM,CAAC;;IAE3B;IACA,MAAM,IAAI,CAACuF,gBAAgB,CAAC,CAAC;IAC7B,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC;IAC1B,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC;EACxB,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,YAAY,CAAC;IACzD;EACF,CAAC;EAEDG,OAAO,EAAE;IACP;IACAT,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC/E,QAAO,GAAIsF,MAAM,CAACG,UAAS,IAAK,GAAG;;MAExC;MACA,IAAI,CAAC,IAAI,CAACzF,QAAQ,EAAE;QAClB,MAAM0F,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAC9F,gBAAe,GAAI4F,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;MAC3D,OAAO;QACL,IAAI,CAAC5F,gBAAe,GAAI,IAAI,EAAE;MAChC;;MAEA;MACA,IAAI,CAACuF,YAAW,GAAI,MAAM;QACxB,MAAMU,SAAQ,GAAI,IAAI,CAAC/F,QAAQ;QAC/B,IAAI,CAACA,QAAO,GAAIsF,MAAM,CAACG,UAAS,IAAK,GAAG;QAExC,IAAI,IAAI,CAACzF,QAAO,IAAK,CAAC+F,SAAS,EAAE;UAC/B,IAAI,CAACjG,gBAAe,GAAI,IAAI,EAAE;QAChC,OAAO,IAAI,CAAC,IAAI,CAACE,QAAO,IAAK+F,SAAS,EAAE;UACtC;UACA,MAAML,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;UAC3D,IAAI,CAAC9F,gBAAe,GAAI4F,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;QAC3D;MACF,CAAC;MACDJ,MAAM,CAACU,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACX,YAAY,CAAC;IACtD,CAAC;IAED;IACAY,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACnG,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;MAC9C6F,YAAY,CAACO,OAAO,CAAC,uBAAuB,EAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAACrG,gBAAgB,CAAC,CAAC;IACtF,CAAC;IAED;IACAsG,gBAAgBA,CAACC,IAAI,EAAE;MACrB,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,cAAc;QACvB,UAAU,EAAE,iBAAiB;QAC7B,SAAS,EAAE,gBAAgB;QAC3B,UAAU,EAAE,iBAAiB;QAC7B,UAAU,EAAE,sBAAsB;QAClC,SAAS,EAAE;MACb,CAAC;;MAED;MACA,IAAI,IAAI,CAACtG,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;MAEA,IAAIwG,MAAM,CAACD,IAAI,CAAC,EAAE;QAChB,IAAI,CAACvB,OAAO,CAACH,IAAI,CAAC2B,MAAM,CAACD,IAAI,CAAC,CAAC;MACjC;IACF,CAAC;IAED;IACAE,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACxG,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED;IACAyG,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAIA,MAAK,KAAM,SAAS,EAAE;QACxB,IAAI,CAAC3B,OAAO,CAACH,IAAI,CAAC,gBAAgB,CAAC;MACrC,OAAO,IAAI8B,MAAK,KAAM,UAAU,EAAE;QAChC,IAAI,CAAC3B,OAAO,CAACH,IAAI,CAAC,iBAAiB,CAAC;MACtC;MACA,IAAI,CAAC5E,gBAAe,GAAI,KAAK;IAC/B,CAAC;IAED;IACA2G,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC1G,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;IACF,CAAC;IAED;IACA6G,YAAYA,CAAA,EAAG;MACbnH,gBAAgB,CAACoH,MAAM,CAAC,CAAC;MACzB,IAAI,CAAC9B,OAAO,CAACH,IAAI,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;IACA,MAAMM,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAM4B,QAAO,GAAI,MAAMrH,gBAAgB,CAACsH,UAAU,CAAC,CAAC;QACpD,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC9G,SAAQ,GAAI4G,QAAQ,CAAChH,IAAI;QAChC;MACF,EAAE,OAAOmH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAAC/G,SAAQ,GAAIT,gBAAgB,CAAC0H,YAAY,CAAC,CAAC;MAClD;IACF,CAAC;IAED;IACA,MAAMhC,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,MAAM2B,QAAO,GAAI,MAAMpH,qBAAqB,CAAC0H,YAAY,CAAC,CAAC;QAE3D,IAAIN,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACrG,SAAQ,GAAImG,QAAQ,CAAChH,IAAI;QAChC,OAAO;UACL,MAAM,IAAIuH,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,gCAAgC,CAAC;QACvE;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;QACA,IAAI,CAACM,cAAc,CAAC,CAAC;MACvB;IACF,CAAC;IAED;IACA,MAAMnC,SAASA,CAAA,EAAG;MAChB,IAAI,CAAC1E,OAAM,GAAI,IAAI;MACnB,IAAI;QACF,MAAM8G,MAAK,GAAI;UACbC,IAAI,EAAE,IAAI,CAACjH,WAAW;UACtBkH,KAAK,EAAE,EAAE;UAAE;UACXC,MAAM,EAAE,IAAI,CAACtH,WAAU,IAAKuH,SAAS;UACrC/F,IAAI,EAAE,IAAI,CAACtB,UAAS,IAAKqH,SAAS;UAClCC,SAAS,EAAE,IAAI,CAACvH,YAAW,KAAM,QAAO,GAAI,IAAG,GACpC,IAAI,CAACA,YAAW,KAAM,UAAS,GAAI,KAAI,GAAIsH;QACxD,CAAC;QAED,MAAMd,QAAO,GAAI,MAAMpH,qBAAqB,CAACoI,QAAQ,CAACN,MAAM,CAAC;QAE7D,IAAIV,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,IAAI,CAAC7G,KAAI,GAAI2G,QAAQ,CAAChH,IAAI,CAACK,KAAK,CAAC4H,GAAG,CAACC,IAAG,IACtCtI,qBAAqB,CAACuI,cAAc,CAACD,IAAI,CAC3C,CAAC;UAED,IAAI,CAAC5H,aAAY,GAAI,CAAC,GAAG,IAAI,CAACD,KAAK,CAAC;UACpC,IAAI,CAACoH,cAAc,CAAC,CAAC;QACvB,OAAO;UACL,MAAM,IAAIF,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,sBAAsB,CAAC;QAC7D;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,sBAAsB,CAAC;;QAE7D;QACA,IAAI,CAACnH,KAAI,GAAI,CACX;UACE8C,EAAE,EAAE,CAAC;UACL3B,QAAQ,EAAE,YAAY;UACtB6G,SAAS,EAAE,sBAAsB;UACjC5G,KAAK,EAAE,uBAAuB;UAC9B6G,IAAI,EAAE,OAAO;UACblF,MAAM,EAAE,QAAQ;UAChBmF,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACrC,CAAC,EACD;UACEtF,EAAE,EAAE,CAAC;UACL3B,QAAQ,EAAE,SAAS;UACnB6G,SAAS,EAAE,WAAW;UACtB5G,KAAK,EAAE,kBAAkB;UACzB6G,IAAI,EAAE,QAAQ;UACdlF,MAAM,EAAE,QAAQ;UAChBmF,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCC,UAAU,EAAE;QACd,EACD;QAED,IAAI,CAACpI,aAAY,GAAI,CAAC,GAAG,IAAI,CAACD,KAAK,CAAC;QACpC,IAAI,CAACoH,cAAc,CAAC,CAAC;MACvB,UAAU;QACR,IAAI,CAAC7G,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA6G,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC5G,SAAQ,GAAI;QACfC,KAAK,EAAE,IAAI,CAACT,KAAK,CAACmE,MAAM;QACxBzD,MAAM,EAAE,IAAI,CAACV,KAAK,CAACsI,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACxF,MAAK,KAAM,QAAQ,CAAC,CAACoB,MAAM;QAC5DxD,OAAO,EAAE,IAAI,CAACX,KAAK,CAACsI,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACxF,MAAK,KAAM,SAAS,CAAC,CAACoB,MAAM;QAC9DvD,MAAM,EAAE,IAAI,CAACZ,KAAK,CAACsI,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACN,IAAG,KAAM,OAAO,CAAC,CAAC9D;MACrD,CAAC;IACH,CAAC;IAED;IACAqE,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACC,WAAW,CAAC,CAAC;IACpB,CAAC;IAED;IACAA,WAAWA,CAAA,EAAG;MACZ,IAAIC,QAAO,GAAI,CAAC,GAAG,IAAI,CAAC1I,KAAK,CAAC;;MAE9B;MACA,IAAI,IAAI,CAACE,WAAW,EAAE;QACpB,MAAMyI,KAAI,GAAI,IAAI,CAACzI,WAAW,CAAC0I,WAAW,CAAC,CAAC;QAC5CF,QAAO,GAAIA,QAAQ,CAACJ,MAAM,CAACT,IAAG,IAC5BA,IAAI,CAACG,SAAS,CAACY,WAAW,CAAC,CAAC,CAACjF,QAAQ,CAACgF,KAAK,KAC3Cd,IAAI,CAACzG,KAAK,CAACwH,WAAW,CAAC,CAAC,CAACjF,QAAQ,CAACgF,KAAK,KACvCd,IAAI,CAAC1G,QAAQ,CAACyH,WAAW,CAAC,CAAC,CAACjF,QAAQ,CAACgF,KAAK,CAC5C,CAAC;MACH;;MAEA;MACA,IAAI,IAAI,CAACxI,YAAY,EAAE;QACrBuI,QAAO,GAAIA,QAAQ,CAACJ,MAAM,CAACT,IAAG,IAAKA,IAAI,CAAC9E,MAAK,KAAM,IAAI,CAAC5C,YAAY,CAAC;MACvE;;MAEA;MACA,IAAI,IAAI,CAACC,UAAU,EAAE;QACnBsI,QAAO,GAAIA,QAAQ,CAACJ,MAAM,CAACT,IAAG,IAAKA,IAAI,CAACI,IAAG,KAAM,IAAI,CAAC7H,UAAU,CAAC;MACnE;MAEA,IAAI,CAACH,aAAY,GAAIyI,QAAQ;MAC7B,IAAI,CAACrI,WAAU,GAAI,CAAC,EAAE;IACxB,CAAC;IAED;IACAwI,UAAUA,CAACvB,IAAI,EAAE;MACf,IAAIA,IAAG,IAAK,KAAKA,IAAG,IAAK,IAAI,CAACtD,UAAU,EAAE;QACxC,IAAI,CAAC3D,WAAU,GAAIiH,IAAI;MACzB;IACF,CAAC;IAED;IACAwB,WAAWA,CAACC,QAAQ,EAAE;MACpB,IAAI,CAACA,QAAQ,EAAE,OAAO,GAAG;MACzB,OAAOA,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACpB,GAAG,CAACnI,IAAG,IAAKA,IAAI,CAACwJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAACpF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED;IACAqF,mBAAmBA,CAACrG,MAAM,EAAE;MAC1B,MAAMsG,OAAM,GAAI;QACd,QAAQ,EAAE,YAAY;QACtB,UAAU,EAAE,cAAc;QAC1B,SAAS,EAAE,YAAY;QACvB,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,OAAO,CAACtG,MAAM,KAAK,cAAc;IAC1C,CAAC;IAED;IACAuG,YAAYA,CAACvG,MAAM,EAAE;MACnB,OAAOA,MAAM,CAACkG,MAAM,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,IAAIpG,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;IACAwF,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAG,GAAI,IAAItB,IAAI,CAACqB,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC;IAED;;IAGA,MAAMC,gBAAgBA,CAACjC,IAAI,EAAE;MAC3B,IAAI;QACF,MAAMkC,SAAQ,GAAIlC,IAAI,CAAC9E,MAAK,KAAM,QAAO,GAAI,WAAU,GAAI,QAAQ;QACnE,MAAMiH,MAAK,GAAI,4BAA4B,IAAI,CAACjK,SAAS,EAAEuB,UAAS,IAAK,OAAO,EAAE;QAElF,MAAMqF,QAAO,GAAI,MAAMpH,qBAAqB,CAAC0K,gBAAgB,CAACpC,IAAI,CAAC/E,EAAE,EAAEiH,SAAS,EAAEC,MAAM,CAAC;QAEzF,IAAIrD,QAAQ,CAACE,OAAO,EAAE;UACpB;UACAgB,IAAI,CAAC9E,MAAK,GAAIgH,SAAS;UACvB,IAAI,CAAC3C,cAAc,CAAC,CAAC;UAErB,IAAI,CAACW,MAAM,EAAElB,OAAO,GAAG,QAAQgB,IAAI,CAACG,SAAS,aAAa+B,SAAQ,KAAM,QAAO,GAAI,WAAU,GAAI,WAAW,GAAG,CAAC;QAClH,OAAO;UACL,MAAM,IAAI7C,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,8BAA8B,CAAC;QACrE;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,iDAAiD,CAAC;MAC1F;IACF,CAAC;IAED,MAAM+C,UAAUA,CAACrC,IAAI,EAAE;MACrB,IAAI,CAACsC,OAAO,CAAC,yCAAyCtC,IAAI,CAACG,SAAS,kCAAkC,CAAC,EAAE;QACvG;MACF;MAEA,IAAI;QACF,MAAMgC,MAAK,GAAI,0BAA0B,IAAI,CAACjK,SAAS,EAAEuB,UAAS,IAAK,OAAO,EAAE;QAChF,MAAMqF,QAAO,GAAI,MAAMpH,qBAAqB,CAAC2K,UAAU,CAACrC,IAAI,CAAC/E,EAAE,EAAEkH,MAAM,CAAC;QAExE,IAAIrD,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMuD,KAAI,GAAI,IAAI,CAACpK,KAAK,CAACqK,SAAS,CAAC9B,CAAA,IAAKA,CAAC,CAACzF,EAAC,KAAM+E,IAAI,CAAC/E,EAAE,CAAC;UACzD,IAAIsH,KAAI,GAAI,CAAC,CAAC,EAAE;YACd,IAAI,CAACpK,KAAK,CAACsK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;YAC3B,IAAI,CAAC3B,WAAW,CAAC,CAAC;YAClB,IAAI,CAACrB,cAAc,CAAC,CAAC;UACvB;UAEA,IAAI,CAACW,MAAM,EAAElB,OAAO,GAAG,QAAQgB,IAAI,CAACG,SAAS,oBAAoB,CAAC;QACpE,OAAO;UACL,MAAM,IAAId,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,0CAA0C,CAAC;MACnF;IACF,CAAC;IAID;IACAoD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI;QACF,MAAMC,YAAW,GAAIC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;QAC5D,IAAIF,YAAY,EAAE;UAChB,MAAMG,KAAI,GAAI,IAAIpL,KAAK,CAACiL,YAAY,CAAC;UACrCG,KAAK,CAACC,IAAI,CAAC,CAAC;QACd;MACF,EAAE,OAAO/D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAG,+BAA+B,CAAC;MACvD;IACF,CAAC;IAED0D,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACtJ,WAAU,GAAI;QACjBC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE,EAAE;QACRC,YAAY,EAAE,EAAE;QAChB;QACAC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACb;QACAC,UAAU,EAAE,EAAE;QACdC,MAAM,EAAE,EAAE;QACVC,eAAe,EAAE,CAAC;QAClBC,WAAW,EAAE,UAAU;QACvBC,YAAY,EAAE,EAAE;QAChBC,MAAM,EAAE,EAAE;QACVC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,iBAAiB,EAAE,EAAE;QACrBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE;MACvB,CAAC;MACD,IAAI,CAAC5B,UAAS,GAAI,CAAC,CAAC;IACtB,CAAC;IAED8J,uBAAuBA,CAAA,EAAG;MACxB,IAAI,IAAI,CAAC5J,WAAW,CAACQ,IAAG,KAAM,OAAO,EAAE;QACrC;QACA,IAAI,CAACR,WAAW,CAACc,UAAS,GAAI,EAAE;QAChC,IAAI,CAACd,WAAW,CAACe,MAAK,GAAI,EAAE;QAC5B,IAAI,CAACf,WAAW,CAACgB,eAAc,GAAI,CAAC;QACpC,IAAI,CAAChB,WAAW,CAACiB,WAAU,GAAI,UAAU;QACzC,IAAI,CAACjB,WAAW,CAACkB,YAAW,GAAI,EAAE;QAClC,IAAI,CAAClB,WAAW,CAACmB,MAAK,GAAI,EAAE;QAC5B,IAAI,CAACnB,WAAW,CAACoB,WAAU,GAAI,EAAE;QACjC,IAAI,CAACpB,WAAW,CAACqB,QAAO,GAAI,EAAE;QAC9B,IAAI,CAACrB,WAAW,CAACsB,iBAAgB,GAAI,EAAE;QACvC,IAAI,CAACtB,WAAW,CAACuB,QAAO,GAAI,EAAE;QAC9B,IAAI,CAACvB,WAAW,CAACwB,WAAU,GAAI,EAAE;QACjC,IAAI,CAACxB,WAAW,CAACyB,kBAAiB,GAAI,IAAI;QAC1C,IAAI,CAACzB,WAAW,CAAC0B,mBAAkB,GAAI,IAAI;MAC7C,OAAO,IAAI,IAAI,CAAC1B,WAAW,CAACQ,IAAG,KAAM,QAAQ,EAAE;QAC7C;QACA,IAAI,CAACR,WAAW,CAACU,QAAO,GAAI,EAAE;QAC9B,IAAI,CAACV,WAAW,CAACW,UAAS,GAAI,EAAE;QAChC,IAAI,CAACX,WAAW,CAACY,WAAU,GAAI,EAAE;QACjC,IAAI,CAACZ,WAAW,CAACa,SAAQ,GAAI,EAAE;MACjC;IACF,CAAC;IAEDgJ,mBAAmBA,CAAA,EAAG;MACpB,MAAMC,MAAK,GAAI,CAAC,CAAC;;MAEjB;MACA,IAAI,CAAC,IAAI,CAAC9J,WAAW,CAACC,QAAO,IAAK,IAAI,CAACD,WAAW,CAACC,QAAQ,CAACgD,MAAK,GAAI,CAAC,EAAE;QACtE6G,MAAM,CAAC7J,QAAO,GAAI,6CAA6C;MACjE;MAEA,IAAI,CAAC,IAAI,CAACD,WAAW,CAACG,QAAO,IAAK,IAAI,CAACH,WAAW,CAACG,QAAQ,CAAC8C,MAAK,GAAI,CAAC,EAAE;QACtE6G,MAAM,CAAC3J,QAAO,GAAI,6CAA6C;MACjE;MAEA,IAAI,CAAC,IAAI,CAACH,WAAW,CAACI,UAAS,IAAK,IAAI,CAACJ,WAAW,CAACI,UAAU,CAAC2J,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,CAAC,EAAE;QACjF6G,MAAM,CAAC1J,UAAS,GAAI,+CAA+C;MACrE;MAEA,IAAI,CAAC,IAAI,CAACJ,WAAW,CAACM,SAAQ,IAAK,IAAI,CAACN,WAAW,CAACM,SAAS,CAACyJ,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,CAAC,EAAE;QAC/E6G,MAAM,CAACxJ,SAAQ,GAAI,8CAA8C;MACnE;MAEA,IAAI,CAAC,IAAI,CAACN,WAAW,CAACQ,IAAI,EAAE;QAC1BsJ,MAAM,CAACtJ,IAAG,GAAI,2BAA2B;MAC3C;MAEA,IAAI,CAAC,IAAI,CAACR,WAAW,CAACS,YAAW,IAAK,IAAI,CAACT,WAAW,CAACS,YAAY,CAACsJ,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,EAAE,EAAE;QACtF6G,MAAM,CAACrJ,YAAW,GAAI,qCAAqC;MAC7D;MAEA,IAAI,IAAI,CAACT,WAAW,CAACE,KAAI,IAAK,CAAC,4BAA4B,CAAC8J,IAAI,CAAC,IAAI,CAAChK,WAAW,CAACE,KAAK,CAAC,EAAE;QACxF4J,MAAM,CAAC5J,KAAI,GAAI,sCAAsC;MACvD;;MAEA;MACA,IAAI,IAAI,CAACF,WAAW,CAACQ,IAAG,KAAM,QAAQ,EAAE;QACtC,IAAI,CAAC,IAAI,CAACR,WAAW,CAACc,UAAU,EAAE;UAChCgJ,MAAM,CAAChJ,UAAS,GAAI,oCAAoC;QAC1D;QAEA,IAAI,CAAC,IAAI,CAACd,WAAW,CAACe,MAAM,EAAE;UAC5B+I,MAAM,CAAC/I,MAAK,GAAI,gCAAgC;QAClD;QAEA,IAAI,CAAC,IAAI,CAACf,WAAW,CAACqB,QAAO,IAAK,IAAI,CAACrB,WAAW,CAACqB,QAAQ,CAAC0I,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,CAAC,EAAE;UAC7E6G,MAAM,CAACzI,QAAO,GAAI,kCAAkC;QACtD;QAEA,IAAI,CAAC,IAAI,CAACrB,WAAW,CAACsB,iBAAgB,IAAK,IAAI,CAACtB,WAAW,CAACsB,iBAAiB,CAACyI,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,CAAC,EAAE;UAC/F6G,MAAM,CAACxI,iBAAgB,GAAI,2CAA2C;QACxE;QAEA,IAAI,CAAC,IAAI,CAACtB,WAAW,CAACuB,QAAO,IAAK,IAAI,CAACvB,WAAW,CAACuB,QAAQ,CAACwI,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,CAAC,EAAE;UAC7E6G,MAAM,CAACvI,QAAO,GAAI,kCAAkC;QACtD;MACF;MAEA,IAAI,CAACzB,UAAS,GAAIgK,MAAM;MACxB,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAAC7G,MAAK,KAAM,CAAC;IACzC,CAAC;IAEDkH,oBAAoBA,CAAA,EAAG;MACrB,MAAML,MAAK,GAAI,CAAC,CAAC;;MAEjB;MACA,IAAI,CAAC,IAAI,CAACnI,YAAY,CAAC1B,QAAO,IAAK,IAAI,CAAC0B,YAAY,CAAC1B,QAAQ,CAACgD,MAAK,GAAI,CAAC,EAAE;QACxE6G,MAAM,CAAC7J,QAAO,GAAI,6CAA6C;MACjE;MAEA,IAAI,CAAC,IAAI,CAAC0B,YAAY,CAACvB,UAAS,IAAK,IAAI,CAACuB,YAAY,CAACvB,UAAU,CAAC2J,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,CAAC,EAAE;QACnF6G,MAAM,CAAC1J,UAAS,GAAI,+CAA+C;MACrE;MAEA,IAAI,CAAC,IAAI,CAACuB,YAAY,CAACrB,SAAQ,IAAK,IAAI,CAACqB,YAAY,CAACrB,SAAS,CAACyJ,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,CAAC,EAAE;QACjF6G,MAAM,CAACxJ,SAAQ,GAAI,8CAA8C;MACnE;MAEA,IAAI,CAAC,IAAI,CAACqB,YAAY,CAAClB,YAAW,IAAK,IAAI,CAACkB,YAAY,CAAClB,YAAY,CAACsJ,IAAI,CAAC,CAAC,CAAC9G,MAAK,GAAI,EAAE,EAAE;QACxF6G,MAAM,CAACrJ,YAAW,GAAI,qCAAqC;MAC7D;MAEA,IAAI,IAAI,CAACkB,YAAY,CAACzB,KAAI,IAAK,CAAC,4BAA4B,CAAC8J,IAAI,CAAC,IAAI,CAACrI,YAAY,CAACzB,KAAK,CAAC,EAAE;QAC1F4J,MAAM,CAAC5J,KAAI,GAAI,sCAAsC;MACvD;;MAEA;MACA,IAAI,IAAI,CAACyB,YAAY,CAACG,aAAa,EAAE;QACnC,IAAI,CAAC,IAAI,CAACH,YAAY,CAACI,WAAU,IAAK,IAAI,CAACJ,YAAY,CAACI,WAAW,CAACkB,MAAK,GAAI,CAAC,EAAE;UAC9E6G,MAAM,CAAC/H,WAAU,GAAI,iDAAiD;QACxE;QAEA,IAAI,IAAI,CAACJ,YAAY,CAACI,WAAU,KAAM,IAAI,CAACJ,YAAY,CAACK,eAAe,EAAE;UACvE8H,MAAM,CAAC9H,eAAc,GAAI,wBAAwB;QACnD;MACF;MAEA,IAAI,CAACjC,cAAa,GAAI+J,MAAM;MAC5B,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAAC7G,MAAK,KAAM,CAAC;IACzC,CAAC;IAEDmH,cAAcA,CAACzD,IAAI,EAAE;MACnB,MAAM0D,KAAI,GAAI,EAAE;MAChB,IAAI1D,IAAI,CAACzF,YAAY,EAAEmJ,KAAK,CAAC9G,IAAI,CAACoD,IAAI,CAACzF,YAAY,CAAC;MACpD,IAAIyF,IAAI,CAACxF,MAAM,EAAEkJ,KAAK,CAAC9G,IAAI,CAACoD,IAAI,CAACxF,MAAM,CAAC;MACxC,IAAIwF,IAAI,CAACvF,WAAW,EAAEiJ,KAAK,CAAC9G,IAAI,CAACoD,IAAI,CAACvF,WAAW,CAAC;MAClD,IAAIuF,IAAI,CAACtF,QAAQ,EAAEgJ,KAAK,CAAC9G,IAAI,CAACoD,IAAI,CAACtF,QAAQ,CAAC;MAC5C,IAAIsF,IAAI,CAACrF,iBAAiB,EAAE+I,KAAK,CAAC9G,IAAI,CAACoD,IAAI,CAACrF,iBAAiB,CAAC;MAC9D,IAAIqF,IAAI,CAACpF,QAAQ,EAAE8I,KAAK,CAAC9G,IAAI,CAACoD,IAAI,CAACpF,QAAQ,CAAC;MAC5C,OAAO8I,KAAK,CAACrC,IAAI,CAAC,IAAI,KAAK,qBAAqB;IAClD,CAAC;IAED,MAAMsC,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,IAAI,CAAC1K,cAAa,GAAI,IAAI;;QAE1B;QACA,IAAI,CAAC,IAAI,CAACiK,mBAAmB,CAAC,CAAC,EAAE;UAC/B,IAAI,CAACU,SAAS,CAAC,OAAO,EAAE,qDAAqD,CAAC;UAC9E;QACF;;QAEA;QACA,MAAMC,QAAO,GAAI;UAAE,GAAG,IAAI,CAACxK;QAAY,CAAC;;QAExC;QACA,IAAIwK,QAAQ,CAAC/I,kBAAkB,EAAE;UAC/B+I,QAAQ,CAAC/I,kBAAiB,GAAIgJ,QAAQ,CAACD,QAAQ,CAAC/I,kBAAkB,CAAC;QACrE;QACA,IAAI+I,QAAQ,CAAC9I,mBAAmB,EAAE;UAChC8I,QAAQ,CAAC9I,mBAAkB,GAAI+I,QAAQ,CAACD,QAAQ,CAAC9I,mBAAmB,CAAC;QACvE;QACA,IAAI8I,QAAQ,CAACxJ,eAAe,EAAE;UAC5BwJ,QAAQ,CAACxJ,eAAc,GAAIyJ,QAAQ,CAACD,QAAQ,CAACxJ,eAAe,CAAC;QAC/D;QAEA6E,OAAO,CAAC6E,GAAG,CAAC,0BAA0B,EAAEF,QAAQ,CAAC;QAEjD,MAAM/E,QAAO,GAAI,MAAMpH,qBAAqB,CAACsM,UAAU,CAACH,QAAQ,CAAC;QAEjE,IAAI/E,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAC4E,SAAS,CAAC,SAAS,EAAE,2BAA2B,CAAC;;UAEtD;UACA,IAAI;YACF,MAAMb,KAAI,GAAIpL,KAAK,CAACsM,WAAW,CAACpB,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,CAAC;YACxE,IAAIC,KAAK,EAAEA,KAAK,CAACmB,IAAI,CAAC,CAAC;UACzB,EAAE,OAAOjF,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC9C;;UAEA;UACA,IAAI,CAAC0D,gBAAgB,CAAC,CAAC;UACvB,MAAM,IAAI,CAACvF,SAAS,CAAC,CAAC;UACtB,MAAM,IAAI,CAACD,aAAa,CAAC,CAAC;QAC5B,OAAO;UACL,MAAM,IAAIkC,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;QAE9C;QACA,IAAIA,KAAK,CAACH,QAAQ,EAAEhH,IAAI,EAAEqM,OAAO,EAAE;UACjC,MAAMC,YAAW,GAAI,CAAC,CAAC;UACvBnF,KAAK,CAACH,QAAQ,CAAChH,IAAI,CAACqM,OAAO,CAACE,OAAO,CAACC,MAAK,IAAK;YAC5C,IAAIA,MAAM,CAAC1I,IAAI,EAAE;cACfwI,YAAY,CAACE,MAAM,CAAC1I,IAAI,IAAI0I,MAAM,CAACC,GAAG;YACxC;UACF,CAAC,CAAC;UACF,IAAI,CAACpL,UAAS,GAAI;YAAE,GAAG,IAAI,CAACA,UAAU;YAAE,GAAGiL;UAAa,CAAC;QAC3D;QAEA,MAAMI,YAAW,GAAIvF,KAAK,CAACH,QAAQ,EAAEhH,IAAI,EAAEwH,OAAM,IAAKL,KAAK,CAACK,OAAM,IAAK,uBAAuB;QAC9F,IAAI,CAACsE,SAAS,CAAC,OAAO,EAAEY,YAAY,CAAC;MACvC,UAAU;QACR,IAAI,CAACvL,cAAa,GAAI,KAAK;MAC7B;IACF,CAAC;IAEDwL,QAAQA,CAACzE,IAAI,EAAE;MACb,IAAI,CAAChF,YAAW,GAAI;QAClBC,EAAE,EAAE+E,IAAI,CAAC/E,EAAE;QACX3B,QAAQ,EAAE0G,IAAI,CAAC1G,QAAQ;QACvBC,KAAK,EAAEyG,IAAI,CAACzG,KAAI,IAAK,EAAE;QACvBE,UAAU,EAAEuG,IAAI,CAACvG,UAAS,IAAK,EAAE;QACjCC,WAAW,EAAEsG,IAAI,CAACtG,WAAU,IAAK,EAAE;QACnCC,SAAS,EAAEqG,IAAI,CAACrG,SAAQ,IAAK,EAAE;QAC/BC,MAAM,EAAEoG,IAAI,CAACpG,MAAK,IAAK,EAAE;QACzBC,IAAI,EAAEmG,IAAI,CAACI,IAAI;QACflF,MAAM,EAAE8E,IAAI,CAAC9E,MAAM;QACnBpB,YAAY,EAAEkG,IAAI,CAAClG,YAAW,IAAK,EAAE;QACrC;QACAC,QAAQ,EAAEiG,IAAI,CAACjG,QAAO,IAAK,EAAE;QAC7BC,UAAU,EAAEgG,IAAI,CAAChG,UAAS,IAAK,EAAE;QACjCC,WAAW,EAAE+F,IAAI,CAAC/F,WAAU,IAAK,EAAE;QACnCC,SAAS,EAAE8F,IAAI,CAAC9F,SAAQ,GAAI8F,IAAI,CAAC9F,SAAS,CAACiH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;QAC7D;QACAhH,UAAU,EAAE6F,IAAI,CAAC7F,UAAS,GAAI6F,IAAI,CAAC7F,UAAU,CAACgH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;QAChE/G,MAAM,EAAE4F,IAAI,CAAC5F,MAAK,IAAK,EAAE;QACzBC,eAAe,EAAE2F,IAAI,CAAC3F,eAAc,IAAK,CAAC;QAC1CC,WAAW,EAAE0F,IAAI,CAAC1F,WAAU,IAAK,UAAU;QAC3CC,YAAY,EAAEyF,IAAI,CAACzF,YAAW,IAAK,EAAE;QACrCC,MAAM,EAAEwF,IAAI,CAACxF,MAAK,IAAK,EAAE;QACzBC,WAAW,EAAEuF,IAAI,CAACvF,WAAU,IAAK,EAAE;QACnCC,QAAQ,EAAEsF,IAAI,CAACtF,QAAO,IAAK,EAAE;QAC7BC,iBAAiB,EAAEqF,IAAI,CAACrF,iBAAgB,IAAK,EAAE;QAC/CC,QAAQ,EAAEoF,IAAI,CAACpF,QAAO,IAAK,EAAE;QAC7BC,WAAW,EAAEmF,IAAI,CAACnF,WAAU,IAAK,EAAE;QACnCC,kBAAkB,EAAEkF,IAAI,CAAClF,kBAAiB,IAAK,IAAI;QACnDC,mBAAmB,EAAEiF,IAAI,CAACjF,mBAAkB,IAAK,IAAI;QACrD;QACAI,aAAa,EAAE,KAAK;QACpBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC;MAED,IAAI,CAACjC,cAAa,GAAI,CAAC,CAAC;MAExB,IAAI;QACF,MAAMwJ,YAAW,GAAIC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;QAC7D,IAAIF,YAAY,EAAE;UAChB,MAAMG,KAAI,GAAI,IAAIpL,KAAK,CAACiL,YAAY,CAAC;UACrCG,KAAK,CAACC,IAAI,CAAC,CAAC;QACd;MACF,EAAE,OAAO/D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC2E,SAAS,CAAC,OAAO,EAAE,gCAAgC,CAAC;MAC3D;IACF,CAAC;IAED,MAAMc,cAAcA,CAAA,EAAG;MACrB,IAAI;QACF,IAAI,CAACxL,eAAc,GAAI,IAAI;;QAE3B;QACA,MAAMyL,UAAS,GAAIjN,qBAAqB,CAACkN,gBAAgB,CAAC,IAAI,CAAC5J,YAAY,EAAE,IAAI,CAAC;QAClF,IAAI,CAAC2J,UAAU,CAACE,OAAO,EAAE;UACvB,IAAI,CAAC3E,MAAM,EAAEjB,KAAK,GAAG0F,UAAU,CAACxB,MAAM,CAAC9B,IAAI,CAAC,IAAI,CAAC,CAAC;UAClD;QACF;QAEA,MAAMvC,QAAO,GAAI,MAAMpH,qBAAqB,CAACoN,UAAU,CAAC,IAAI,CAAC9J,YAAY,CAACC,EAAE,EAAE,IAAI,CAACD,YAAY,CAAC;QAEhG,IAAI8D,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACkB,MAAM,EAAElB,OAAO,GAAG,2BAA2B,CAAC;;UAEnD;UACA,IAAI;YACF,MAAM+D,KAAI,GAAIpL,KAAK,CAACsM,WAAW,CAACpB,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC,CAAC;YACzE,IAAIC,KAAK,EAAEA,KAAK,CAACmB,IAAI,CAAC,CAAC;UACzB,EAAE,OAAOjF,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC9C;;UAEA;UACA,MAAM8F,SAAQ,GAAI,IAAI,CAAC5M,KAAK,CAACqK,SAAS,CAAC9B,CAAA,IAAKA,CAAC,CAACzF,EAAC,KAAM,IAAI,CAACD,YAAY,CAACC,EAAE,CAAC;UAC1E,IAAI8J,SAAQ,GAAI,CAAC,CAAC,EAAE;YAClB,IAAI,CAAC5M,KAAK,CAAC4M,SAAS,IAAI;cAAE,GAAG,IAAI,CAAC5M,KAAK,CAAC4M,SAAS,CAAC;cAAE,GAAG,IAAI,CAAC/J;YAAa,CAAC;YAC1E,IAAI,CAAC4F,WAAW,CAAC,CAAC;UACpB;;UAEA;UACA,MAAM,IAAI,CAACzD,aAAa,CAAC,CAAC;QAC5B,OAAO;UACL,MAAM,IAAIkC,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,uBAAuB,CAAC;MAChE,UAAU;QACR,IAAI,CAACpG,eAAc,GAAI,KAAK;MAC9B;IACF,CAAC;IAED,MAAM8L,QAAQA,CAAChF,IAAI,EAAE;MACnB,IAAI;QACF,MAAMlB,QAAO,GAAI,MAAMpH,qBAAqB,CAACuN,OAAO,CAACjF,IAAI,CAAC/E,EAAE,CAAC;QAE7D,IAAI6D,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAChG,YAAW,GAAI8F,QAAQ,CAAChH,IAAI;UACjC,IAAI;YACF,MAAM8K,YAAW,GAAIC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;YAC7D,IAAIF,YAAY,EAAE;cAChB,MAAMG,KAAI,GAAI,IAAIpL,KAAK,CAACiL,YAAY,CAAC;cACrCG,KAAK,CAACC,IAAI,CAAC,CAAC;YACd;UACF,EAAE,OAAO/D,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;YACtD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAG,mCAAmC,CAAC;UAC3D;QACF,OAAO;UACL,MAAM,IAAII,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,6BAA6B,CAAC;QACpE;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,6BAA6B,CAAC;MACtE;IACF;;IAEA;IACA;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}