<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend CRUD</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        input, select { padding: 8px; margin: 5px; width: 200px; }
        button { padding: 10px 20px; margin: 5px; }
        .results { margin-top: 20px; padding: 10px; background: #f5f5f5; }
        .error { color: red; }
        .success { color: green; }
        .user-item { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Frontend CRUD Test</h1>
    
    <div class="form-group">
        <button onclick="loadUsers()">Load Users</button>
        <button onclick="createTestUser()">Create Test User</button>
        <button onclick="clearStorage()">Clear Storage</button>
    </div>
    
    <div id="users-list"></div>
    <div id="results" class="results"></div>

    <script type="module">
        // Import the userManagementService
        import userManagementService from './src/services/userManagementService.js';
        
        window.userService = userManagementService;
        
        function log(message, type = 'info') {
            const div = document.getElementById('results');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            div.innerHTML += `<p class="${className}">${message}</p>`;
            console.log(message);
        }
        
        function clearStorage() {
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminData');
            localStorage.removeItem('clientToken');
            localStorage.removeItem('clientData');
            log('Storage cleared', 'success');
        }
        
        async function loadUsers() {
            const adminToken = localStorage.getItem('adminToken');
            
            if (!adminToken) {
                log('❌ No admin token found. Please login first.', 'error');
                return;
            }
            
            log('Loading users...');
            
            try {
                const response = await window.userService.getUsers({
                    page: 1,
                    limit: 10
                });
                
                if (response.success) {
                    log('✅ Users loaded successfully!', 'success');
                    displayUsers(response.data.users);
                } else {
                    log('❌ Failed to load users: ' + response.message, 'error');
                }
            } catch (error) {
                log('❌ Error loading users: ' + error.message, 'error');
            }
        }
        
        function displayUsers(users) {
            const usersList = document.getElementById('users-list');
            usersList.innerHTML = '<h3>Users:</h3>';
            
            users.forEach(user => {
                const userDiv = document.createElement('div');
                userDiv.className = 'user-item';
                userDiv.innerHTML = `
                    <strong>${user.full_name}</strong> (${user.username}) - ${user.type} - ${user.status}
                    <br>Email: ${user.email || 'N/A'} | Phone: ${user.phone_number || 'N/A'}
                    <br>
                    <button onclick="editUser(${user.id})">Edit</button>
                    <button onclick="deleteUser(${user.id}, '${user.full_name}')">Delete</button>
                `;
                usersList.appendChild(userDiv);
            });
        }
        
        async function createTestUser() {
            const adminToken = localStorage.getItem('adminToken');
            
            if (!adminToken) {
                log('❌ No admin token found. Please login first.', 'error');
                return;
            }
            
            log('Creating test user...');
            
            const userData = {
                username: 'frontend_test_' + Date.now(),
                password: 'testpass123',
                first_name: 'Frontend',
                last_name: 'Test',
                role: 'client',
                phone_number: '09123456789',
                email: '<EMAIL>',
                birth_date: '1990-01-01',
                gender: 'male',
                civil_status_id: 1,
                nationality: 'Filipino',
                barangay: 'Test Barangay',
                city_municipality: 'Test City',
                province: 'Test Province'
            };
            
            try {
                const response = await window.userService.createUser(userData);
                
                if (response.success) {
                    log('✅ Test user created successfully!', 'success');
                    loadUsers(); // Refresh the list
                } else {
                    log('❌ Failed to create user: ' + response.message, 'error');
                }
            } catch (error) {
                log('❌ Error creating user: ' + error.message, 'error');
            }
        }
        
        async function editUser(userId) {
            const adminToken = localStorage.getItem('adminToken');
            
            if (!adminToken) {
                log('❌ No admin token found. Please login first.', 'error');
                return;
            }
            
            log(`Editing user ${userId}...`);
            
            const updateData = {
                first_name: 'Updated Frontend',
                last_name: 'Updated Test',
                email: '<EMAIL>'
            };
            
            try {
                const response = await window.userService.updateUser(userId, updateData);
                
                if (response.success) {
                    log('✅ User updated successfully!', 'success');
                    loadUsers(); // Refresh the list
                } else {
                    log('❌ Failed to update user: ' + response.message, 'error');
                }
            } catch (error) {
                log('❌ Error updating user: ' + error.message, 'error');
            }
        }
        
        async function deleteUser(userId, userName) {
            const adminToken = localStorage.getItem('adminToken');
            
            if (!adminToken) {
                log('❌ No admin token found. Please login first.', 'error');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete user "${userName}"?`)) {
                return;
            }
            
            log(`Deleting user ${userId}...`);
            
            try {
                const response = await window.userService.deleteUser(userId, 'Deleted via frontend test');
                
                if (response.success) {
                    log('✅ User deleted successfully (soft delete)!', 'success');
                    loadUsers(); // Refresh the list
                } else {
                    log('❌ Failed to delete user: ' + response.message, 'error');
                }
            } catch (error) {
                log('❌ Error deleting user: ' + error.message, 'error');
            }
        }
        
        // Make functions global
        window.loadUsers = loadUsers;
        window.createTestUser = createTestUser;
        window.clearStorage = clearStorage;
        window.editUser = editUser;
        window.deleteUser = deleteUser;
        
        // Check current storage on load
        window.onload = function() {
            const adminToken = localStorage.getItem('adminToken');
            const adminData = localStorage.getItem('adminData');
            
            if (adminToken) {
                log('✅ Admin token found in storage', 'success');
                if (adminData) {
                    try {
                        const data = JSON.parse(adminData);
                        log('Admin: ' + data.username + ' (' + data.role + ')');
                    } catch (e) {
                        log('❌ Error parsing admin data', 'error');
                    }
                }
            } else {
                log('ℹ️ No admin token found in storage. Please login first.');
            }
        };
    </script>
</body>
</html>
